/*
---------------------------------------------------------------
File name:          status_bar.dart
Author:             lgnorant-lu
Date created:       2025-07-18
Last modified:      2025-07-18
Dart Version:       3.2+
Description:        创意工坊状态栏组件
---------------------------------------------------------------
Change History:
    2025-07-18: Initial creation - 状态栏组件实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:creative_workshop/src/core/tools/index.dart';
import 'package:creative_workshop/src/core/projects/index.dart';
import 'package:creative_workshop/src/core/games/index.dart';

/// 状态栏组件
class StatusBar extends StatefulWidget {
  const StatusBar({
    super.key,
    this.height = 32,
    this.backgroundColor,
    this.showProjectInfo = true,
    this.showToolInfo = true,
    this.showGameInfo = true,
  });

  /// 状态栏高度
  final double height;

  /// 背景颜色
  final Color? backgroundColor;

  /// 是否显示项目信息
  final bool showProjectInfo;

  /// 是否显示工具信息
  final bool showToolInfo;

  /// 是否显示游戏信息
  final bool showGameInfo;

  @override
  State<StatusBar> createState() => _StatusBarState();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(DoubleProperty('height', height));
    properties.add(ColorProperty('backgroundColor', backgroundColor));
    properties.add(DiagnosticsProperty<bool>('showProjectInfo', showProjectInfo));
    properties.add(DiagnosticsProperty<bool>('showToolInfo', showToolInfo));
    properties.add(DiagnosticsProperty<bool>('showGameInfo', showGameInfo));
  }
}

class _StatusBarState extends State<StatusBar> {
  late SimpleToolManager _toolManager;
  late ProjectManager _projectManager;
  late SimpleGameManager _gameManager;

  @override
  void initState() {
    super.initState();
    _toolManager = SimpleToolManager.instance;
    _projectManager = ProjectManager.instance;
    _gameManager = SimpleGameManager.instance;

    _toolManager.addListener(_onStateChanged);
    _projectManager.addListener(_onStateChanged);
    _gameManager.addListener(_onStateChanged);
  }

  @override
  void dispose() {
    _toolManager.removeListener(_onStateChanged);
    _projectManager.removeListener(_onStateChanged);
    _gameManager.removeListener(_onStateChanged);
    super.dispose();
  }

  void _onStateChanged() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: backgroundColor,
        border: Border(
          top: BorderSide(color: Colors.grey.shade300),
        ),
      ),
      child: Row(
        children: <Widget>[
          // 左侧状态信息
          Expanded(
            child: Row(
              children: <Widget>[
                // 项目信息
                if (widget.showProjectInfo) _buildProjectInfo(),

                // 分隔符
                if (widget.showProjectInfo && widget.showToolInfo)
                  _buildDivider(),

                // 工具信息
                if (widget.showToolInfo) _buildToolInfo(),

                // 分隔符
                if ((widget.showProjectInfo || widget.showToolInfo) &&
                    widget.showGameInfo)
                  _buildDivider(),

                // 插件信息
                if (widget.showGameInfo) _buildPluginInfo(),
              ],
            ),
          ),

          // 右侧系统信息
          _buildSystemInfo(),
        ],
      ),
    );
  }

  Widget _buildProjectInfo() {
    final currentProject = _projectManager.currentProject;

    if (currentProject == null) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Text(
          '没有打开的项目',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(
            _getProjectTypeIcon(currentProject.type),
            size: 14,
            color: _getProjectTypeColor(currentProject.type),
          ),
          const SizedBox(width: 6),
          Text(
            currentProject.name,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(width: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
            decoration: BoxDecoration(
              color: _getProjectStatusColor(currentProject.status)
                  .withOpacity(0.1),
              border: Border.all(
                color: _getProjectStatusColor(currentProject.status),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              _getProjectStatusName(currentProject.status),
              style: TextStyle(
                fontSize: 10,
                color: _getProjectStatusColor(currentProject.status),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildToolInfo() {
    final activeTool = _toolManager.activeTool;

    if (activeTool == null) {
      return const Padding(
        padding: EdgeInsets.symmetric(horizontal: 12),
        child: Text(
          '没有选择工具',
          style: TextStyle(fontSize: 12, color: Colors.grey),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(
            _getToolIcon(activeTool),
            size: 14,
            color: Colors.blue,
          ),
          const SizedBox(width: 6),
          Text(
            _getToolName(activeTool),
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          const SizedBox(width: 6),
          Text(
            _getToolDetails(activeTool),
            style: const TextStyle(fontSize: 10, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  Widget _buildPluginInfo() {
    // 显示插件系统状态信息
    return const Padding(
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(
            Icons.extension,
            size: 14,
            color: Colors.blue,
          ),
          SizedBox(width: 6),
          Text(
            '插件系统',
            style: TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
          ),
          SizedBox(width: 6),
          Text(
            '运行中',
            style: TextStyle(fontSize: 10, color: Colors.green),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemInfo() => Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            // 内存使用情况
            _buildSystemInfoItem(
              icon: Icons.memory,
              label: '内存',
              value: '${_getMemoryUsage()}MB',
              color: Colors.orange,
            ),

            const SizedBox(width: 12),

            // 时间显示
            _buildSystemInfoItem(
              icon: Icons.access_time,
              label: '时间',
              value: _getCurrentTime(),
              color: Colors.blue,
            ),
          ],
        ),
      );

  Widget _buildSystemInfoItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) =>
      Row(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Icon(icon, size: 12, color: color),
          const SizedBox(width: 4),
          Text(
            '$label: $value',
            style: const TextStyle(fontSize: 10, color: Colors.grey),
          ),
        ],
      );

  Widget _buildDivider() => Container(
        width: 1,
        height: 16,
        margin: const EdgeInsets.symmetric(horizontal: 8),
        color: Colors.grey.shade300,
      );

  // 辅助方法
  IconData _getProjectTypeIcon(ProjectType type) {
    switch (type) {
      case ProjectType.plugin:
        return Icons.extension;
      case ProjectType.design:
        return Icons.design_services;
      case ProjectType.animation:
        return Icons.movie;
      case ProjectType.model3d:
        return Icons.view_in_ar;
      case ProjectType.mixed:
        return Icons.auto_awesome;
      case ProjectType.custom:
        return Icons.build;
    }
  }

  Color _getProjectTypeColor(ProjectType type) {
    switch (type) {
      case ProjectType.plugin:
        return Colors.blue;
      case ProjectType.design:
        return Colors.purple;
      case ProjectType.animation:
        return Colors.red;
      case ProjectType.model3d:
        return Colors.orange;
      case ProjectType.mixed:
        return Colors.teal;
      case ProjectType.custom:
        return Colors.grey;
    }
  }

  String _getProjectStatusName(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.draft:
        return '草稿';
      case ProjectStatus.inProgress:
        return '进行中';
      case ProjectStatus.completed:
        return '已完成';
      case ProjectStatus.published:
        return '已发布';
      case ProjectStatus.archived:
        return '已归档';
    }
  }

  Color _getProjectStatusColor(ProjectStatus status) {
    switch (status) {
      case ProjectStatus.draft:
        return Colors.grey;
      case ProjectStatus.inProgress:
        return Colors.blue;
      case ProjectStatus.completed:
        return Colors.green;
      case ProjectStatus.published:
        return Colors.purple;
      case ProjectStatus.archived:
        return Colors.orange;
    }
  }

  IconData _getToolIcon(tool) {
    // Phase 5.0.6 - 转型为应用商店模式，使用通用工具图标
    return Icons.extension;
  }

  String _getToolName(tool) {
    // Phase 5.0.6 - 转型为应用商店模式，显示插件名称
    try {
      return tool?.name?.toString() ?? tool?.runtimeType.toString() ?? '未知工具';
    } catch (e) {
      return '未知工具';
    }
  }

  String _getToolDetails(tool) {
    // Phase 5.0.6 - 转型为应用商店模式，显示插件版本
    try {
      return tool?.version != null ? '版本: ${tool.version}' : '';
    } catch (e) {
      return '';
    }
  }

  int _getMemoryUsage() {
    try {
      // 在实际应用中，可以使用以下方法获取内存使用情况：
      // 1. 使用 dart:developer 的 Service.getIsolateMemoryUsage()
      // 2. 使用 dart:io 的 ProcessInfo.currentRss
      // 3. 使用第三方包如 device_info_plus

      // 这里提供一个基于时间和随机因子的模拟实现
      final now = DateTime.now();
      const baseMemory = 64; // 基础内存 64MB
      final timeVariation =
          (now.millisecondsSinceEpoch % 1000) / 10; // 0-100的时间变化
      final randomFactor = now.microsecond % 50; // 0-50的随机因子

      final totalMemory = baseMemory + timeVariation + randomFactor;
      return totalMemory.round();
    } catch (e) {
      // 如果获取失败，返回默认值
      return 128;
    }
  }

  String _getCurrentTime() {
    final now = DateTime.now();
    return '${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
  }
}
