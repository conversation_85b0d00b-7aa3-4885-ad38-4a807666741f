/*
---------------------------------------------------------------
File name:          plugin_module_interface.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        插件模块接口定义 - Phase 4.2 架构重构和模块解耦
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.2 - 模块接口定义实现;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:plugin_system/src/architecture/dependency_injection_container.dart';

/// 模块状态
enum ModuleState {
  /// 未初始化
  uninitialized,

  /// 初始化中
  initializing,

  /// 已初始化
  initialized,

  /// 启动中
  starting,

  /// 已启动
  started,

  /// 停止中
  stopping,

  /// 已停止
  stopped,

  /// 错误状态
  error,
}

/// 模块优先级
enum ModulePriority {
  /// 低优先级
  low,

  /// 普通优先级
  normal,

  /// 高优先级
  high,

  /// 关键优先级
  critical,
}

/// 模块元数据
class ModuleMetadata {
  const ModuleMetadata({
    required this.name,
    required this.version,
    required this.description,
    this.author,
    this.dependencies = const <String>[],
    this.optionalDependencies = const <String>[],
    this.provides = const <String>[],
    this.priority = ModulePriority.normal,
    this.tags = const <String>[],
    this.configuration = const <String, dynamic>{},
  });

  /// 模块名称
  final String name;

  /// 版本
  final String version;

  /// 描述
  final String description;

  /// 作者
  final String? author;

  /// 依赖模块
  final List<String> dependencies;

  /// 可选依赖
  final List<String> optionalDependencies;

  /// 提供的服务
  final List<String> provides;

  /// 优先级
  final ModulePriority priority;

  /// 标签
  final List<String> tags;

  /// 配置
  final Map<String, dynamic> configuration;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'name': name,
        'version': version,
        'description': description,
        'author': author,
        'dependencies': dependencies,
        'optionalDependencies': optionalDependencies,
        'provides': provides,
        'priority': priority.name,
        'tags': tags,
        'configuration': configuration,
      };
}

/// 模块上下文
class ModuleContext {
  ModuleContext({
    required this.container,
    required this.moduleManager,
    this.configuration = const <String, dynamic>{},
  });

  /// 依赖注入容器
  final DIContainer container;

  /// 模块管理器
  final PluginModuleManager moduleManager;

  /// 配置
  final Map<String, dynamic> configuration;

  /// 获取配置值
  T? getConfig<T>(String key, [T? defaultValue]) {
    final value = configuration[key];
    if (value is T) {
      return value;
    }
    return defaultValue;
  }

  /// 设置配置值
  void setConfig(String key, value) {
    configuration[key] = value;
  }
}

/// 插件模块接口
///
/// 所有插件模块都应该实现此接口
abstract class IPluginModule {
  /// 模块元数据
  ModuleMetadata get metadata;

  /// 当前状态
  ModuleState get state;

  /// 状态变化流
  Stream<ModuleState> get stateStream;

  /// 初始化模块
  Future<void> initialize(ModuleContext context);

  /// 启动模块
  Future<void> start();

  /// 停止模块
  Future<void> stop();

  /// 销毁模块
  Future<void> dispose();

  /// 注册依赖
  void registerDependencies(DIContainer container);

  /// 配置模块
  void configure(Map<String, dynamic> configuration);

  /// 健康检查
  Future<bool> healthCheck();

  /// 获取模块信息
  Map<String, dynamic> getModuleInfo();
}

/// 模块基类
///
/// 提供模块的基础实现
abstract class BasePluginModule implements IPluginModule {
  BasePluginModule(this.metadata);

  @override
  final ModuleMetadata metadata;

  ModuleState _state = ModuleState.uninitialized;
  final StreamController<ModuleState> _stateController =
      StreamController<ModuleState>.broadcast();

  ModuleContext? _context;
  final Map<String, dynamic> _configuration = <String, dynamic>{};

  @override
  ModuleState get state => _state;

  @override
  Stream<ModuleState> get stateStream => _stateController.stream;

  /// 获取模块上下文
  ModuleContext get context {
    if (_context == null) {
      throw StateError('Module not initialized');
    }
    return _context!;
  }

  @override
  Future<void> initialize(ModuleContext context) async {
    if (_state != ModuleState.uninitialized) {
      throw StateError('Module already initialized');
    }

    _setState(ModuleState.initializing);
    _context = context;
    _configuration.addAll(context.configuration);

    try {
      await onInitialize();
      _setState(ModuleState.initialized);
    } catch (e) {
      _setState(ModuleState.error);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_state != ModuleState.initialized) {
      throw StateError('Module not initialized or already started');
    }

    _setState(ModuleState.starting);

    try {
      await onStart();
      _setState(ModuleState.started);
    } catch (e) {
      _setState(ModuleState.error);
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_state != ModuleState.started) {
      throw StateError('Module not started');
    }

    _setState(ModuleState.stopping);

    try {
      await onStop();
      _setState(ModuleState.stopped);
    } catch (e) {
      _setState(ModuleState.error);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      if (_state == ModuleState.started) {
        await stop();
      }
      await onDispose();
    } finally {
      await _stateController.close();
      _context = null;
      _configuration.clear();
    }
  }

  @override
  void configure(Map<String, dynamic> configuration) {
    _configuration.addAll(configuration);
    onConfigure(configuration);
  }

  @override
  Future<bool> healthCheck() async {
    try {
      return await onHealthCheck();
    } catch (e) {
      return false;
    }
  }

  @override
  Map<String, dynamic> getModuleInfo() => <String, dynamic>{
        'metadata': metadata.toJson(),
        'state': state.name,
        'configuration': Map<String, dynamic>.from(_configuration),
        'healthStatus': 'unknown',
        ...onGetModuleInfo(),
      };

  /// 设置状态
  void _setState(ModuleState newState) {
    if (_state != newState) {
      _state = newState;
      _stateController.add(newState);
    }
  }

  /// 获取配置值
  T? getConfig<T>(String key, [T? defaultValue]) {
    final value = _configuration[key];
    if (value is T) {
      return value;
    }
    return defaultValue;
  }

  /// 设置配置值
  void setConfig(String key, value) {
    _configuration[key] = value;
  }

  // 子类需要实现的方法

  /// 初始化时调用
  Future<void> onInitialize();

  /// 启动时调用
  Future<void> onStart();

  /// 停止时调用
  Future<void> onStop();

  /// 销毁时调用
  Future<void> onDispose();

  /// 配置时调用
  void onConfigure(Map<String, dynamic> configuration);

  /// 健康检查时调用
  Future<bool> onHealthCheck();

  /// 获取模块信息时调用
  Map<String, dynamic> onGetModuleInfo();
}

/// 模块管理器接口
abstract class PluginModuleManager {
  /// 注册模块
  void registerModule(IPluginModule module);

  /// 注销模块
  void unregisterModule(String moduleName);

  /// 获取模块
  IPluginModule? getModule(String moduleName);

  /// 获取所有模块
  List<IPluginModule> getAllModules();

  /// 初始化所有模块
  Future<void> initializeModules();

  /// 启动所有模块
  Future<void> startModules();

  /// 停止所有模块
  Future<void> stopModules();

  /// 获取模块依赖图
  Map<String, List<String>> getDependencyGraph();

  /// 解析模块启动顺序
  List<String> resolveStartupOrder();
}
