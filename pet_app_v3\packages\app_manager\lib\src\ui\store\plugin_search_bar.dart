/*
---------------------------------------------------------------
File name:          plugin_search_bar.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        插件搜索栏组件 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 从Creative Workshop迁移搜索栏组件;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:flutter/material.dart';

/// 插件搜索栏组件
/// 
/// 提供实时搜索功能，支持防抖处理
class PluginSearchBar extends StatefulWidget {
  /// 搜索变化回调
  final Function(String)? onSearchChanged;
  
  /// 提示文本
  final String? hintText;
  
  /// 初始搜索文本
  final String? initialText;
  
  /// 是否自动聚焦
  final bool autofocus;
  
  /// 防抖延迟（毫秒）
  final int debounceDelay;

  const PluginSearchBar({
    super.key,
    this.onSearchChanged,
    this.hintText = '搜索插件...',
    this.initialText,
    this.autofocus = false,
    this.debounceDelay = 500,
  });

  @override
  State<PluginSearchBar> createState() => _PluginSearchBarState();
}

class _PluginSearchBarState extends State<PluginSearchBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  Timer? _debounceTimer;
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();
    
    // 监听文本变化
    _controller.addListener(_onTextChanged);
    
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// 文本变化处理
  void _onTextChanged() {
    final text = _controller.text;
    
    // 取消之前的防抖计时器
    _debounceTimer?.cancel();
    
    // 设置新的防抖计时器
    _debounceTimer = Timer(
      Duration(milliseconds: widget.debounceDelay),
      () {
        if (mounted) {
          setState(() {
            _isSearching = false;
          });
          widget.onSearchChanged?.call(text);
        }
      },
    );
    
    // 显示搜索状态
    if (!_isSearching && text.isNotEmpty) {
      setState(() {
        _isSearching = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          prefixIcon: Icon(
            Icons.search,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          suffixIcon: _buildSuffixIcon(),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        style: Theme.of(context).textTheme.bodyMedium,
        textInputAction: TextInputAction.search,
        onSubmitted: (value) {
          _debounceTimer?.cancel();
          setState(() {
            _isSearching = false;
          });
          widget.onSearchChanged?.call(value);
        },
      ),
    );
  }

  /// 构建后缀图标
  Widget? _buildSuffixIcon() {
    if (_isSearching) {
      return Container(
        padding: const EdgeInsets.all(12),
        child: SizedBox(
          width: 16,
          height: 16,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(
              Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      );
    }

    if (_controller.text.isNotEmpty) {
      return IconButton(
        icon: Icon(
          Icons.clear,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        onPressed: _clearSearch,
        tooltip: '清除搜索',
      );
    }

    return null;
  }

  /// 清除搜索
  void _clearSearch() {
    _controller.clear();
    _focusNode.unfocus();
    
    // 立即触发搜索变化
    _debounceTimer?.cancel();
    setState(() {
      _isSearching = false;
    });
    widget.onSearchChanged?.call('');
  }
}
