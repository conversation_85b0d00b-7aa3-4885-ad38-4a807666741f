/*
---------------------------------------------------------------
File name:          app_store_service.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        App Manager应用商店服务 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 从Creative Workshop迁移应用商店功能;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

/// 应用商店服务
///
/// 负责管理应用商店的核心功能，包括：
/// - 插件搜索和浏览
/// - 插件安装和卸载
/// - 插件更新检查
/// - 商店数据缓存
class AppStoreService extends ChangeNotifier {
  static AppStoreService? _instance;

  /// 获取单例实例
  static AppStoreService get instance {
    _instance ??= AppStoreService._();
    return _instance!;
  }

  /// 私有构造函数
  AppStoreService._();

  // 核心服务实例
  late plugin_sys.PluginStoreManager _storeManager;
  late plugin_sys.PluginSearchEngine _searchEngine;

  // 状态管理
  bool _isInitialized = false;
  bool _isLoading = false;
  String? _error;

  // 数据缓存
  List<plugin_sys.PluginStoreEntry> _allPlugins = [];
  List<plugin_sys.PluginStoreEntry> _filteredPlugins = [];
  Map<String, List<plugin_sys.PluginStoreEntry>> _categoryCache = {};

  // 搜索状态
  String _currentSearchQuery = '';
  String? _selectedCategory;

  // 事件流
  final StreamController<AppStoreEvent> _eventController =
      StreamController<AppStoreEvent>.broadcast();

  /// 获取初始化状态
  bool get isInitialized => _isInitialized;

  /// 获取加载状态
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get error => _error;

  /// 获取所有插件
  List<plugin_sys.PluginStoreEntry> get allPlugins =>
      List.unmodifiable(_allPlugins);

  /// 获取过滤后的插件
  List<plugin_sys.PluginStoreEntry> get filteredPlugins =>
      List.unmodifiable(_filteredPlugins);

  /// 获取当前搜索查询
  String get currentSearchQuery => _currentSearchQuery;

  /// 获取选中的分类
  String? get selectedCategory => _selectedCategory;

  /// 获取事件流
  Stream<AppStoreEvent> get events => _eventController.stream;

  /// 初始化服务
  Future<void> initialize() async {
    if (_isInitialized) {
      _log('warning', '应用商店服务已经初始化，跳过重复初始化');
      return;
    }

    try {
      _log('info', '开始初始化应用商店服务');
      _setLoading(true);

      // 初始化Plugin System组件
      _storeManager = plugin_sys.PluginStoreManager.instance;
      _searchEngine = plugin_sys.PluginSearchEngine.instance;

      // 加载初始数据
      await _loadInitialData();

      _isInitialized = true;
      _setError(null);
      _eventController.add(AppStoreEvent.initialized());

      _log('info', '应用商店服务初始化完成');
    } catch (e, stackTrace) {
      _log('severe', '应用商店服务初始化失败', e, stackTrace);
      _setError('初始化失败: $e');
      _eventController.add(AppStoreEvent.error(e.toString()));
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// 搜索插件
  Future<List<plugin_sys.PluginStoreEntry>> searchPlugins(String query) async {
    try {
      _log('info', '搜索插件: $query');
      _setLoading(true);
      _currentSearchQuery = query;

      List<plugin_sys.PluginStoreEntry> results;

      if (query.isEmpty) {
        // 空查询返回所有插件
        results = _allPlugins;
      } else {
        // 使用搜索引擎进行搜索
        final searchResults = await _searchEngine.searchPlugins(
          plugin_sys.SearchQuery(
            query: query,
            category: _selectedCategory,
            sortBy: plugin_sys.SortBy.relevance,
          ),
        );
        results = searchResults.plugins;
      }

      // 应用分类过滤
      if (_selectedCategory != null && _selectedCategory!.isNotEmpty) {
        results = results
            .where((plugin) => plugin.category == _selectedCategory)
            .toList();
      }

      _filteredPlugins = results;
      _eventController
          .add(AppStoreEvent.searchCompleted(query, results.length));

      notifyListeners();
      return results;
    } catch (e, stackTrace) {
      _log('severe', '搜索插件失败', e, stackTrace);
      _setError('搜索失败: $e');
      _eventController.add(AppStoreEvent.error(e.toString()));
      return [];
    } finally {
      _setLoading(false);
    }
  }

  /// 按分类过滤插件
  Future<List<plugin_sys.PluginStoreEntry>> filterByCategory(
      String? category) async {
    try {
      _log('info', '按分类过滤插件: $category');
      _selectedCategory = category;

      // 从缓存获取或重新搜索
      List<plugin_sys.PluginStoreEntry> results;
      if (category == null || category.isEmpty) {
        results = _allPlugins;
      } else {
        if (_categoryCache.containsKey(category)) {
          results = _categoryCache[category]!;
        } else {
          results = _allPlugins
              .where((plugin) => plugin.category == category)
              .toList();
          _categoryCache[category] = results;
        }
      }

      // 应用搜索过滤
      if (_currentSearchQuery.isNotEmpty) {
        final searchResults = await _searchEngine.searchPlugins(
          plugin_sys.SearchQuery(
            query: _currentSearchQuery,
            category: category,
            sortBy: plugin_sys.SortBy.relevance,
          ),
        );
        results = searchResults.plugins;
      }

      _filteredPlugins = results;
      _eventController.add(AppStoreEvent.categoryChanged(category));

      notifyListeners();
      return results;
    } catch (e, stackTrace) {
      _log('severe', '分类过滤失败', e, stackTrace);
      _setError('过滤失败: $e');
      return [];
    }
  }

  /// 获取插件详情
  Future<plugin_sys.PluginStoreEntry?> getPluginDetails(String pluginId) async {
    try {
      _log('info', '获取插件详情: $pluginId');

      final details = await _storeManager.getPluginDetails(pluginId);
      if (details != null) {
        _eventController.add(AppStoreEvent.pluginDetailsLoaded(pluginId));
      }

      return details;
    } catch (e, stackTrace) {
      _log('severe', '获取插件详情失败', e, stackTrace);
      _setError('获取详情失败: $e');
      return null;
    }
  }

  /// 安装插件
  Future<bool> installPlugin(String pluginId) async {
    try {
      _log('info', '安装插件: $pluginId');
      _setLoading(true);

      final result = await _storeManager.installPlugin(pluginId);
      if (result.isSuccess) {
        _eventController.add(AppStoreEvent.pluginInstalled(pluginId));
        _log('info', '插件安装成功: $pluginId');
        return true;
      } else {
        _setError('安装失败: ${result.message}');
        _eventController.add(AppStoreEvent.error('安装失败: ${result.message}'));
        return false;
      }
    } catch (e, stackTrace) {
      _log('severe', '安装插件失败', e, stackTrace);
      _setError('安装失败: $e');
      _eventController.add(AppStoreEvent.error(e.toString()));
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// 刷新商店数据
  Future<void> refreshStore() async {
    try {
      _log('info', '刷新应用商店数据');
      _setLoading(true);

      // 清除缓存
      _categoryCache.clear();

      // 重新加载数据
      await _loadInitialData();

      // 重新应用当前过滤条件
      if (_selectedCategory != null || _currentSearchQuery.isNotEmpty) {
        await searchPlugins(_currentSearchQuery);
      }

      _eventController.add(AppStoreEvent.storeRefreshed());
      _log('info', '应用商店数据刷新完成');
    } catch (e, stackTrace) {
      _log('severe', '刷新商店数据失败', e, stackTrace);
      _setError('刷新失败: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// 获取可用分类列表
  List<String> getAvailableCategories() {
    final categories = <String>{};
    for (final plugin in _allPlugins) {
      if (plugin.category.isNotEmpty) {
        categories.add(plugin.category);
      }
    }
    return categories.toList()..sort();
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    _log('info', '加载应用商店初始数据');

    // 获取所有可用插件
    final plugins = await _storeManager.getAllPlugins();
    _allPlugins = plugins;
    _filteredPlugins = plugins;

    _log('info', '加载了 ${plugins.length} 个插件');
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 设置错误信息
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }

  /// 日志记录
  void _log(String level, String message,
      [Object? error, StackTrace? stackTrace]) {
    if (kDebugMode) {
      final timestamp = DateTime.now().toIso8601String();
      print('[$timestamp] [$level] [AppStoreService] $message');
      if (error != null) {
        print('Error: $error');
      }
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// 清理资源
  @override
  void dispose() {
    _eventController.close();
    super.dispose();
  }
}

/// 应用商店事件
class AppStoreEvent {
  final String type;
  final Map<String, dynamic> data;

  AppStoreEvent._(this.type, this.data);

  factory AppStoreEvent.initialized() => AppStoreEvent._('initialized', {});
  factory AppStoreEvent.searchCompleted(String query, int resultCount) =>
      AppStoreEvent._(
          'searchCompleted', {'query': query, 'resultCount': resultCount});
  factory AppStoreEvent.categoryChanged(String? category) =>
      AppStoreEvent._('categoryChanged', {'category': category});
  factory AppStoreEvent.pluginDetailsLoaded(String pluginId) =>
      AppStoreEvent._('pluginDetailsLoaded', {'pluginId': pluginId});
  factory AppStoreEvent.pluginInstalled(String pluginId) =>
      AppStoreEvent._('pluginInstalled', {'pluginId': pluginId});
  factory AppStoreEvent.storeRefreshed() =>
      AppStoreEvent._('storeRefreshed', {});
  factory AppStoreEvent.error(String message) =>
      AppStoreEvent._('error', {'message': message});
}
