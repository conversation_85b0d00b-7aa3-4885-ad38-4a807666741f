/*
---------------------------------------------------------------
File name:          plugin_system_integration_adapter_simple_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        PluginSystemIntegrationAdapter 简化测试文件
---------------------------------------------------------------
*/

import 'package:creative_workshop/src/core/plugins/plugin_system_integration_adapter.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PluginSystemIntegrationAdapter Basic Tests', () {
    late PluginSystemIntegrationAdapter adapter;

    setUp(() {
      adapter = PluginSystemIntegrationAdapter.instance;
    });

    tearDown(() async {
      try {
        await adapter.dispose();
      } catch (e) {
        // 忽略dispose错误
      }
    });

    test('should be singleton', () {
      final adapter1 = PluginSystemIntegrationAdapter.instance;
      final adapter2 = PluginSystemIntegrationAdapter.instance;
      expect(adapter1, same(adapter2));
    });

    test('should provide event stream', () {
      final events = adapter.events;
      expect(events, isA<Stream<PluginIntegrationEvent>>());
    });

    group('Enums', () {
      test('PluginIntegrationEventType should have correct values', () {
        expect(PluginIntegrationEventType.installed.name, 'installed');
        expect(PluginIntegrationEventType.enabled.name, 'enabled');
        expect(PluginIntegrationEventType.disabled.name, 'disabled');
        expect(PluginIntegrationEventType.uninstalled.name, 'uninstalled');
        expect(PluginIntegrationEventType.synchronized.name, 'synchronized');
        expect(PluginIntegrationEventType.error.name, 'error');
      });

      test('IntegrationSource should have correct values', () {
        expect(IntegrationSource.workshop.name, 'workshop');
        expect(IntegrationSource.system.name, 'system');
        expect(IntegrationSource.adapter.name, 'adapter');
      });
    });

    group('Event Creation', () {
      test('PluginIntegrationEvent.installed should create correct event', () {
        final now = DateTime.now();
        final event = PluginIntegrationEvent.installed(
          pluginId: 'test_plugin',
          source: IntegrationSource.workshop,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.installed);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.workshop);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent.enabled should create correct event', () {
        final now = DateTime.now();
        final event = PluginIntegrationEvent.enabled(
          pluginId: 'test_plugin',
          source: IntegrationSource.system,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.enabled);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.system);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent toString should work correctly', () {
        final now = DateTime.now();
        final event = PluginIntegrationEvent.installed(
          pluginId: 'test_plugin',
          source: IntegrationSource.workshop,
          timestamp: now,
        );

        final eventString = event.toString();
        expect(eventString, contains('PluginIntegrationEvent'));
        expect(eventString, contains('test_plugin'));
        expect(eventString, contains('workshop'));
        expect(eventString, contains('installed'));
      });
    });

    group('Error Handling', () {
      test('should throw StateError when not initialized', () {
        expect(
          () => adapter.getIntegrationStatistics(),
          throwsA(isA<StateError>()),
        );
      });
    });
  });
}
