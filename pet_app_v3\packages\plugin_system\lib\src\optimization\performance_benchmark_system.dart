/*
---------------------------------------------------------------
File name:          performance_benchmark_system.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        性能基准测试系统 - Phase 5.1 高级性能优化
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 5.1 - 性能基准测试系统实现;
---------------------------------------------------------------
*/

import 'dart:async';

import 'dart:io';
import 'dart:math' as math;

/// 基准测试类型
enum BenchmarkType {
  /// CPU密集型
  cpuIntensive,

  /// 内存密集型
  memoryIntensive,

  /// I/O密集型
  ioIntensive,

  /// 网络密集型
  networkIntensive,

  /// 混合型
  mixed,
}

/// 基准测试配置
class BenchmarkConfig {
  const BenchmarkConfig({
    this.warmupIterations = 100,
    this.measurementIterations = 1000,
    this.timeoutDuration = const Duration(minutes: 5),
    this.enableGCBetweenTests = true,
    this.enableDetailedProfiling = false,
    this.sampleSize = 10,
    this.confidenceLevel = 0.95,
  });

  /// 预热迭代次数
  final int warmupIterations;

  /// 测量迭代次数
  final int measurementIterations;

  /// 超时时间
  final Duration timeoutDuration;

  /// 测试间启用GC
  final bool enableGCBetweenTests;

  /// 启用详细性能分析
  final bool enableDetailedProfiling;

  /// 样本大小
  final int sampleSize;

  /// 置信水平
  final double confidenceLevel;
}

/// 基准测试结果
class BenchmarkResult {
  const BenchmarkResult({
    required this.testName,
    required this.type,
    required this.iterations,
    required this.totalTime,
    required this.averageTime,
    required this.minTime,
    required this.maxTime,
    required this.standardDeviation,
    required this.throughput,
    required this.memoryUsage,
    required this.gcCount,
    required this.timestamp,
    this.percentiles = const <int, double>{},
    this.metadata = const <String, dynamic>{},
  });

  /// 测试名称
  final String testName;

  /// 测试类型
  final BenchmarkType type;

  /// 迭代次数
  final int iterations;

  /// 总时间（微秒）
  final int totalTime;

  /// 平均时间（微秒）
  final double averageTime;

  /// 最小时间（微秒）
  final int minTime;

  /// 最大时间（微秒）
  final int maxTime;

  /// 标准差
  final double standardDeviation;

  /// 吞吐量（操作/秒）
  final double throughput;

  /// 内存使用（字节）
  final int memoryUsage;

  /// GC次数
  final int gcCount;

  /// 时间戳
  final DateTime timestamp;

  /// 百分位数
  final Map<int, double> percentiles;

  /// 元数据
  final Map<String, dynamic> metadata;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'testName': testName,
        'type': type.name,
        'iterations': iterations,
        'totalTime': totalTime,
        'averageTime': averageTime,
        'minTime': minTime,
        'maxTime': maxTime,
        'standardDeviation': standardDeviation,
        'throughput': throughput,
        'memoryUsage': memoryUsage,
        'gcCount': gcCount,
        'timestamp': timestamp.toIso8601String(),
        'percentiles': percentiles,
        'metadata': metadata,
      };
}

/// 基准测试套件
class BenchmarkSuite {
  const BenchmarkSuite({
    required this.name,
    required this.tests,
    this.config = const BenchmarkConfig(),
    this.description,
  });

  /// 套件名称
  final String name;

  /// 测试列表
  final List<BenchmarkTest> tests;

  /// 配置
  final BenchmarkConfig config;

  /// 描述
  final String? description;
}

/// 基准测试
class BenchmarkTest {
  const BenchmarkTest({
    required this.name,
    required this.type,
    required this.testFunction,
    this.setupFunction,
    this.teardownFunction,
    this.config,
    this.description,
  });

  /// 测试名称
  final String name;

  /// 测试类型
  final BenchmarkType type;

  /// 测试函数
  final Future<void> Function() testFunction;

  /// 设置函数
  final Future<void> Function()? setupFunction;

  /// 清理函数
  final Future<void> Function()? teardownFunction;

  /// 配置
  final BenchmarkConfig? config;

  /// 描述
  final String? description;
}

/// 性能基准测试系统
class PerformanceBenchmarkSystem {
  PerformanceBenchmarkSystem._();
  static final PerformanceBenchmarkSystem _instance =
      PerformanceBenchmarkSystem._();
  static PerformanceBenchmarkSystem get instance => _instance;

  /// 测试结果历史
  final List<BenchmarkResult> _resultHistory = <BenchmarkResult>[];

  /// 运行基准测试套件
  Future<List<BenchmarkResult>> runBenchmarkSuite(BenchmarkSuite suite) async {
    final results = <BenchmarkResult>[];

    for (final test in suite.tests) {
      final config = test.config ?? suite.config;
      final result = await runBenchmarkTest(test, config);
      results.add(result);
      _resultHistory.add(result);

      // 测试间GC
      if (config.enableGCBetweenTests) {
        await _performGarbageCollection();
      }
    }

    return results;
  }

  /// 运行单个基准测试
  Future<BenchmarkResult> runBenchmarkTest(
    BenchmarkTest test,
    BenchmarkConfig config,
  ) async {
    try {
      // 设置
      if (test.setupFunction != null) {
        await test.setupFunction!();
      }

      // 预热
      await _warmupTest(test, config);

      // 测量
      final measurements = await _measureTest(test, config);

      // 清理
      if (test.teardownFunction != null) {
        await test.teardownFunction!();
      }

      // 计算统计信息
      final result = _calculateStatistics(test, measurements, config);
      return result;
    } catch (e) {
      rethrow;
    }
  }

  /// 预热测试
  Future<void> _warmupTest(BenchmarkTest test, BenchmarkConfig config) async {
    for (int i = 0; i < config.warmupIterations; i++) {
      await test.testFunction();
    }
  }

  /// 测量测试
  Future<List<int>> _measureTest(
      BenchmarkTest test, BenchmarkConfig config,) async {
    final measurements = <int>[];

    for (int i = 0; i < config.measurementIterations; i++) {
      final startTime = DateTime.now().microsecondsSinceEpoch;

      await test.testFunction().timeout(config.timeoutDuration);

      final endTime = DateTime.now().microsecondsSinceEpoch;
      measurements.add(endTime - startTime);
    }

    return measurements;
  }

  /// 计算统计信息
  BenchmarkResult _calculateStatistics(
    BenchmarkTest test,
    List<int> measurements,
    BenchmarkConfig config,
  ) {
    measurements.sort();

    final totalTime = measurements.reduce((int a, int b) => a + b);
    final averageTime = totalTime / measurements.length;
    final minTime = measurements.first;
    final maxTime = measurements.last;

    // 计算标准差
    final variance = measurements
            .map((int time) => math.pow(time - averageTime, 2))
            .reduce((num a, num b) => a + b) /
        measurements.length;
    final standardDeviation = math.sqrt(variance);

    // 计算吞吐量
    final throughput = 1000000 / averageTime; // 操作/秒

    // 计算百分位数
    final percentiles = <int, double>{
      50: _calculatePercentile(measurements, 0.5),
      90: _calculatePercentile(measurements, 0.9),
      95: _calculatePercentile(measurements, 0.95),
      99: _calculatePercentile(measurements, 0.99),
    };

    return BenchmarkResult(
      testName: test.name,
      type: test.type,
      iterations: measurements.length,
      totalTime: totalTime,
      averageTime: averageTime,
      minTime: minTime,
      maxTime: maxTime,
      standardDeviation: standardDeviation,
      throughput: throughput,
      memoryUsage: _getCurrentMemoryUsage(),
      gcCount: 0, // 简化实现
      timestamp: DateTime.now(),
      percentiles: percentiles,
    );
  }

  /// 计算百分位数
  double _calculatePercentile(List<int> sortedData, double percentile) {
    final index = (sortedData.length - 1) * percentile;
    final lower = index.floor();
    final upper = index.ceil();

    if (lower == upper) {
      return sortedData[lower].toDouble();
    }

    final weight = index - lower;
    return sortedData[lower] * (1 - weight) + sortedData[upper] * weight;
  }

  /// 获取当前内存使用
  int _getCurrentMemoryUsage() {
    try {
      return ProcessInfo.currentRss;
    } catch (e) {
      return 0;
    }
  }

  /// 执行垃圾回收
  Future<void> _performGarbageCollection() async {
    // Dart没有直接的GC API，使用间接方法
    final largeList = List<int>.filled(1000000, 0);
    largeList.clear();
    await Future<void>.delayed(const Duration(milliseconds: 10));
  }

  /// 比较基准测试结果
  BenchmarkComparison compareBenchmarks(
    BenchmarkResult baseline,
    BenchmarkResult current,
  ) {
    final speedupRatio = baseline.averageTime / current.averageTime;
    final throughputRatio = current.throughput / baseline.throughput;
    final memoryRatio = current.memoryUsage / baseline.memoryUsage;

    return BenchmarkComparison(
      baseline: baseline,
      current: current,
      speedupRatio: speedupRatio,
      throughputRatio: throughputRatio,
      memoryRatio: memoryRatio,
      isImprovement: speedupRatio > 1.0,
      significantChange: (speedupRatio - 1.0).abs() > 0.05,
    );
  }

  /// 生成性能报告
  PerformanceReport generateReport({
    Duration? timeWindow,
    List<String>? testNames,
  }) {
    var results = _resultHistory;

    // 时间窗口过滤
    if (timeWindow != null) {
      final cutoff = DateTime.now().subtract(timeWindow);
      results = results
          .where((BenchmarkResult r) => r.timestamp.isAfter(cutoff))
          .toList();
    }

    // 测试名称过滤
    if (testNames != null) {
      results = results
          .where((BenchmarkResult r) => testNames.contains(r.testName))
          .toList();
    }

    // 按测试名称分组
    final groupedResults = <String, List<BenchmarkResult>>{};
    for (final result in results) {
      groupedResults[result.testName] ??= <BenchmarkResult>[];
      groupedResults[result.testName]!.add(result);
    }

    // 计算趋势
    final trends = <String, PerformanceTrend>{};
    for (final entry in groupedResults.entries) {
      trends[entry.key] = _calculateTrend(entry.value);
    }

    return PerformanceReport(
      generatedAt: DateTime.now(),
      totalTests: results.length,
      uniqueTests: groupedResults.length,
      timeWindow: timeWindow,
      results: results,
      trends: trends,
    );
  }

  /// 计算性能趋势
  PerformanceTrend _calculateTrend(List<BenchmarkResult> results) {
    if (results.length < 2) {
      return const PerformanceTrend(
        direction: TrendDirection.stable,
        changeRate: 0,
        confidence: 0,
      );
    }

    results.sort((BenchmarkResult a, BenchmarkResult b) =>
        a.timestamp.compareTo(b.timestamp),);

    final first = results.first.averageTime;
    final last = results.last.averageTime;
    final changeRate = (last - first) / first;

    TrendDirection direction;
    if (changeRate > 0.05) {
      direction = TrendDirection.degrading;
    } else if (changeRate < -0.05) {
      direction = TrendDirection.improving;
    } else {
      direction = TrendDirection.stable;
    }

    // 简化的置信度计算
    final double confidence = math.min(results.length / 10.0, 1);

    return PerformanceTrend(
      direction: direction,
      changeRate: changeRate,
      confidence: confidence,
    );
  }

  /// 创建内置基准测试套件
  BenchmarkSuite createBuiltinBenchmarkSuite() => BenchmarkSuite(
        name: 'Plugin System Benchmark Suite',
        description: '插件系统内置基准测试套件',
        tests: <BenchmarkTest>[
          // CPU密集型测试
          BenchmarkTest(
            name: 'CPU Intensive - Prime Calculation',
            type: BenchmarkType.cpuIntensive,
            testFunction: _cpuIntensiveTest,
            description: 'CPU密集型测试：质数计算',
          ),

          // 内存密集型测试
          BenchmarkTest(
            name: 'Memory Intensive - Large Array Operations',
            type: BenchmarkType.memoryIntensive,
            testFunction: _memoryIntensiveTest,
            description: '内存密集型测试：大数组操作',
          ),

          // I/O密集型测试
          BenchmarkTest(
            name: 'IO Intensive - File Operations',
            type: BenchmarkType.ioIntensive,
            testFunction: _ioIntensiveTest,
            description: 'I/O密集型测试：文件操作',
          ),

          // 算法优化测试
          BenchmarkTest(
            name: 'Algorithm - Optimized Sort',
            type: BenchmarkType.cpuIntensive,
            testFunction: _algorithmOptimizationTest,
            description: '算法优化测试：排序算法',
          ),
        ],
      );

  /// CPU密集型测试
  Future<void> _cpuIntensiveTest() async {
    // 计算前1000个质数
    var count = 0;
    var num = 2;

    while (count < 1000) {
      if (_isPrime(num)) {
        count++;
      }
      num++;
    }
  }

  /// 内存密集型测试
  Future<void> _memoryIntensiveTest() async {
    // 创建和操作大数组
    final data = List<int>.generate(100000, (int i) => i);

    // 执行一些内存密集操作
    data.sort();
    data.reversed.toList();
    data.where((int x) => x % 2 == 0).toList();
  }

  /// I/O密集型测试
  Future<void> _ioIntensiveTest() async {
    // 模拟文件I/O操作
    final tempFile =
        File('temp_benchmark_${DateTime.now().millisecondsSinceEpoch}.txt');

    try {
      // 写入数据
      await tempFile.writeAsString('Benchmark test data\n' * 1000);

      // 读取数据
      await tempFile.readAsString();

      // 追加数据
      await tempFile.writeAsString('Additional data\n', mode: FileMode.append);
    } finally {
      // 清理
      if (await tempFile.exists()) {
        await tempFile.delete();
      }
    }
  }

  /// 算法优化测试
  Future<void> _algorithmOptimizationTest() async {
    final data =
        List<int>.generate(10000, (int i) => math.Random().nextInt(100000));
    data.sort(); // 使用标准排序避免类型推断问题
  }

  /// 判断质数
  bool _isPrime(int n) {
    if (n < 2) return false;
    if (n == 2) return true;
    if (n % 2 == 0) return false;

    for (int i = 3; i * i <= n; i += 2) {
      if (n % i == 0) return false;
    }

    return true;
  }

  /// 获取结果历史
  List<BenchmarkResult> getResultHistory() => List.from(_resultHistory);

  /// 清理历史记录
  void clearHistory() => _resultHistory.clear();
}

/// 基准测试比较结果
class BenchmarkComparison {
  const BenchmarkComparison({
    required this.baseline,
    required this.current,
    required this.speedupRatio,
    required this.throughputRatio,
    required this.memoryRatio,
    required this.isImprovement,
    required this.significantChange,
  });

  final BenchmarkResult baseline;
  final BenchmarkResult current;
  final double speedupRatio;
  final double throughputRatio;
  final double memoryRatio;
  final bool isImprovement;
  final bool significantChange;
}

/// 性能报告
class PerformanceReport {
  const PerformanceReport({
    required this.generatedAt,
    required this.totalTests,
    required this.uniqueTests,
    required this.results,
    required this.trends,
    this.timeWindow,
  });

  final DateTime generatedAt;
  final int totalTests;
  final int uniqueTests;
  final Duration? timeWindow;
  final List<BenchmarkResult> results;
  final Map<String, PerformanceTrend> trends;
}

/// 性能趋势
class PerformanceTrend {
  const PerformanceTrend({
    required this.direction,
    required this.changeRate,
    required this.confidence,
  });

  final TrendDirection direction;
  final double changeRate;
  final double confidence;
}

/// 趋势方向
enum TrendDirection {
  improving,
  stable,
  degrading,
}
