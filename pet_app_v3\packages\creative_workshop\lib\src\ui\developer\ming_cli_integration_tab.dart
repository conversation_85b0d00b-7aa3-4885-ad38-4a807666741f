/*
---------------------------------------------------------------
File name:          ming_cli_integration_tab.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-07-22
Dart Version:       3.2+
Description:        Ming CLI 集成标签页
---------------------------------------------------------------
Change History:
    2025-07-21: Phase ******* - Ming CLI 集成功能实现;
---------------------------------------------------------------
*/

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';

/// CLI 命令类型
enum CliCommandType {
  create('创建项目'),
  build('构建项目'),
  test('运行测试'),
  publish('发布插件'),
  validate('验证项目'),
  doctor('环境诊断');

  const CliCommandType(this.displayName);
  final String displayName;
}

/// CLI 命令历史
class CliCommandHistory {
  const CliCommandHistory({
    required this.command,
    required this.executedAt,
    required this.success,
    this.output,
    this.error,
  });

  final String command;
  final DateTime executedAt;
  final bool success;
  final String? output;
  final String? error;
}

/// Ming CLI 集成标签页
class MingCliIntegrationTab extends StatefulWidget {
  const MingCliIntegrationTab({super.key});

  @override
  State<MingCliIntegrationTab> createState() => _MingCliIntegrationTabState();
}

class _MingCliIntegrationTabState extends State<MingCliIntegrationTab> {
  final TextEditingController _commandController = TextEditingController();
  final ScrollController _outputScrollController = ScrollController();

  bool _isCliInstalled = false;
  bool _isExecuting = false;
  String _cliVersion = '';
  List<CliCommandHistory> _commandHistory = <CliCommandHistory>[];
  String _currentOutput = '';

  // LocalMingCliService实例
  late final LocalMingCliService _mingCliService;

  @override
  void initState() {
    super.initState();
    _mingCliService = LocalMingCliService.instance;
    _checkCliInstallation();
    _loadCommandHistory();
  }

  @override
  void dispose() {
    _commandController.dispose();
    _outputScrollController.dispose();
    super.dispose();
  }

  /// 检查 CLI 安装状态
  Future<void> _checkCliInstallation() async {
    try {
      // 使用LocalMingCliService初始化和检测
      await _mingCliService.initialize();

      setState(() {
        _isCliInstalled = _mingCliService.isInstalled;
        _cliVersion = _mingCliService.version;
      });
    } catch (e) {
      // CLI 检测失败
      setState(() {
        _isCliInstalled = false;
        _cliVersion = '';
      });
    }
  }

  /// 加载命令历史
  Future<void> _loadCommandHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getStringList('ming_cli_command_history') ?? <String>[];

      _commandHistory = historyJson.map((String jsonStr) {
        final data = jsonDecode(jsonStr) as Map<String, dynamic>;
        return CliCommandHistory(
          command: data['command'] as String,
          executedAt: DateTime.parse(data['executedAt'] as String),
          success: data['success'] as bool,
          output: data['output'] as String?,
          error: data['error'] as String?,
        );
      }).toList();
    } catch (e) {
      // 如果加载失败，使用空历史记录
      _commandHistory = <CliCommandHistory>[];
    }
  }

  /// 保存命令历史到本地存储
  Future<void> _saveCommandHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = _commandHistory.take(50).map((CliCommandHistory history) => jsonEncode(<String, Object?>{
          'command': history.command,
          'executedAt': history.executedAt.toIso8601String(),
          'success': history.success,
          'output': history.output,
          'error': history.error,
        }),).toList();

      await prefs.setStringList('ming_cli_command_history', historyJson);
    } catch (e) {
      // 保存失败时忽略错误
      debugPrint('保存命令历史失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) => Column(
      children: <Widget>[
        // CLI 状态面板
        _buildCliStatusPanel(),

        // 快速命令面板
        _buildQuickCommandsPanel(),

        // 命令输入和输出区域
        Expanded(
          child: Row(
            children: <Widget>[
              // 左侧：命令历史
              Expanded(
                child: _buildCommandHistory(),
              ),

              // 右侧：命令输入和输出
              Expanded(
                flex: 2,
                child: _buildCommandInterface(),
              ),
            ],
          ),
        ),
      ],
    );

  /// 构建 CLI 状态面板
  Widget _buildCliStatusPanel() => Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: <Widget>[
          Icon(
            Icons.terminal,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  'Ming CLI 状态',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 4),
                Row(
                  children: <Widget>[
                    Icon(
                      _isCliInstalled ? Icons.check_circle : Icons.error,
                      size: 16,
                      color: _isCliInstalled ? Colors.green : Colors.red,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _isCliInstalled ? '已安装 (v$_cliVersion)' : '未安装',
                      style: TextStyle(
                        color: _isCliInstalled ? Colors.green : Colors.red,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          if (!_isCliInstalled)
            ElevatedButton.icon(
              onPressed: _installCli,
              icon: const Icon(Icons.download),
              label: const Text('安装 CLI'),
            )
          else
            Row(
              children: <Widget>[
                IconButton(
                  onPressed: _updateCli,
                  icon: const Icon(Icons.update),
                  tooltip: '检查更新',
                ),
                IconButton(
                  onPressed: _openCliDocumentation,
                  icon: const Icon(Icons.help),
                  tooltip: '查看文档',
                ),
              ],
            ),
        ],
      ),
    );

  /// 构建快速命令面板
  Widget _buildQuickCommandsPanel() => Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '快速命令',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: CliCommandType.values.map((CliCommandType type) => Padding(
                  padding: const EdgeInsets.only(right: 8),
                  child: ActionChip(
                    label: Text(type.displayName),
                    avatar: Icon(
                      _getCommandIcon(type),
                      size: 16,
                    ),
                    onPressed: () => _executeQuickCommand(type),
                  ),
                ),).toList(),
            ),
          ),
        ],
      ),
    );

  /// 构建命令历史
  Widget _buildCommandHistory() => Container(
      margin: const EdgeInsets.only(left: 16, bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: <Widget>[
                const Icon(Icons.history, size: 16),
                const SizedBox(width: 8),
                Text(
                  '命令历史',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          Expanded(
            child: _commandHistory.isEmpty
                ? const Center(
                    child: Text(
                      '暂无命令历史',
                      style: TextStyle(color: Colors.grey),
                    ),
                  )
                : ListView.builder(
                    itemCount: _commandHistory.length,
                    itemBuilder: (BuildContext context, int index) {
                      final CliCommandHistory history = _commandHistory[index];
                      return ListTile(
                        dense: true,
                        leading: Icon(
                          history.success ? Icons.check_circle : Icons.error,
                          size: 16,
                          color: history.success ? Colors.green : Colors.red,
                        ),
                        title: Text(
                          history.command,
                          style: const TextStyle(
                            fontSize: 12,
                            fontFamily: 'monospace',
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        subtitle: Text(
                          _formatTime(history.executedAt),
                          style: const TextStyle(fontSize: 10),
                        ),
                        onTap: () => _rerunCommand(history.command),
                      );
                    },
                  ),
          ),
        ],
      ),
    );

  /// 构建命令界面
  Widget _buildCommandInterface() => Container(
      margin: const EdgeInsets.only(right: 16, bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: <Widget>[
          // 命令输入
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: Row(
              children: <Widget>[
                const Text(
                  'ming',
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: TextField(
                    controller: _commandController,
                    enabled: _isCliInstalled && !_isExecuting,
                    decoration: const InputDecoration(
                      hintText: '输入命令...',
                      border: InputBorder.none,
                      isDense: true,
                    ),
                    style: const TextStyle(fontFamily: 'monospace'),
                    onSubmitted: _executeCommand,
                  ),
                ),
                IconButton(
                  onPressed: _isCliInstalled && !_isExecuting
                      ? () => _executeCommand(_commandController.text)
                      : null,
                  icon: _isExecuting
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.play_arrow),
                  tooltip: '执行命令',
                ),
              ],
            ),
          ),

          // 输出区域
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              child: SingleChildScrollView(
                controller: _outputScrollController,
                child: Text(
                  _currentOutput.isEmpty ? '等待命令执行...' : _currentOutput,
                  style: TextStyle(
                    fontFamily: 'monospace',
                    fontSize: 12,
                    color: _currentOutput.isEmpty ? Colors.grey : null,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

  /// 获取命令图标
  IconData _getCommandIcon(CliCommandType type) {
    switch (type) {
      case CliCommandType.create:
        return Icons.create_new_folder;
      case CliCommandType.build:
        return Icons.build;
      case CliCommandType.test:
        return Icons.verified;
      case CliCommandType.publish:
        return Icons.publish;
      case CliCommandType.validate:
        return Icons.check_circle;
      case CliCommandType.doctor:
        return Icons.medical_services;
    }
  }

  /// 格式化时间
  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  /// 执行快速命令
  void _executeQuickCommand(CliCommandType type) {
    String command;
    switch (type) {
      case CliCommandType.create:
        command = 'create my-plugin --type tool';
      case CliCommandType.build:
        command = 'build';
      case CliCommandType.test:
        command = 'test';
      case CliCommandType.publish:
        command = 'publish';
      case CliCommandType.validate:
        command = 'validate';
      case CliCommandType.doctor:
        command = 'doctor';
    }

    _commandController.text = command;
    _executeCommand(command);
  }

  /// 执行命令
  Future<void> _executeCommand(String command) async {
    if (command.trim().isEmpty || _isExecuting) {
      return;
    }

    setState(() {
      _isExecuting = true;
      _currentOutput = '\$ ming $command\n正在执行...\n';
    });

    // 使用LocalMingCliService执行命令
    final result = await _mingCliService.executeCommand(command);

    final success = result.success;
    final output = success
        ? '✅ 命令执行成功 (${result.mode.name})\n📝 输出:\n${result.output}\n⏱️ 执行时间: ${result.executionTime.inMilliseconds}ms'
        : null;
    final error = !success
        ? '❌ 命令执行失败 (${result.mode.name}, 退出码: ${result.exitCode})\n📝 错误:\n${result.error}\n⏱️ 执行时间: ${result.executionTime.inMilliseconds}ms'
        : null;

    setState(() {
      _isExecuting = false;
      _currentOutput = '\$ ming $command\n${success ? output! : error!}';
    });

    // 添加到历史记录
    final historyEntry = CliCommandHistory(
      command: 'ming $command',
      executedAt: DateTime.now(),
      success: success,
      output: output,
      error: error,
    );

    _commandHistory.insert(0, historyEntry);

    // 保存历史记录到本地存储
    await _saveCommandHistory();

    // 滚动到底部
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _outputScrollController.animateTo(
        _outputScrollController.position.maxScrollExtent,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    });

    _commandController.clear();
  }

  /// 重新运行命令
  void _rerunCommand(String command) {
    final cleanCommand =
        command.startsWith('ming ') ? command.substring(5) : command;
    _commandController.text = cleanCommand;
    _executeCommand(cleanCommand);
  }

  /// 安装 CLI
  Future<void> _installCli() async {
    try {
      // 显示安装进度对话框
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => const AlertDialog(
          title: Text('安装 Ming CLI'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在下载并安装 Ming CLI...'),
            ],
          ),
        ),
      );

      // 使用LocalMingCliService尝试安装
      final success = await _mingCliService.installOrBuildMingCli();

      Navigator.of(context).pop();

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('Ming CLI 安装成功！(${_mingCliService.statusDescription})'),
            backgroundColor: Colors.green,
          ),
        );
        // 重新检查安装状态
        await _checkCliInstallation();
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content:
                Text('安装失败，已切换到模拟模式 (${_mingCliService.statusDescription})'),
            backgroundColor: Colors.orange,
          ),
        );
        // 即使失败也要更新状态，因为可能切换到了模拟模式
        await _checkCliInstallation();
      }
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('安装过程中出现错误: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 更新 CLI
  Future<void> _updateCli() async {
    try {
      // 显示更新进度对话框
      showDialog<void>(
        context: context,
        barrierDismissible: false,
        builder: (BuildContext context) => const AlertDialog(
          title: Text('更新 Ming CLI'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text('正在检查并更新 Ming CLI...'),
            ],
          ),
        ),
      );

      // 使用LocalMingCliService刷新状态
      await _mingCliService.refresh();

      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('状态已刷新：${_mingCliService.statusDescription}'),
          backgroundColor: Colors.blue,
        ),
      );

      // 重新检查安装状态
      await _checkCliInstallation();
    } catch (e) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('更新过程中出现错误: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// 打开 CLI 文档
  Future<void> _openCliDocumentation() async {
    try {
      // 尝试执行 ming help 命令显示帮助信息
      final helpResult = await Process.run(
        'ming',
        <String>['help'],
        runInShell: true,
      );

      if (helpResult.exitCode == 0) {
        // 在对话框中显示帮助信息
        showDialog<void>(
          context: context,
          builder: (BuildContext context) => AlertDialog(
            title: const Text('Ming CLI 文档'),
            content: SizedBox(
              width: double.maxFinite,
              height: 400,
              child: SingleChildScrollView(
                child: Text(
                  helpResult.stdout.toString(),
                  style: const TextStyle(fontFamily: 'monospace'),
                ),
              ),
            ),
            actions: <Widget>[
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('关闭'),
              ),
            ],
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('无法获取 CLI 文档，请确保 Ming CLI 已正确安装'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('打开文档时出现错误: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
