/*
---------------------------------------------------------------
File name:          ming_cli_git_integration_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        Ming CLI Git集成测试
---------------------------------------------------------------
Change History:
    2025-07-30: 创建Ming CLI Git集成测试;
---------------------------------------------------------------
*/

import 'dart:io';
import 'package:test/test.dart';
import 'package:path/path.dart' as path;
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';

void main() {
  group('Ming CLI Git Integration Tests', () {
    late LocalMingCliService service;
    late Directory testDir;

    setUpAll(() async {
      // 创建测试目录
      testDir =
          Directory(path.join(Directory.systemTemp.path, 'ming_cli_test'));
      if (await testDir.exists()) {
        await testDir.delete(recursive: true);
      }
      await testDir.create(recursive: true);

      // 切换到测试目录
      Directory.current = testDir;
    });

    setUp(() {
      service = LocalMingCliService.instance;
    });

    tearDownAll(() async {
      // 清理测试目录
      try {
        if (await testDir.exists()) {
          await testDir.delete(recursive: true);
        }
      } catch (e) {
        // 忽略清理错误，这在Windows上很常见
        print('⚠️ 清理测试目录失败: $e');
      }
    });

    test('should have correct Git repository URL', () {
      // 验证Git仓库URL是正确的
      expect(service.toString(), contains('LocalMingCliService'));
      // 这是一个间接测试，验证服务可以正常实例化
    });

    test('should detect Git availability', () async {
      // 检查系统是否有Git
      try {
        final result = await Process.run('git', <String>['--version']);
        if (result.exitCode == 0) {
          print('✅ Git可用: ${result.stdout}');
        } else {
          print('⚠️ Git不可用，跳过Git相关测试');
        }
      } catch (e) {
        print('⚠️ Git检查失败: $e');
      }
    });

    test('should handle tools directory creation', () async {
      await service.initialize();

      // 验证服务初始化后的状态
      expect(service.currentMode, isNotNull);
      expect(service.statusDescription, isNotNull);

      // 验证服务可以提供详细状态
      final status = service.getDetailedStatus();
      expect(status, isA<Map<String, dynamic>>());
      expect(status['currentMode'], isA<String>());
    });

    test('should execute commands in fallback mode', () async {
      await service.initialize();

      // 在没有真实CLI的情况下，应该使用fallback模式
      final result = await service.executeCommand('--version');

      expect(result, isNotNull);
      expect(result.mode, isNotNull);
      expect(result.executionTime, isNotNull);

      // 验证结果包含有意义的信息
      expect(result.output, isNotEmpty);
    });

    test('should handle create command with parameters', () async {
      await service.initialize();

      final result =
          await service.executeCommand('create test-project --template basic');

      expect(result, isNotNull);
      expect(result.success, isTrue);
      expect(result.output, contains('模拟模式'));
    });

    test('should provide comprehensive status information', () async {
      await service.initialize();

      final status = service.getDetailedStatus();

      // 验证状态信息的完整性
      expect(status['isInstalled'], isA<bool>());
      expect(status['version'], isA<String>());
      expect(status['currentMode'], isA<String>());
      expect(status['statusDescription'], isA<String>());
      expect(status['timestamp'], isA<String>());

      // 验证时间戳格式
      final timestamp = status['timestamp'] as String;
      expect(timestamp, isNotEmpty);
    });

    test('should handle refresh operation', () async {
      await service.initialize();
      final initialMode = service.currentMode;

      await service.refresh();

      // 刷新后模式应该仍然有效
      expect(service.currentMode, isNotNull);
      // 可能相同也可能不同，但应该是有效的模式
    });

    group('Git Integration Simulation', () {
      test('should simulate Git clone process', () async {
        await service.initialize();

        // 模拟Git克隆过程（不执行真实克隆）
        final toolsPath = path.join(Directory.current.path, 'tools');
        final toolsDir = Directory(toolsPath);

        // 验证可以创建tools目录
        if (!await toolsDir.exists()) {
          await toolsDir.create(recursive: true);
        }

        expect(await toolsDir.exists(), isTrue);

        // 清理
        if (await toolsDir.exists()) {
          await toolsDir.delete(recursive: true);
        }
      });

      test('should handle missing Git gracefully', () async {
        await service.initialize();

        // 即使Git不可用，服务也应该正常工作
        expect(service.currentMode, isNotNull);
        expect(service.statusDescription, isNotNull);

        // 应该能够执行基本命令
        final result = await service.executeCommand('doctor');
        expect(result, isNotNull);
        expect(result.success, isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle invalid commands gracefully', () async {
        await service.initialize();

        final result = await service.executeCommand('invalid-command-xyz');

        expect(result, isNotNull);
        expect(result.mode, isNotNull);
        // 即使是无效命令，也应该有响应
      });

      test('should handle empty commands', () async {
        await service.initialize();

        final result = await service.executeCommand('');

        expect(result, isNotNull);
        expect(result.mode, isNotNull);
      });
    });
  });
}
