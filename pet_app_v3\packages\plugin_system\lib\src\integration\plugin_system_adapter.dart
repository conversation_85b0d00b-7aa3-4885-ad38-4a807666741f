/*
---------------------------------------------------------------
File name:          plugin_system_adapter.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Plugin System模块的适应层实现
---------------------------------------------------------------
Change History:
    2025-07-29: 插件系统模块适应层实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:path/path.dart' as path;

import '../core/plugin_registry.dart';
import '../core/plugin_loader.dart';
import '../core/plugin_messenger.dart';
import '../core/event_bus.dart';
import '../core/plugin_publisher.dart';
import '../core/plugin_downloader.dart';
import '../core/plugin.dart';
import '../core/plugin_manifest.dart';

import 'module_interfaces.dart';

/// Plugin System 模块适应层
///
/// 将Plugin System的核心功能封装为标准的模块接口，
/// 便于与其他模块和主应用进行集成。
class PluginSystemAdapter implements IModuleAdapter {
  /// 单例实例
  static PluginSystemAdapter? _instance;

  /// 获取单例实例
  static PluginSystemAdapter get instance {
    _instance ??= PluginSystemAdapter._();
    return _instance!;
  }

  /// 私有构造函数
  PluginSystemAdapter._();

  /// 模块协调器
  IModuleCoordinator? _coordinator;

  /// 当前状态
  ModuleStatus _status = ModuleStatus.uninitialized;

  /// 状态控制器
  final StreamController<ModuleStatus> _statusController =
      StreamController<ModuleStatus>.broadcast();

  /// 请求处理器映射
  final Map<String, RequestHandler> _requestHandlers = {};

  /// 事件处理器映射
  final Map<String, List<EventHandler>> _eventHandlers = {};

  /// 是否已初始化
  bool _initialized = false;

  @override
  ModuleMetadata get metadata => const ModuleMetadata(
        id: 'plugin_system',
        name: 'Plugin System',
        version: '1.4.0',
        description: 'Pet App V3 核心插件化框架，实现万物皆插件的设计理念',
        author: 'Pet App Team',
        dependencies: [],
        providedServices: [
          'plugin_registry',
          'plugin_loader',
          'plugin_messenger',
          'plugin_publisher',
          'plugin_downloader',
          'event_bus',
        ],
        requiredServices: [],
      );

  @override
  ModuleStatus get status => _status;

  @override
  Stream<ModuleStatus> get statusStream => _statusController.stream;

  @override
  Future<void> initialize(IModuleCoordinator coordinator) async {
    if (_initialized) {
      developer.log('Plugin System 已经初始化', name: 'PluginSystemAdapter');
      return;
    }

    try {
      _updateStatus(ModuleStatus.initializing);
      _coordinator = coordinator;

      developer.log('开始初始化 Plugin System', name: 'PluginSystemAdapter');

      // 1. 初始化核心组件
      await _initializeCoreComponents();

      // 2. 注册标准请求处理器
      _registerStandardHandlers();

      // 3. 设置事件监听
      _setupEventListeners();

      _initialized = true;
      _updateStatus(ModuleStatus.initialized);

      developer.log('Plugin System 初始化完成', name: 'PluginSystemAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Plugin System 初始化失败: $e',
        name: 'PluginSystemAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_status != ModuleStatus.initialized) {
      throw StateError('模块必须先初始化才能启动');
    }

    try {
      _updateStatus(ModuleStatus.starting);

      developer.log('启动 Plugin System', name: 'PluginSystemAdapter');

      // 启动核心服务
      await _startCoreServices();

      _updateStatus(ModuleStatus.running);

      developer.log('Plugin System 启动完成', name: 'PluginSystemAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Plugin System 启动失败: $e',
        name: 'PluginSystemAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_status != ModuleStatus.running) {
      developer.log('Plugin System 未在运行状态', name: 'PluginSystemAdapter');
      return;
    }

    try {
      _updateStatus(ModuleStatus.stopping);

      developer.log('停止 Plugin System', name: 'PluginSystemAdapter');

      // 停止核心服务
      await _stopCoreServices();

      _updateStatus(ModuleStatus.stopped);

      developer.log('Plugin System 停止完成', name: 'PluginSystemAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Plugin System 停止失败: $e',
        name: 'PluginSystemAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      developer.log('销毁 Plugin System', name: 'PluginSystemAdapter');

      // 停止模块（如果正在运行）
      if (_status == ModuleStatus.running) {
        await stop();
      }

      // 清理资源
      _requestHandlers.clear();
      _eventHandlers.clear();
      await _statusController.close();

      _initialized = false;
      _coordinator = null;

      developer.log('Plugin System 销毁完成', name: 'PluginSystemAdapter');
    } catch (e, stackTrace) {
      developer.log(
        'Plugin System 销毁失败: $e',
        name: 'PluginSystemAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> handleRequest(
    String action,
    Map<String, dynamic> data,
    String senderId,
  ) async {
    final handler = _requestHandlers[action];
    if (handler == null) {
      developer.log(
        '未找到请求处理器: $action',
        name: 'PluginSystemAdapter',
      );
      return null;
    }

    try {
      return await handler(data, senderId);
    } catch (e, stackTrace) {
      developer.log(
        '处理请求失败: $action, 错误: $e',
        name: 'PluginSystemAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  @override
  void registerRequestHandler(String action, RequestHandler handler) {
    _requestHandlers[action] = handler;
    developer.log('注册请求处理器: $action', name: 'PluginSystemAdapter');
  }

  @override
  void registerEventHandler(String event, EventHandler handler) {
    _eventHandlers.putIfAbsent(event, () => []).add(handler);
    developer.log('注册事件处理器: $event', name: 'PluginSystemAdapter');
  }

  @override
  Future<Map<String, dynamic>> getHealthStatus() async {
    try {
      final registry = PluginRegistry.instance;
      final loader = PluginLoader.instance;
      final messenger = PluginMessenger.instance;

      return {
        'status': _status.name,
        'initialized': _initialized,
        'pluginCount': registry.getAllPlugins().length,
        'activePlugins': registry
            .getAllPlugins()
            .where((p) => registry.getState(p.id) == PluginState.started)
            .length,
        'loaderStatus': loader.getStatus(),
        'messengerStatus': messenger.getStatus(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 更新模块状态
  void _updateStatus(ModuleStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(newStatus);
      developer.log('状态更新: ${newStatus.name}', name: 'PluginSystemAdapter');
    }
  }

  /// 初始化核心组件
  Future<void> _initializeCoreComponents() async {
    // 确保核心组件实例化
    PluginRegistry.instance;
    PluginLoader.instance;
    PluginMessenger.instance;
    EventBus.instance;
    PluginPublisher.instance;
    PluginDownloader.instance;

    developer.log('核心组件初始化完成', name: 'PluginSystemAdapter');
  }

  /// 注册标准请求处理器
  void _registerStandardHandlers() {
    // 插件注册相关
    registerRequestHandler('register_plugin', _handleRegisterPlugin);
    registerRequestHandler('unregister_plugin', _handleUnregisterPlugin);
    registerRequestHandler('get_plugin', _handleGetPlugin);
    registerRequestHandler('list_plugins', _handleListPlugins);

    // 插件加载相关
    registerRequestHandler('load_plugin', _handleLoadPlugin);
    registerRequestHandler('unload_plugin', _handleUnloadPlugin);

    // 插件通信相关
    registerRequestHandler('send_plugin_message', _handleSendPluginMessage);

    // 插件发布相关
    registerRequestHandler('publish_plugin', _handlePublishPlugin);
    registerRequestHandler('download_plugin', _handleDownloadPlugin);

    developer.log('标准请求处理器注册完成', name: 'PluginSystemAdapter');
  }

  /// 设置事件监听
  void _setupEventListeners() {
    // 监听插件系统内部事件，转发给其他模块
    final eventBus = EventBus.instance;

    // 监听插件注册事件
    eventBus.on('plugin_registered', (event) {
      _broadcastEvent('plugin_registered', {
        'pluginId': event.data?['pluginId'],
        'timestamp': event.timestamp?.toIso8601String(),
      });
    });

    // 监听插件注销事件
    eventBus.on('plugin_unregistered', (event) {
      _broadcastEvent('plugin_unregistered', {
        'pluginId': event.data?['pluginId'],
        'timestamp': event.timestamp?.toIso8601String(),
      });
    });

    developer.log('事件监听设置完成', name: 'PluginSystemAdapter');
  }

  /// 启动核心服务
  Future<void> _startCoreServices() async {
    // 这里可以添加需要启动的服务
    developer.log('核心服务启动完成', name: 'PluginSystemAdapter');
  }

  /// 停止核心服务
  Future<void> _stopCoreServices() async {
    // 这里可以添加需要停止的服务
    developer.log('核心服务停止完成', name: 'PluginSystemAdapter');
  }

  /// 广播事件到其他模块
  void _broadcastEvent(String event, Map<String, dynamic> data) {
    _coordinator?.communication.broadcastEvent(
      event,
      data,
      excludeModules: [metadata.id],
    );
  }

  // 请求处理器实现
  Future<Map<String, dynamic>?> _handleRegisterPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      final pluginPath = data['pluginPath'] as String?;
      final pluginManifest = data['manifest'] as Map<String, dynamic>?;

      if (pluginId == null) {
        return {'success': false, 'error': 'pluginId is required'};
      }

      final registry = PluginRegistry.instance;

      // 检查插件是否已注册
      if (registry.contains(pluginId)) {
        return {
          'success': false,
          'error': 'Plugin $pluginId is already registered',
        };
      }

      // 创建插件实例
      if (pluginPath != null && pluginManifest != null) {
        // 从清单创建插件实例
        final plugin = await _createPluginFromManifest(
            pluginId, pluginPath, pluginManifest);

        // 注册插件
        await registry.register(plugin);

        // 发布插件注册事件
        final eventBus = EventBus.instance;
        eventBus.publish(
          'plugin_registered',
          'plugin_system',
          data: {
            'pluginId': pluginId,
            'timestamp': DateTime.now().toIso8601String(),
          },
        );

        return {
          'success': true,
          'message': 'Plugin registered successfully',
          'pluginId': pluginId,
          'state': registry.getState(pluginId)?.name,
        };
      } else {
        return {
          'success': false,
          'error': 'pluginPath and manifest are required for registration',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleUnregisterPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) {
        return {'success': false, 'error': 'pluginId is required'};
      }

      final registry = PluginRegistry.instance;

      if (!registry.contains(pluginId)) {
        return {
          'success': false,
          'error': 'Plugin $pluginId is not registered',
        };
      }

      await registry.unregister(pluginId);

      return {
        'success': true,
        'message': 'Plugin unregistered successfully',
        'pluginId': pluginId,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleGetPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) {
        return {'success': false, 'error': 'pluginId is required'};
      }

      final registry = PluginRegistry.instance;
      final plugin = registry.get(pluginId);

      if (plugin == null) {
        return {
          'success': false,
          'error': 'Plugin $pluginId not found',
        };
      }

      final metadata = registry.getMetadata(pluginId);
      final state = registry.getState(pluginId);

      return {
        'success': true,
        'plugin': {
          'id': plugin.id,
          'name': plugin.name,
          'version': plugin.version,
          'description': plugin.description,
          'author': plugin.author,
          'state': state?.name,
          'metadata': metadata?.toString(),
        },
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleListPlugins(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final registry = PluginRegistry.instance;
      final plugins = registry.getAllPlugins();

      final pluginList = plugins.map((plugin) {
        final metadata = registry.getMetadata(plugin.id);
        final state = registry.getState(plugin.id);

        return {
          'id': plugin.id,
          'name': plugin.name,
          'version': plugin.version,
          'description': plugin.description,
          'author': plugin.author,
          'state': state?.name,
          'category': plugin.category.name,
          'metadata': metadata?.toString(),
        };
      }).toList();

      return {
        'success': true,
        'plugins': pluginList,
        'count': pluginList.length,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleLoadPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) {
        return {'success': false, 'error': 'pluginId is required'};
      }

      final registry = PluginRegistry.instance;
      final loader = PluginLoader.instance;

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return {
          'success': false,
          'error': 'Plugin $pluginId not found',
        };
      }

      await loader.loadPlugin(plugin);

      return {
        'success': true,
        'message': 'Plugin loaded successfully',
        'pluginId': pluginId,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleUnloadPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) {
        return {'success': false, 'error': 'pluginId is required'};
      }

      final loader = PluginLoader.instance;
      await loader.unloadPlugin(pluginId);

      return {
        'success': true,
        'message': 'Plugin unloaded successfully',
        'pluginId': pluginId,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleSendPluginMessage(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final targetId = data['targetId'] as String?;
      final action = data['action'] as String?;
      final messageData = data['data'] as Map<String, dynamic>?;

      if (targetId == null || action == null) {
        return {
          'success': false,
          'error': 'targetId and action are required',
        };
      }

      final messenger = PluginMessenger.instance;
      final response = await messenger.sendMessage(
        senderId,
        targetId,
        action,
        messageData ?? {},
      );

      return {
        'success': true,
        'response': {
          'success': response.success,
          'data': response.data,
          'error': response.error,
        },
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handlePublishPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginPath = data['pluginPath'] as String?;
      if (pluginPath == null) {
        return {'success': false, 'error': 'pluginPath is required'};
      }

      // 实现实际的插件发布逻辑
      final publisher = PluginPublisher.instance;

      try {
        // 读取插件文件
        final pluginFile = File(pluginPath);
        if (!await pluginFile.exists()) {
          return {
            'success': false,
            'error': 'Plugin file not found: $pluginPath',
          };
        }

        final pluginData = await pluginFile.readAsBytes();

        // 创建发布元数据
        final metadataMap = data['metadata'] as Map<String, dynamic>? ?? {};
        final metadata = PublishMetadata(
          id: metadataMap['id'] as String? ??
              path.basenameWithoutExtension(pluginPath),
          name: metadataMap['name'] as String? ??
              path.basenameWithoutExtension(pluginPath),
          version: metadataMap['version'] as String? ?? '1.0.0',
          description:
              data['description'] as String? ?? 'Plugin published via adapter',
          author: metadataMap['author'] as String? ?? 'Unknown',
          keywords: (data['tags'] as List<dynamic>?)?.cast<String>() ?? [],
          category: metadataMap['category'] as String?,
          permissions:
              (metadataMap['permissions'] as List<dynamic>?)?.cast<String>() ??
                  [],
          platforms:
              (metadataMap['platforms'] as List<dynamic>?)?.cast<String>() ??
                  ['android', 'ios'],
        );

        // 发布插件
        final publishResult = await publisher.publishPlugin(
          pluginId: metadata.id,
          metadata: metadata,
          pluginData: pluginData,
        );

        if (publishResult.success) {
          return {
            'success': true,
            'message': 'Plugin published successfully',
            'publishId': publishResult.publishId,
            'version': publishResult.version,
            'downloadUrl': publishResult.downloadUrl,
          };
        } else {
          return {
            'success': false,
            'error': publishResult.error ?? 'Unknown publish error',
          };
        }
      } catch (e) {
        return {
          'success': false,
          'error': 'Plugin publish failed: $e',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleDownloadPlugin(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final pluginId = data['pluginId'] as String?;
      final downloadUrl = data['downloadUrl'] as String?;

      if (pluginId == null || downloadUrl == null) {
        return {
          'success': false,
          'error': 'pluginId and downloadUrl are required',
        };
      }

      // 实现实际的插件下载逻辑
      final downloader = PluginDownloader.instance;

      try {
        // 开始下载插件
        final fileName = data['fileName'] as String? ?? '$pluginId.plugin';
        final downloadResult = await downloader.downloadPlugin(
          taskId: 'download_$pluginId',
          url: downloadUrl,
          fileName: fileName,
          expectedSize: data['expectedSize'] as int?,
          checksum: data['checksum'] as String?,
        );

        if (downloadResult.success) {
          return {
            'success': true,
            'message': 'Plugin downloaded successfully',
            'pluginId': pluginId,
            'filePath': downloadResult.filePath,
            'fileSize': downloadResult.fileSize,
            'taskId': 'download_$pluginId',
          };
        } else {
          return {
            'success': false,
            'error': downloadResult.error ?? 'Unknown download error',
          };
        }
      } catch (e) {
        return {
          'success': false,
          'error': 'Plugin download failed: $e',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 从清单创建插件实例
  Future<Plugin> _createPluginFromManifest(
    String pluginId,
    String pluginPath,
    Map<String, dynamic> manifest,
  ) async {
    try {
      // 创建插件清单
      final pluginManifest = PluginManifest(
        id: pluginId,
        name: manifest['name'] as String? ?? pluginId,
        version: manifest['version'] as String? ?? '1.0.0',
        description: manifest['description'] as String? ?? '',
        author: manifest['author'] as String? ?? 'Unknown',
        category: manifest['category'] as String? ?? 'tool',
        main: manifest['main'] as String? ?? 'lib/main.dart',
        permissions:
            _parseStringList(manifest['permissions'] as List<dynamic>?),
        dependencies:
            _parseManifestDeps(manifest['dependencies'] as List<dynamic>?),
        platforms: _parseStringList(manifest['platforms'] as List<dynamic>?),
      );

      // 实现实际的插件实例创建逻辑
      // 根据清单创建具体的插件实例
      developer.log('Plugin manifest created: ${pluginManifest.id}',
          name: 'PluginSystemAdapter');

      // 创建插件实例
      // 实现完整的插件实例创建逻辑，包括动态加载插件代码
      final plugin = _createBasicPluginFromManifest(pluginManifest, pluginPath);

      // 使用PluginLoader加载插件到系统中
      final loader = PluginLoader.instance;
      try {
        await loader.loadPlugin(plugin);
        developer.log('Plugin loaded successfully: ${plugin.id}',
            name: 'PluginSystemAdapter');
        return plugin;
      } catch (e) {
        developer.log('Failed to load plugin: $e', name: 'PluginSystemAdapter');
        // 即使加载失败，也返回插件实例，但标记为未加载状态
        return plugin;
      }
    } catch (e) {
      throw Exception('Failed to create plugin from manifest: $e');
    }
  }

  /// 解析字符串列表
  List<String> _parseStringList(List<dynamic>? list) {
    if (list == null) return [];

    return list.where((item) => item is String).cast<String>().toList();
  }

  /// 解析清单依赖列表
  List<PluginManifestDependency> _parseManifestDeps(
      List<dynamic>? dependenciesList) {
    if (dependenciesList == null) return [];

    return dependenciesList
        .where((item) => item is Map<String, dynamic>)
        .cast<Map<String, dynamic>>()
        .map((dep) => PluginManifestDependency.fromMap(dep))
        .toList();
  }

  /// 从清单创建基础插件实例
  Plugin _createBasicPluginFromManifest(
    PluginManifest manifest,
    String pluginPath,
  ) {
    // 创建基础插件实现
    return _BasicPluginImpl(
      pluginId: manifest.id,
      pluginName: manifest.name,
      pluginVersion: manifest.version,
      pluginDescription: manifest.description,
      pluginAuthor: manifest.author,
      pluginCategory: manifest.category,
      pluginPath: pluginPath,
      pluginPermissions: manifest.permissions,
      pluginPlatforms: manifest.platforms,
    );
  }
}

/// 基础插件实现
class _BasicPluginImpl extends Plugin {
  final String pluginId;
  final String pluginName;
  final String pluginVersion;
  final String pluginDescription;
  final String pluginAuthor;
  final String pluginCategory;
  final String pluginPath;
  final List<String> pluginPermissions;
  final List<String> pluginPlatforms;

  _BasicPluginImpl({
    required this.pluginId,
    required this.pluginName,
    required this.pluginVersion,
    required this.pluginDescription,
    required this.pluginAuthor,
    required this.pluginCategory,
    required this.pluginPath,
    required this.pluginPermissions,
    required this.pluginPlatforms,
  });

  @override
  String get id => pluginId;

  @override
  String get name => pluginName;

  @override
  String get version => pluginVersion;

  @override
  String get description => pluginDescription;

  @override
  String get author => pluginAuthor;

  @override
  Future<void> initialize() async {
    developer.log('Initializing basic plugin: $name', name: 'BasicPluginImpl');
    // 基础初始化逻辑
  }

  @override
  Future<void> dispose() async {
    developer.log('Disposing basic plugin: $name', name: 'BasicPluginImpl');
    // 基础清理逻辑
  }

  @override
  Future<void> start() async {
    developer.log('Starting basic plugin: $name', name: 'BasicPluginImpl');
    // 基础启动逻辑
  }

  @override
  Future<void> stop() async {
    developer.log('Stopping basic plugin: $name', name: 'BasicPluginImpl');
    // 基础停止逻辑
  }

  @override
  Future<void> pause() async {
    developer.log('Pausing basic plugin: $name', name: 'BasicPluginImpl');
    // 基础暂停逻辑
  }

  @override
  Future<void> resume() async {
    developer.log('Resuming basic plugin: $name', name: 'BasicPluginImpl');
    // 基础恢复逻辑
  }

  @override
  PluginType get category {
    // 根据字符串类型转换为PluginType枚举
    switch (pluginCategory.toLowerCase()) {
      case 'tool':
        return PluginType.tool;
      case 'game':
        return PluginType.game;
      case 'ui':
        return PluginType.ui;
      case 'service':
        return PluginType.service;
      case 'theme':
        return PluginType.theme;
      case 'widget':
        return PluginType.widget;
      case 'system':
        return PluginType.system;
      default:
        return PluginType.tool;
    }
  }

  @override
  List<PluginPermission> get requiredPermissions {
    // 将字符串权限转换为PluginPermission枚举
    return pluginPermissions.map((permission) {
      switch (permission.toLowerCase()) {
        case 'file_system':
        case 'filesystem':
        case 'storage':
          return PluginPermission.fileSystem;
        case 'network':
          return PluginPermission.network;
        case 'camera':
          return PluginPermission.camera;
        case 'microphone':
          return PluginPermission.microphone;
        case 'location':
          return PluginPermission.location;
        case 'notifications':
          return PluginPermission.notifications;
        case 'contacts':
          return PluginPermission.contacts;
        case 'calendar':
          return PluginPermission.calendar;
        default:
          return PluginPermission.fileSystem; // 默认权限
      }
    }).toList();
  }

  @override
  List<PluginDependency> get dependencies => [];

  @override
  List<SupportedPlatform> get supportedPlatforms {
    // 将字符串平台转换为SupportedPlatform枚举
    return pluginPlatforms.map((platform) {
      switch (platform.toLowerCase()) {
        case 'android':
          return SupportedPlatform.android;
        case 'ios':
          return SupportedPlatform.ios;
        case 'windows':
          return SupportedPlatform.windows;
        case 'macos':
          return SupportedPlatform.macos;
        case 'linux':
          return SupportedPlatform.linux;
        case 'web':
          return SupportedPlatform.web;
        default:
          return SupportedPlatform.android; // 默认平台
      }
    }).toList();
  }

  @override
  Object? getConfigWidget() => null;

  @override
  Object getMainWidget() => 'Plugin: $name ($id)';

  @override
  Future<dynamic> handleMessage(
    String action,
    Map<String, dynamic> data,
  ) async {
    // 基础实现只处理基本消息
    switch (action) {
      case 'ping':
        return <String, dynamic>{'status': 'pong', 'pluginId': id};
      case 'getInfo':
        return <String, dynamic>{
          'id': id,
          'name': name,
          'version': version,
          'category': category.toString(),
          'author': author,
          'description': description,
        };
      default:
        return <String, dynamic>{'error': 'Unknown action: $action'};
    }
  }

  @override
  PluginState get currentState => PluginState.loaded;

  @override
  Stream<PluginState> get stateChanges => Stream.value(PluginState.loaded);

  @override
  PluginManifest get manifest => PluginManifest(
        id: pluginId,
        name: pluginName,
        version: pluginVersion,
        description: pluginDescription,
        author: pluginAuthor,
        category: pluginCategory,
        main: 'lib/main.dart',
        permissions: pluginPermissions,
        dependencies: [],
        platforms: pluginPlatforms,
      );

  @override
  bool get isEnabled => true;

  @override
  Duration? get loadTime => null;
}

/// 插件注册事件
class PluginRegisteredEvent {
  const PluginRegisteredEvent(this.pluginId);
  final String pluginId;
}

/// 插件注销事件
class PluginUnregisteredEvent {
  const PluginUnregisteredEvent(this.pluginId);
  final String pluginId;
}
