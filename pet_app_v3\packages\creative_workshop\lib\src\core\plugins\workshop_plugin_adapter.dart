/*
---------------------------------------------------------------
File name:          workshop_plugin_adapter.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop插件到Plugin System的适配器
---------------------------------------------------------------
Change History:
    2025-07-29: P0.2.2 - 创建WorkshopPluginAdapter，实现插件接口适配;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:creative_workshop/creative_workshop.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

/// Creative Workshop插件到Plugin System的适配器
///
/// 将Creative Workshop的插件信息适配为Plugin System的Plugin接口
class WorkshopPluginAdapter implements plugin_sys.Plugin {
  /// 构造函数
  WorkshopPluginAdapter({
    required plugin_sys.PluginManifest manifest,
    required this.workshopInfo,
  })  : _manifest = manifest,
        _stateController = StreamController<plugin_sys.PluginState>.broadcast();

  final plugin_sys.PluginManifest _manifest;
  final PluginInstallInfo workshopInfo;
  final StreamController<plugin_sys.PluginState> _stateController;

  plugin_sys.PluginState _currentState = plugin_sys.PluginState.loaded;
  Duration? _loadTime;
  bool _isDisposed = false;

  @override
  String get id => _manifest.id;

  @override
  String get name => _manifest.name;

  @override
  String get version => _manifest.version;

  @override
  String get description => _manifest.description;

  @override
  String get author => _manifest.author;

  @override
  plugin_sys.PluginType get category => _convertCategory(_manifest.category);

  @override
  List<plugin_sys.PluginPermission> get requiredPermissions =>
      _manifest.permissions.map(_convertPermission).toList();

  @override
  List<plugin_sys.PluginDependency> get dependencies => _manifest.dependencies
      .map<plugin_sys.PluginDependency>(_convertDependency)
      .toList();

  @override
  List<plugin_sys.SupportedPlatform> get supportedPlatforms =>
      _manifest.platforms
          .map<plugin_sys.SupportedPlatform>(_convertPlatform)
          .toList();

  @override
  plugin_sys.PluginState get currentState => _currentState;

  @override
  Stream<plugin_sys.PluginState> get stateChanges => _stateController.stream;

  @override
  plugin_sys.PluginManifest get manifest => _manifest;

  @override
  bool get isEnabled => _currentState == plugin_sys.PluginState.started;

  @override
  Duration? get loadTime => _loadTime;

  @override
  Future<void> initialize() async {
    _updateState(plugin_sys.PluginState.loaded);
    try {
      // 初始化逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));
      _updateState(plugin_sys.PluginState.initialized);
    } on Exception {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    _updateState(plugin_sys.PluginState.initialized);
    try {
      final DateTime startTime = DateTime.now();
      // 启动逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));
      _loadTime = DateTime.now().difference(startTime);
      _updateState(plugin_sys.PluginState.started);
    } on Exception {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    _updateState(plugin_sys.PluginState.paused);
  }

  @override
  Future<void> resume() async {
    _updateState(plugin_sys.PluginState.started);
  }

  @override
  Future<void> stop() async {
    _updateState(plugin_sys.PluginState.stopped);
    try {
      // 停止逻辑
      await Future<void>.delayed(const Duration(milliseconds: 50));
      _updateState(plugin_sys.PluginState.stopped);
    } on Exception {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    if (_isDisposed) {
      return;
    }

    _isDisposed = true;
    _currentState = plugin_sys.PluginState.unloaded;

    try {
      // 清理逻辑
      await _stateController.close();
    } on Exception {
      _currentState = plugin_sys.PluginState.error;
      rethrow;
    }
  }

  @override
  Object? getConfigWidget() => null;

  @override
  Object getMainWidget() => 'Workshop Plugin: $name ($id)';

  @override
  Future<dynamic> handleMessage(
    String action,
    Map<String, dynamic> data,
  ) async {
    // 处理插件间消息
    switch (action) {
      case 'ping':
        return <String, dynamic>{'status': 'pong', 'pluginId': id};
      case 'getInfo':
        return <String, dynamic>{
          'id': id,
          'name': name,
          'version': version,
          'state': currentState.toString(),
          'workshopInfo': <String, dynamic>{
            'installedAt': workshopInfo.installedAt.toIso8601String(),
            'size': workshopInfo.size,
            'autoUpdate': workshopInfo.autoUpdate,
          },
        };
      default:
        return <String, dynamic>{'error': 'Unknown action: $action'};
    }
  }

  /// 更新插件状态
  void _updateState(plugin_sys.PluginState newState) {
    if (_isDisposed) {
      return;
    }

    _currentState = newState;
    if (!_stateController.isClosed) {
      _stateController.add(newState);
    }
  }

  /// 转换插件类型
  plugin_sys.PluginType _convertCategory(String category) {
    switch (category.toLowerCase()) {
      case 'tool':
        return plugin_sys.PluginType.tool;
      case 'game':
        return plugin_sys.PluginType.game;
      case 'theme':
        return plugin_sys.PluginType.theme;
      case 'service':
        return plugin_sys.PluginType.service;
      case 'widget':
        return plugin_sys.PluginType.widget;
      default:
        return plugin_sys.PluginType.tool;
    }
  }

  /// 转换权限
  plugin_sys.PluginPermission _convertPermission(String permission) {
    switch (permission.toLowerCase()) {
      case 'filesystem':
      case 'file_system':
        return plugin_sys.PluginPermission.fileSystem;
      case 'network':
        return plugin_sys.PluginPermission.network;
      case 'camera':
        return plugin_sys.PluginPermission.camera;
      case 'microphone':
        return plugin_sys.PluginPermission.microphone;
      case 'location':
        return plugin_sys.PluginPermission.location;
      case 'clipboard':
        return plugin_sys.PluginPermission.clipboard;
      case 'notifications':
        return plugin_sys.PluginPermission.notifications;
      case 'contacts':
        return plugin_sys.PluginPermission.contacts;
      case 'calendar':
        return plugin_sys.PluginPermission.calendar;
      case 'photos':
        return plugin_sys.PluginPermission.photos;
      case 'systemsettings':
      case 'system_settings':
        return plugin_sys.PluginPermission.systemSettings;
      case 'backgroundexecution':
      case 'background_execution':
        return plugin_sys.PluginPermission.backgroundExecution;
      case 'deviceinfo':
      case 'device_info':
        return plugin_sys.PluginPermission.deviceInfo;
      default:
        return plugin_sys.PluginPermission.fileSystem;
    }
  }

  /// 转换依赖
  plugin_sys.PluginDependency _convertDependency(
    plugin_sys.PluginManifestDependency dep,
  ) =>
      plugin_sys.PluginDependency(
        pluginId: dep.id,
        versionConstraint: dep.version,
        optional: !dep.required,
      );

  /// 转换平台
  plugin_sys.SupportedPlatform _convertPlatform(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return plugin_sys.SupportedPlatform.android;
      case 'ios':
        return plugin_sys.SupportedPlatform.ios;
      case 'windows':
        return plugin_sys.SupportedPlatform.windows;
      case 'macos':
        return plugin_sys.SupportedPlatform.macos;
      case 'linux':
        return plugin_sys.SupportedPlatform.linux;
      case 'web':
        return plugin_sys.SupportedPlatform.web;
      default:
        return plugin_sys.SupportedPlatform.android;
    }
  }
}
