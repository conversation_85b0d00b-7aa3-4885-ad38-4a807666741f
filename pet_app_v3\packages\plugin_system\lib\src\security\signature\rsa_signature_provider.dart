/*
---------------------------------------------------------------
File name:          rsa_signature_provider.dart
Author:             lgnorant-lu
Date created:       2025-07-28
Last modified:      2025-07-28
Dart Version:       3.2+
Description:        RSA数字签名提供者实现
---------------------------------------------------------------
Change History:
    2025-07-28:     初始实现RSA数字签名提供者;
*/

import 'dart:math';
import 'dart:typed_data';

import 'package:crypto/crypto.dart' as crypto;
import 'package:pointycastle/export.dart';

import 'package:plugin_system/src/core/plugin_exceptions.dart';
import 'package:plugin_system/src/security/signature/plugin_signature_core.dart';

/// 模拟的安全随机数生成器实现
class _MockSecureRandomImpl implements SecureRandom {
  _MockSecureRandomImpl(this._random);

  final Random _random;

  @override
  String get algorithmName => 'Mock';

  @override
  int nextUint8() => _random.nextInt(256);

  @override
  int nextUint16() => _random.nextInt(65536);

  @override
  int nextUint32() => _random.nextInt(**********);

  @override
  BigInt nextBigInteger(int bitLength) {
    final bytes = Uint8List((bitLength + 7) ~/ 8);
    for (int i = 0; i < bytes.length; i++) {
      bytes[i] = nextUint8();
    }
    return BigInt.parse(
        bytes.map((int b) => b.toRadixString(16).padLeft(2, '0')).join(),
        radix: 16,);
  }

  @override
  void seed(CipherParameters params) {
    // 忽略种子设置
  }

  @override
  Uint8List nextBytes(int count) {
    final Uint8List bytes = Uint8List(count);
    for (int i = 0; i < count; i++) {
      bytes[i] = nextUint8();
    }
    return bytes;
  }
}

/// RSA签名提供者
class RSASignatureProvider implements SignatureProvider {
  /// 构造函数
  RSASignatureProvider() {
    _initializeSecureRandom();
  }

  @override
  PluginSignatureAlgorithm get algorithm => PluginSignatureAlgorithm.rsa2048;

  /// RSA密钥对缓存
  final Map<String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>> _keyPairs =
      <String, AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>>{};

  /// 安全随机数生成器
  late final SecureRandom _secureRandom;

  /// 初始化安全随机数生成器
  void _initializeSecureRandom() {
    // 使用简单的随机数生成器避免注册问题
    final random = Random.secure();
    _secureRandom = _MockSecureRandomImpl(random);
  }

  @override
  Future<Uint8List> generateSignature(
    Uint8List data,
    String? privateKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取或生成RSA密钥对
      final keyPair = await _getKeyPair(privateKeyPath);

      // 创建RSA签名器
      final signer = RSASigner(SHA256Digest(), '0609608648016503040201');
      signer.init(true, PrivateKeyParameter<RSAPrivateKey>(keyPair.privateKey));

      // 生成签名
      final signature =
          signer.generateSignature(Uint8List.fromList(hash.bytes));
      return signature.bytes;
    } catch (e) {
      throw PluginSignatureException(
        'RSA signature generation failed: $e',
      );
    }
  }

  @override
  Future<bool> verifySignature(
    Uint8List signature,
    Uint8List data,
    String? publicKeyPath,
  ) async {
    try {
      // 计算数据哈希
      final hash = crypto.sha256.convert(data);

      // 获取RSA密钥对（这里需要公钥）
      final keyPair = await _getKeyPair(publicKeyPath);

      // 创建RSA验证器
      final verifier = RSASigner(SHA256Digest(), '0609608648016503040201');
      verifier.init(false, PublicKeyParameter<RSAPublicKey>(keyPair.publicKey));

      // 验证签名
      final rsaSignature = RSASignature(signature);
      return verifier.verifySignature(
          Uint8List.fromList(hash.bytes), rsaSignature,);
    } catch (e) {
      return false;
    }
  }

  @override
  Future<void> generateKeyPair(String keyPath) async {
    try {
      // 生成新的RSA密钥对
      final keyGen = RSAKeyGenerator();
      keyGen.init(ParametersWithRandom(
        RSAKeyGeneratorParameters(BigInt.parse('65537'), 2048, 64),
        _secureRandom,
      ),);

      final keyPair = keyGen.generateKeyPair();
      final rsaKeyPair = AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>(
        keyPair.publicKey as RSAPublicKey,
        keyPair.privateKey as RSAPrivateKey,
      );

      // 缓存密钥对
      _keyPairs[keyPath] = rsaKeyPair;
    } catch (e) {
      throw PluginSignatureException(
        'RSA key pair generation failed: $e',
      );
    }
  }

  /// 获取或生成RSA密钥对
  Future<AsymmetricKeyPair<RSAPublicKey, RSAPrivateKey>> _getKeyPair(
    String? keyPath,
  ) async {
    final keyId = keyPath ?? 'default';

    // 检查缓存
    if (_keyPairs.containsKey(keyId)) {
      return _keyPairs[keyId]!;
    }

    // 生成新的密钥对
    await generateKeyPair(keyId);
    return _keyPairs[keyId]!;
  }

  /// 清理缓存
  void clearCache() {
    _keyPairs.clear();
  }

  /// 获取公钥信息
  Future<RSAPublicKey?> getPublicKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.publicKey;
    } catch (e) {
      return null;
    }
  }

  /// 获取私钥信息
  Future<RSAPrivateKey?> getPrivateKey(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      return keyPair.privateKey;
    } catch (e) {
      return null;
    }
  }

  /// 导出公钥为PEM格式
  /// TODO(rsa_signature): 实现PEM格式导出
  Future<String> exportPublicKeyPEM(String? keyPath) async {
    final publicKey = await getPublicKey(keyPath);
    if (publicKey == null) {
      throw const PluginSignatureException('Public key not found');
    }

    // TODO: 实现真实的PEM格式导出
    return 'TODO: Implement PEM export for RSA public key';
  }

  /// 导入公钥从PEM格式
  /// TODO(rsa_signature): 实现PEM格式导入
  Future<void> importPublicKeyPEM(String keyPath, String pemData) async {
    // TODO: 实现真实的PEM格式导入
    throw const PluginSignatureException('PEM import not implemented yet');
  }

  /// 验证密钥对是否匹配
  Future<bool> verifyKeyPairMatch(String? keyPath) async {
    try {
      // 使用测试数据验证密钥对是否匹配
      final testData = Uint8List.fromList('test'.codeUnits);
      final signature = await generateSignature(testData, keyPath);
      return await verifySignature(signature, testData, keyPath);
    } catch (e) {
      return false;
    }
  }

  /// 获取密钥信息
  Future<Map<String, dynamic>> getKeyInfo(String? keyPath) async {
    try {
      final keyPair = await _getKeyPair(keyPath);
      final publicKey = keyPair.publicKey;

      return <String, dynamic>{
        'algorithm': 'RSA',
        'keySize': publicKey.modulus!.bitLength,
        'exponent': publicKey.exponent.toString(),
        'modulus': publicKey.modulus.toString(),
        'created': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return <String, dynamic>{
        'error': e.toString(),
      };
    }
  }
}
