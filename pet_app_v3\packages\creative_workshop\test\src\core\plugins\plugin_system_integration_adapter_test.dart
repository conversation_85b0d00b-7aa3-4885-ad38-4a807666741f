/*
---------------------------------------------------------------
File name:          plugin_system_integration_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        PluginSystemIntegrationAdapter 测试文件
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:creative_workshop/src/core/plugins/plugin_manager.dart';
import 'package:creative_workshop/src/core/plugins/plugin_system_integration_adapter.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('PluginSystemIntegrationAdapter Tests', () {
    late PluginSystemIntegrationAdapter adapter;

    setUp(() {
      adapter = PluginSystemIntegrationAdapter.instance;
    });

    tearDown(() async {
      await adapter.dispose();
    });

    test('should be singleton', () {
      final PluginSystemIntegrationAdapter adapter1 =
          PluginSystemIntegrationAdapter.instance;
      final PluginSystemIntegrationAdapter adapter2 =
          PluginSystemIntegrationAdapter.instance;
      expect(adapter1, same(adapter2));
    });

    test('should provide event stream', () {
      final events = adapter.events;
      expect(events, isA<Stream<PluginIntegrationEvent>>());
    });

    group('Plugin Integration Events', () {
      test('PluginIntegrationEvent.installed should create correct event', () {
        final DateTime now = DateTime.now();
        final PluginIntegrationEvent event = PluginIntegrationEvent.installed(
          pluginId: 'test_plugin',
          source: IntegrationSource.workshop,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.installed);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.workshop);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent.enabled should create correct event', () {
        final DateTime now = DateTime.now();
        final PluginIntegrationEvent event = PluginIntegrationEvent.enabled(
          pluginId: 'test_plugin',
          source: IntegrationSource.system,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.enabled);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.system);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent.disabled should create correct event', () {
        final DateTime now = DateTime.now();
        final PluginIntegrationEvent event = PluginIntegrationEvent.disabled(
          pluginId: 'test_plugin',
          source: IntegrationSource.adapter,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.disabled);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.adapter);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent.uninstalled should create correct event',
          () {
        final DateTime now = DateTime.now();
        final PluginIntegrationEvent event = PluginIntegrationEvent.uninstalled(
          pluginId: 'test_plugin',
          source: IntegrationSource.workshop,
          timestamp: now,
        );

        expect(event.type, PluginIntegrationEventType.uninstalled);
        expect(event.pluginId, 'test_plugin');
        expect(event.source, IntegrationSource.workshop);
        expect(event.timestamp, now);
      });

      test('PluginIntegrationEvent toString should work correctly', () {
        final DateTime now = DateTime.now();
        final PluginIntegrationEvent event = PluginIntegrationEvent.installed(
          pluginId: 'test_plugin',
          source: IntegrationSource.workshop,
          timestamp: now,
        );

        final String eventString = event.toString();
        expect(eventString, contains('PluginIntegrationEvent'));
        expect(eventString, contains('test_plugin'));
        expect(eventString, contains('workshop'));
        expect(eventString, contains('installed'));
      });
    });

    group('Enums', () {
      test('PluginIntegrationEventType should have correct values', () {
        expect(PluginIntegrationEventType.installed.name, 'installed');
        expect(PluginIntegrationEventType.enabled.name, 'enabled');
        expect(PluginIntegrationEventType.disabled.name, 'disabled');
        expect(PluginIntegrationEventType.uninstalled.name, 'uninstalled');
        expect(PluginIntegrationEventType.synchronized.name, 'synchronized');
        expect(PluginIntegrationEventType.error.name, 'error');
      });

      test('IntegrationSource should have correct values', () {
        expect(IntegrationSource.workshop.name, 'workshop');
        expect(IntegrationSource.system.name, 'system');
        expect(IntegrationSource.adapter.name, 'adapter');
      });
    });

    group('Error Handling', () {
      test('should throw StateError when not initialized', () {
        final PluginSystemIntegrationAdapter newAdapter =
            PluginSystemIntegrationAdapter.instance;

        expect(
          () => newAdapter.getIntegrationStatistics(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle plugin installation failure gracefully', () async {
        await adapter.initialize();

        final PluginOperationResult result =
            await adapter.installPluginWithSystemIntegration(
          'non_existent_plugin',
        );

        expect(result.success, isFalse);
        expect(result.error, isNotNull);
      });

      test('should handle plugin enabling failure gracefully', () async {
        await adapter.initialize();

        final PluginOperationResult result =
            await adapter.enablePluginWithSystemIntegration(
          'non_existent_plugin',
        );

        expect(result.success, isFalse);
        expect(result.error, isNotNull);
      });

      test('should handle plugin disabling failure gracefully', () async {
        await adapter.initialize();

        final PluginOperationResult result =
            await adapter.disablePluginWithSystemIntegration(
          'non_existent_plugin',
        );

        expect(result.success, isFalse);
        expect(result.error, isNotNull);
      });

      test('should handle plugin uninstallation failure gracefully', () async {
        await adapter.initialize();

        final PluginOperationResult result =
            await adapter.uninstallPluginWithSystemIntegration(
          'non_existent_plugin',
        );

        expect(result.success, isFalse);
        expect(result.error, isNotNull);
      });
    });

    group('Resource Management', () {
      test('should dispose resources properly', () async {
        await adapter.initialize();
        await adapter.dispose();

        // 验证资源已清理
        expect(
          () => adapter.getIntegrationStatistics(),
          throwsA(isA<StateError>()),
        );
      });

      test('should handle multiple dispose calls', () async {
        await adapter.initialize();
        await adapter.dispose();
        await adapter.dispose(); // 第二次调用不应该出错
      });
    });
  });
}
