/*
---------------------------------------------------------------
File name:          config_workflow_coordinator_simple_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        ConfigWorkflowCoordinator 简化测试文件
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:communication_system/src/core/config_workflow_coordinator.dart';

void main() {
  group('ConfigWorkflowCoordinator Simple Tests', () {
    late ConfigWorkflowCoordinator coordinator;

    setUp(() {
      coordinator = ConfigWorkflowCoordinator.instance;
    });

    test('should create instance successfully', () {
      expect(coordinator, isNotNull);
      expect(coordinator, same(ConfigWorkflowCoordinator.instance));
    });

    test('should handle empty config changes', () async {
      final result = await coordinator.startConfigWorkflow(
        sourceModule: 'test_source',
        configChanges: {},
        targetModules: ['test_target'],
      );

      expect(result.isSuccess, isFalse);
      expect(result.error, contains('配置验证失败'));
    });

    test('should handle non-existent source module', () async {
      final result = await coordinator.startConfigWorkflow(
        sourceModule: 'non_existent',
        configChanges: {'test': 'value'},
        targetModules: ['test_target'],
      );

      expect(result.isSuccess, isFalse);
      expect(result.error, contains('源模块未注册'));
    });

    test('should track workflow status', () async {
      final configChanges = {'test': 'value'};

      final result = await coordinator.startConfigWorkflow(
        sourceModule: 'test_source',
        configChanges: configChanges,
        targetModules: ['test_target'],
      );

      expect(result.workflowId, isNotEmpty);
      expect(result.status, isA<ConfigWorkflowStatus>());
    });

    test('should maintain workflow history', () async {
      final configChanges = {'test': 'value'};

      await coordinator.startConfigWorkflow(
        sourceModule: 'test_source',
        configChanges: configChanges,
        targetModules: ['test_target'],
      );

      final history = coordinator.getWorkflowHistory();
      expect(history, isA<List<ConfigWorkflowResult>>());
    });

    test('should cleanup expired workflows', () {
      // 验证清理功能不会崩溃
      expect(() => coordinator.cleanupExpiredWorkflows(), returnsNormally);
    });
  });
}
