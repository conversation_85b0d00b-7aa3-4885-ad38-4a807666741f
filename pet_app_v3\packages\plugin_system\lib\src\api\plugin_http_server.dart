/*
---------------------------------------------------------------
File name:          plugin_http_server.dart
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件系统HTTP服务器管理模块
---------------------------------------------------------------
Change History:
    2025-07-27: 从plugin_rest_api.dart重构拆分出HTTP服务器管理功能;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:plugin_system/src/api/plugin_api_interface.dart';

/// HTTP服务器管理器
/// 
/// 负责HTTP服务器的启动、停止、请求处理等核心功能
class PluginHttpServer {
  PluginHttpServer({
    required this.config,
    required this.requestHandler,
  });

  /// 配置
  final RestApiConfig config;

  /// 请求处理器
  final Future<ApiResponse<dynamic>> Function(ApiRequest) requestHandler;

  /// HTTP服务器实例
  HttpServer? _httpServer;

  /// 是否已启动
  bool _started = false;

  /// 是否已启动
  bool get isStarted => _started;

  /// 启动HTTP服务器
  Future<void> start() async {
    if (_started) return;

    try {
      // 启动HTTP服务器
      _httpServer = await HttpServer.bind(config.host, config.port);

      // 监听请求
      _httpServer!.listen(_handleHttpRequest);

      print('Plugin HTTP Server started on ${config.host}:${config.port}');
      _started = true;
    } catch (e) {
      print('Failed to start Plugin HTTP Server: $e');
      rethrow;
    }
  }

  /// 停止HTTP服务器
  Future<void> stop() async {
    if (!_started || _httpServer == null) return;

    try {
      await _httpServer!.close();
      _httpServer = null;
      _started = false;
      print('Plugin HTTP Server stopped');
    } catch (e) {
      print('Failed to stop Plugin HTTP Server: $e');
      rethrow;
    }
  }

  /// 处理HTTP请求
  Future<void> _handleHttpRequest(HttpRequest httpRequest) async {
    try {
      // 设置CORS头
      if (config.enableCors) {
        httpRequest.response.headers.set('Access-Control-Allow-Origin', '*');
        httpRequest.response.headers.set(
            'Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS',);
        httpRequest.response.headers
            .set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      }

      // 处理OPTIONS请求
      if (httpRequest.method == 'OPTIONS') {
        httpRequest.response.statusCode = 200;
        await httpRequest.response.close();
        return;
      }

      // 解析请求
      final body = await utf8.decoder.bind(httpRequest).join();
      final Map<String, dynamic> bodyData = body.isNotEmpty
          ? jsonDecode(body) as Map<String, dynamic>
          : <String, dynamic>{};

      final ApiRequest apiRequest = ApiRequest(
        method: _parseHttpMethod(httpRequest.method),
        path: httpRequest.uri.path,
        version: const ApiVersion(major: 1, minor: 0, patch: 0),
        headers: _parseHeaders(httpRequest.headers),
        queryParameters: httpRequest.uri.queryParameters,
        body: bodyData,
      );

      // 处理请求
      final ApiResponse response = await requestHandler(apiRequest);

      // 发送响应
      await _sendResponse(httpRequest, response);
    } catch (e) {
      // 发送错误响应
      await _sendErrorResponse(httpRequest, e);
    }
  }

  /// 发送响应
  Future<void> _sendResponse(
    HttpRequest httpRequest,
    ApiResponse<dynamic> response,
  ) async {
    try {
      httpRequest.response.statusCode = response.statusCode;
      httpRequest.response.headers.contentType = ContentType.json;

      final Map<String, dynamic> responseData = <String, dynamic>{
        'status': response.status.name,
        'data': response.data,
        'message': response.message,
        'errors': response.errors,
        'metadata': response.metadata,
      };

      httpRequest.response.write(jsonEncode(responseData));
      await httpRequest.response.close();
    } catch (e) {
      print('Failed to send response: $e');
    }
  }

  /// 发送错误响应
  Future<void> _sendErrorResponse(HttpRequest httpRequest, error) async {
    try {
      httpRequest.response.statusCode = 500;
      httpRequest.response.headers.contentType = ContentType.json;

      final Map<String, dynamic> errorData = <String, dynamic>{
        'status': 'error',
        'message': 'Internal server error',
        'error': error.toString(),
      };

      httpRequest.response.write(jsonEncode(errorData));
      await httpRequest.response.close();
    } catch (e) {
      print('Failed to send error response: $e');
    }
  }

  /// 解析HTTP方法
  HttpMethod _parseHttpMethod(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return HttpMethod.get;
      case 'POST':
        return HttpMethod.post;
      case 'PUT':
        return HttpMethod.put;
      case 'DELETE':
        return HttpMethod.delete;
      case 'PATCH':
        return HttpMethod.patch;
      case 'HEAD':
        return HttpMethod.head;
      case 'OPTIONS':
        return HttpMethod.options;
      default:
        return HttpMethod.get;
    }
  }

  /// 解析请求头
  Map<String, String> _parseHeaders(HttpHeaders headers) {
    final Map<String, String> result = <String, String>{};
    headers.forEach((String name, List<String> values) {
      result[name] = values.join(', ');
    });
    return result;
  }
}

/// REST API配置
class RestApiConfig {
  const RestApiConfig({
    this.host = 'localhost',
    this.port = 8080,
    this.basePath = '/api',
    this.enableCors = true,
    this.enableAuth = false,
    this.enableRateLimit = true,
    this.rateLimitRequests = 100,
    this.rateLimitWindow = const Duration(minutes: 1),
    this.enableLogging = true,
    this.enableMetrics = true,
  });

  /// 主机地址
  final String host;

  /// 端口
  final int port;

  /// 基础路径
  final String basePath;

  /// 启用CORS
  final bool enableCors;

  /// 启用认证
  final bool enableAuth;

  /// 启用限流
  final bool enableRateLimit;

  /// 限流请求数
  final int rateLimitRequests;

  /// 限流时间窗口
  final Duration rateLimitWindow;

  /// 启用日志
  final bool enableLogging;

  /// 启用指标
  final bool enableMetrics;
}
