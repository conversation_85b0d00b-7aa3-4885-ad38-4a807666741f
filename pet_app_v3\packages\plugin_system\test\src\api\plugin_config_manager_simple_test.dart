/*
---------------------------------------------------------------
File name:          plugin_config_manager_simple_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        PluginConfigManager 简化测试文件
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/src/api/plugin_api_interface.dart';
import 'package:plugin_system/src/api/plugin_config_manager.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

void main() {
  group('PluginConfigManager Basic Tests', () {
    late PluginConfigManager configManager;
    late PluginRegistry registry;

    setUp(() {
      registry = PluginRegistry.instance;
      configManager = PluginConfigManager(registry: registry);
    });

    test('should create instance successfully', () {
      expect(configManager, isNotNull);
    });

    test('should get system config', () async {
      final config = await configManager.getSystemConfig();

      expect(config.isSuccess, isTrue);
      expect(config.data, isA<Map<String, dynamic>>());
    });

    test('should update system config', () async {
      final newConfig = <String, Object>{
        'maxPlugins': 50,
        'autoStart': false,
        'logLevel': 'debug',
      };

      final result = await configManager.updateSystemConfig(newConfig);

      expect(result.isSuccess, isTrue);
    });

    test('should handle non-existent plugin gracefully', () async {
      final config = await configManager.getPluginConfig('non_existent_plugin');

      // 当插件不存在时，应该返回错误响应
      expect(config.isSuccess, isFalse);
      expect(config.statusCode, 404);
    });
  });
}
