/*
---------------------------------------------------------------
File name:          plugin_system_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        插件系统适应层集成测试
---------------------------------------------------------------
Change History:
    2025-07-29: 插件系统适应层集成测试实现;
---------------------------------------------------------------
*/

import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/src/integration/plugin_system_adapter.dart';
import 'package:plugin_system/src/integration/module_interfaces.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';

void main() {
  group('PluginSystemAdapter Tests', () {
    late PluginSystemAdapter adapter;
    late MockModuleCoordinator coordinator;

    setUp(() {
      adapter = PluginSystemAdapter.instance;
      coordinator = MockModuleCoordinator();
    });

    tearDown(() async {
      await adapter.dispose();
    });

    group('初始化和配置', () {
      test('应该成功初始化适应层', () async {
        await adapter.initialize(coordinator);
        expect(adapter.status, equals(ModuleStatus.initialized));
      });

      test('应该提供正确的模块元数据', () {
        final metadata = adapter.metadata;
        expect(metadata.id, equals('plugin_system'));
        expect(metadata.name, isNotEmpty);
        expect(metadata.version, isNotEmpty);
      });
    });

    group('插件发布功能', () {
      test('应该处理插件发布请求', () async {
        await adapter.initialize(coordinator);

        // 创建测试插件文件
        final testFile = File('test_plugin.zip');
        await testFile.writeAsBytes([1, 2, 3, 4, 5]);

        try {
          final result = await adapter.handleRequest(
              'publish_plugin',
              {
                'pluginPath': testFile.path,
                'metadata': {
                  'id': 'test_plugin',
                  'name': 'Test Plugin',
                  'version': '1.0.0',
                  'author': 'Test Author',
                },
                'description': 'Test plugin description',
                'tags': ['test', 'example'],
              },
              'test_sender');

          expect(result, isA<Map<String, dynamic>?>());
          if (result != null) {
            expect(result['success'], isA<bool>());
          }
        } finally {
          if (await testFile.exists()) {
            await testFile.delete();
          }
        }
      });

      test('应该处理健康状态检查', () async {
        await adapter.initialize(coordinator);

        final healthStatus = await adapter.getHealthStatus();

        expect(healthStatus, isA<Map<String, dynamic>>());
        expect(healthStatus['status'], isNotNull);
      });
    });

    group('生命周期管理', () {
      test('应该正确处理启动和停止', () async {
        await adapter.initialize(coordinator);
        await adapter.start();
        expect(adapter.status, equals(ModuleStatus.running));

        await adapter.stop();
        expect(adapter.status, equals(ModuleStatus.stopped));
      });

      test('应该正确清理资源', () async {
        await adapter.initialize(coordinator);
        await adapter.start();

        await adapter.dispose();
        // 验证资源已清理
        expect(adapter.status, isNot(equals(ModuleStatus.running)));
      });
    });
  });
}

/// Mock模块协调器
class MockModuleCoordinator implements IModuleCoordinator {
  @override
  IModuleCommunication get communication => MockModuleCommunication();

  @override
  Future<void> registerModule(IModuleAdapter adapter) async {
    // Mock implementation
  }

  @override
  Future<void> unregisterModule(String moduleId) async {
    // Mock implementation
  }

  @override
  IModuleAdapter? getModule(String moduleId) {
    return null; // Mock implementation
  }

  @override
  List<IModuleAdapter> getAllModules() {
    return []; // Mock implementation
  }

  @override
  Future<void> startAllModules() async {
    // Mock implementation
  }

  @override
  Future<void> stopAllModules() async {
    // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> getSystemStatus() async {
    return {'status': 'running', 'modules': 2};
  }
}

/// Mock模块通信
class MockModuleCommunication implements IModuleCommunication {
  @override
  Future<Map<String, dynamic>?> sendRequest(
    String targetModule,
    String action,
    Map<String, dynamic> data, {
    int timeoutMs = 5000,
  }) async {
    return {'success': true, 'data': 'mock response'};
  }

  @override
  Future<void> sendNotification(
    String targetModule,
    String event,
    Map<String, dynamic> data,
  ) async {
    // Mock implementation
  }

  @override
  Future<void> broadcastEvent(
    String event,
    Map<String, dynamic> data, {
    List<String> excludeModules = const [],
  }) async {
    // Mock implementation
  }

  @override
  void subscribeToEvent(String event, EventHandler handler) {
    // Mock implementation
  }

  @override
  void unsubscribeFromEvent(String event, [EventHandler? handler]) {
    // Mock implementation
  }
}
