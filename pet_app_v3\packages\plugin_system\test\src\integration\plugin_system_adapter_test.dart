/*
---------------------------------------------------------------
File name:          plugin_system_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        插件系统适应层集成测试
---------------------------------------------------------------
Change History:
    2025-07-29: 插件系统适应层集成测试实现;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:plugin_system/src/integration/plugin_system_adapter.dart';

void main() {
  group('PluginSystemAdapter Tests', () {
    late PluginSystemAdapter adapter;

    setUp(() {
      adapter = PluginSystemAdapter.instance;
    });

    test('应该能够获取适应层实例', () {
      expect(adapter, isNotNull);
      expect(adapter, isA<PluginSystemAdapter>());
    });

    test('应该提供正确的模块元数据', () {
      final metadata = adapter.metadata;
      expect(metadata.id, equals('plugin_system'));
      expect(metadata.name, equals('Plugin System'));
      expect(metadata.version, isNotEmpty);
      expect(metadata.description, isNotEmpty);
    });

    test('应该能够处理基础插件功能测试', () async {
      // 测试插件清单解析
      final manifestData = {
        'id': 'test_plugin',
        'name': 'Test Plugin',
        'version': '1.0.0',
        'description': 'A test plugin',
        'author': 'Test Author',
        'category': 'tool',
        'permissions': ['network', 'fileSystem'],
        'platforms': ['android', 'ios'],
      };

      expect(() => manifestData['id'], returnsNormally);
      expect(manifestData['name'], equals('Test Plugin'));
    });

    test('应该能够处理插件清单数据', () {
      // 测试插件清单数据结构
      final manifestData = {
        'id': 'test_plugin',
        'name': 'Test Plugin',
        'version': '1.0.0',
        'description': 'A test plugin',
        'author': 'Test Author',
        'category': 'tool',
        'permissions': ['network', 'fileSystem'],
        'platforms': ['android', 'ios'],
      };

      expect(manifestData['id'], equals('test_plugin'));
      expect(manifestData['name'], equals('Test Plugin'));
      expect(manifestData['category'], equals('tool'));
    });
  });
}
