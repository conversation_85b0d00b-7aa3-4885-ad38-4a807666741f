$total = 0
$fileStats = @{}

Write-Output "开始搜索creative_workshop模块..."
Get-ChildItem -Path "lib" -Recurse -Include "*.dart" | ForEach-Object {
    $content = Get-Content $_.FullName -Raw
    $matches = [regex]::Matches($content, "TODO.*Phase|TODO|简化|简化实现|简化处理|模拟|暂时实现|临时实现|占位符|placeholder|simplified|mock|未实现|待实现", [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $count = $matches.Count
    $total += $count

    if ($count -gt 0) {
        $fileStats[$_.Name] = $count
        Write-Output "$($_.Name): $count 个简化/TODO/未实现"
        foreach ($match in $matches) {
            Write-Output "  - $($match.Value)"
        }
    }
}

Write-Output ""
Write-Output "=== 统计结果 ==="
Write-Output "总计: $total 个简化/TODO/未实现"
Write-Output ""
Write-Output "按文件排序:"
$fileStats.GetEnumerator() | Sort-Object Value -Descending | ForEach-Object {
    Write-Output "$($_.Key): $($_.Value)"
}
