/*
---------------------------------------------------------------
File name:          plugin_system_integration_adapter.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop与Plugin System集成适配器
---------------------------------------------------------------
Change History:
    2025-07-29: P0.2.1 - 创建集成适配器，实现两套插件系统的统一管理;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:creative_workshop/creative_workshop.dart';
import 'package:flutter/foundation.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

import 'package:creative_workshop/src/core/plugins/workshop_plugin_adapter.dart';

/// Creative Workshop与Plugin System集成适配器
///
/// 负责协调Creative Workshop的PluginManager与Plugin System的
/// PluginRegistry、PluginLoader之间的交互，实现统一的插件管理。
///
/// 核心功能：
/// 1. 统一插件安装流程
/// 2. 统一插件启用/禁用管理
/// 3. 状态同步和事件传播
/// 4. 错误处理和恢复
class PluginSystemIntegrationAdapter {
  PluginSystemIntegrationAdapter._();
  static final PluginSystemIntegrationAdapter _instance =
      PluginSystemIntegrationAdapter._();

  /// 获取适配器单例实例
  static PluginSystemIntegrationAdapter get instance => _instance;

  // Plugin System 组件
  late final plugin_sys.PluginRegistry _systemRegistry;
  late final plugin_sys.PluginLoader _systemLoader;

  // Creative Workshop 组件
  late final PluginManager _workshopManager;

  // 状态管理
  bool _isInitialized = false;
  final Map<String, StreamSubscription<dynamic>> _stateSubscriptions =
      <String, StreamSubscription<dynamic>>{};
  final StreamController<PluginIntegrationEvent> _eventController =
      StreamController<PluginIntegrationEvent>.broadcast();

  /// 事件流
  Stream<PluginIntegrationEvent> get events => _eventController.stream;

  /// 初始化适配器
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // 初始化 Plugin System 组件
      _systemRegistry = plugin_sys.PluginRegistry.instance;
      _systemLoader = plugin_sys.PluginLoader.instance;

      // 初始化 Creative Workshop 组件
      _workshopManager = PluginManager.instance;
      await _workshopManager.initialize();

      // 建立状态同步
      await _setupStateSynchronization();

      _isInitialized = true;
      _logInfo('插件系统集成适配器初始化完成');
    } on Exception catch (e, stackTrace) {
      _logError('适配器初始化失败', e, stackTrace);
      rethrow;
    }
  }

  /// 集成安装插件
  ///
  /// 统一Creative Workshop和Plugin System的插件安装流程
  Future<PluginOperationResult> installPluginWithSystemIntegration(
    String pluginId, {
    String? version,
    bool autoUpdate = true,
  }) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成安装插件: $pluginId');

      // 1. 使用Creative Workshop安装插件文件
      final workshopResult = await _workshopManager.installPlugin(
        pluginId,
        version: version,
        autoUpdate: autoUpdate,
      );

      if (!workshopResult.success) {
        return workshopResult;
      }

      // 2. 创建Plugin System插件实例
      final pluginInstance = await _createPluginInstanceFromWorkshop(pluginId);
      if (pluginInstance == null) {
        return PluginOperationResult.failure(
          '无法为插件 $pluginId 创建Plugin System实例',
        );
      }

      // 3. 注册到Plugin System
      try {
        await _systemRegistry.register(pluginInstance);
        _logInfo('插件 $pluginId 已注册到Plugin System');
      } on Exception {
        // 如果注册失败，尝试安全注册
        final registered =
            await _systemRegistry.registerIfNotExists(pluginInstance);
        if (!registered) {
          _logWarning('插件 $pluginId 已存在于Plugin System中');
        }
      }

      // 4. 发送集成事件
      _eventController.add(
        PluginIntegrationEvent.installed(
          pluginId: pluginId,
          source: IntegrationSource.workshop,
          timestamp: DateTime.now(),
        ),
      );

      return PluginOperationResult.success('插件安装并集成成功');
    } on Exception catch (e, stackTrace) {
      _logError('集成安装插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成安装失败: $e');
    }
  }

  /// 集成启用插件
  Future<PluginOperationResult> enablePluginWithSystemIntegration(
    String pluginId,
  ) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成启用插件: $pluginId');

      // 1. Creative Workshop启用插件
      final workshopResult = await _workshopManager.enablePlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 2. Plugin System启动插件
      final plugin_sys.Plugin? plugin = _systemRegistry.get(pluginId);
      if (plugin != null) {
        try {
          await _systemLoader.loadPlugin(plugin);
          _logInfo('插件 $pluginId 已在Plugin System中启动');
        } on Exception catch (e) {
          _logWarning('Plugin System启动插件失败: $pluginId, 错误: $e');
          // 不阻止Creative Workshop的启用操作
        }
      } else {
        _logWarning('插件 $pluginId 在Plugin System中不存在');
      }

      // 3. 发送集成事件
      _eventController.add(
        PluginIntegrationEvent.enabled(
          pluginId: pluginId,
          source: IntegrationSource.workshop,
          timestamp: DateTime.now(),
        ),
      );

      return PluginOperationResult.success('插件启用并集成成功');
    } on Exception catch (e, stackTrace) {
      _logError('集成启用插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成启用失败: $e');
    }
  }

  /// 集成禁用插件
  Future<PluginOperationResult> disablePluginWithSystemIntegration(
    String pluginId,
  ) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成禁用插件: $pluginId');

      // 1. Plugin System停止插件
      final plugin_sys.Plugin? plugin = _systemRegistry.get(pluginId);
      if (plugin != null) {
        try {
          await _systemLoader.unloadPlugin(pluginId);
          _logInfo('插件 $pluginId 已在Plugin System中停止');
        } on Exception catch (e) {
          _logWarning('Plugin System停止插件失败: $pluginId, 错误: $e');
        }
      }

      // 2. Creative Workshop禁用插件
      final workshopResult = await _workshopManager.disablePlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 3. 发送集成事件
      _eventController.add(
        PluginIntegrationEvent.disabled(
          pluginId: pluginId,
          source: IntegrationSource.workshop,
          timestamp: DateTime.now(),
        ),
      );

      return PluginOperationResult.success('插件禁用并集成成功');
    } on Exception catch (e, stackTrace) {
      _logError('集成禁用插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成禁用失败: $e');
    }
  }

  /// 集成卸载插件
  Future<PluginOperationResult> uninstallPluginWithSystemIntegration(
    String pluginId,
  ) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成卸载插件: $pluginId');

      // 1. 先禁用插件
      await disablePluginWithSystemIntegration(pluginId);

      // 2. Plugin System注销插件
      try {
        await _systemRegistry.unregister(pluginId);
        _logInfo('插件 $pluginId 已从Plugin System注销');
      } on Exception catch (e) {
        _logWarning('Plugin System注销插件失败: $pluginId, 错误: $e');
      }

      // 3. Creative Workshop卸载插件
      final workshopResult = await _workshopManager.uninstallPlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 4. 发送集成事件
      _eventController.add(
        PluginIntegrationEvent.uninstalled(
          pluginId: pluginId,
          source: IntegrationSource.workshop,
          timestamp: DateTime.now(),
        ),
      );

      return PluginOperationResult.success('插件卸载并集成成功');
    } on Exception catch (e, stackTrace) {
      _logError('集成卸载插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成卸载失败: $e');
    }
  }

  /// 获取集成状态统计
  Map<String, dynamic> getIntegrationStatistics() {
    _ensureInitialized();

    final List<PluginInstallInfo> workshopPlugins =
        _workshopManager.installedPlugins;
    final List<plugin_sys.Plugin> systemPlugins =
        _systemRegistry.getAllPlugins();

    final Map<String, dynamic> stats = <String, dynamic>{
      'workshop_plugins_count': workshopPlugins.length,
      'system_plugins_count': systemPlugins.length,
      'integrated_plugins': _getIntegratedPluginIds(),
      'orphaned_workshop_plugins': _getOrphanedWorkshopPlugins(),
      'orphaned_system_plugins': _getOrphanedSystemPlugins(),
      'last_sync_time': DateTime.now().toIso8601String(),
    };

    return stats;
  }

  /// 建立状态同步
  Future<void> _setupStateSynchronization() async {
    // 监听Creative Workshop插件状态变化
    _workshopManager.addListener(_onWorkshopStateChanged);

    // 监听Plugin System状态变化
    // 注意：Plugin System的状态变化监听需要通过插件实例的stateChanges流来实现
    // 这里我们建立基础的监听框架，具体的插件状态监听在插件注册时建立
    _logInfo('状态同步监听已建立');
  }

  /// Creative Workshop状态变化处理
  void _onWorkshopStateChanged() {
    // 处理Creative Workshop插件状态变化
    // 当Creative Workshop中的插件状态发生变化时，同步到Plugin System
    try {
      final List<PluginInstallInfo> workshopPlugins =
          _workshopManager.installedPlugins;

      for (final PluginInstallInfo plugin in workshopPlugins) {
        final plugin_sys.Plugin? systemPlugin = _systemRegistry.get(plugin.id);

        if (systemPlugin != null) {
          // 根据Creative Workshop的状态同步Plugin System状态
          _syncPluginState(plugin, systemPlugin);
        }
      }

      // 发送同步事件
      _eventController.add(
        PluginIntegrationEvent(
          type: PluginIntegrationEventType.synchronized,
          pluginId: 'all',
          source: IntegrationSource.adapter,
          timestamp: DateTime.now(),
        ),
      );
    } on Exception catch (e) {
      _logError('状态同步处理失败', e);
    }
  }

  /// 同步单个插件状态
  void _syncPluginState(
      PluginInstallInfo workshopPlugin, plugin_sys.Plugin systemPlugin,) {
    try {
      // 根据Creative Workshop的插件状态决定Plugin System的操作
      switch (workshopPlugin.state) {
        case PluginState.notInstalled:
          // 插件未安装，无需同步
          break;
        case PluginState.downloading:
        case PluginState.installing:
        case PluginState.updating:
          // 插件正在处理中，暂不同步
          break;
        case PluginState.installed:
          // 如果Creative Workshop中插件已安装但未启用，确保Plugin System中也是停止状态
          if (systemPlugin.currentState == plugin_sys.PluginState.started) {
            systemPlugin.stop();
          }
        case PluginState.enabling:
          // 插件正在启用中，暂不同步
          break;
        case PluginState.enabled:
          // 如果Creative Workshop中插件已启用，确保Plugin System中也是启动状态
          if (systemPlugin.currentState != plugin_sys.PluginState.started) {
            systemPlugin.start();
          }
        case PluginState.disabling:
        case PluginState.uninstalling:
          // 插件正在处理中，暂不同步
          break;
        case PluginState.disabled:
          // 如果Creative Workshop中插件已禁用，确保Plugin System中也是停止状态
          if (systemPlugin.currentState == plugin_sys.PluginState.started) {
            systemPlugin.stop();
          }
        case PluginState.installFailed:
          // 如果Creative Workshop中插件安装失败，记录日志
          _logWarning('插件 ${workshopPlugin.id} 在Creative Workshop中安装失败');
        case PluginState.updateAvailable:
          // 插件有更新可用，记录日志
          _logInfo('插件 ${workshopPlugin.id} 有更新可用');
      }
    } on Exception catch (e) {
      _logError('同步插件 ${workshopPlugin.id} 状态失败', e);
    }
  }

  /// 从Creative Workshop创建Plugin System插件实例
  Future<plugin_sys.Plugin?> _createPluginInstanceFromWorkshop(
    String pluginId,
  ) async {
    try {
      _logInfo('开始为插件 $pluginId 创建Plugin System实例');

      // 1. 获取Creative Workshop中的插件信息
      final PluginInstallInfo? workshopPlugin = _workshopManager
          .installedPlugins
          .where((PluginInstallInfo p) => p.id == pluginId)
          .firstOrNull;

      if (workshopPlugin == null) {
        _logError('在Creative Workshop中未找到插件: $pluginId', null);
        return null;
      }

      // 2. 从文件系统读取插件清单
      final plugin_sys.PluginManifestParser manifestParser =
          plugin_sys.PluginManifestParser.instance;
      final plugin_sys.PluginManifestParseResult parseResult =
          await manifestParser.parseFromPlugin(pluginId);

      if (!parseResult.success || parseResult.manifest == null) {
        _logError('解析插件清单失败: $pluginId', parseResult.error);
        return null;
      }

      final plugin_sys.PluginManifest manifest = parseResult.manifest!;

      // 3. 创建Plugin System插件实例
      final WorkshopPluginAdapter pluginInstance = WorkshopPluginAdapter(
        manifest: manifest,
        workshopInfo: workshopPlugin,
      );

      _logInfo('成功为插件 $pluginId 创建Plugin System实例');
      return pluginInstance;
    } on Exception catch (e, stackTrace) {
      _logError('创建插件实例失败: $pluginId', e, stackTrace);
      return null;
    }
  }

  /// 获取集成的插件ID列表
  List<String> _getIntegratedPluginIds() {
    final Set<String> workshopIds = _workshopManager.installedPlugins
        .map((PluginInstallInfo p) => p.id)
        .toSet();
    final Set<String> systemIds = _systemRegistry
        .getAllPlugins()
        .map((plugin_sys.Plugin p) => p.id)
        .toSet();
    return workshopIds.intersection(systemIds).toList();
  }

  /// 获取孤立的Creative Workshop插件
  List<String> _getOrphanedWorkshopPlugins() {
    final Set<String> workshopIds = _workshopManager.installedPlugins
        .map((PluginInstallInfo p) => p.id)
        .toSet();
    final Set<String> systemIds = _systemRegistry
        .getAllPlugins()
        .map((plugin_sys.Plugin p) => p.id)
        .toSet();
    return workshopIds.difference(systemIds).toList();
  }

  /// 获取孤立的Plugin System插件
  List<String> _getOrphanedSystemPlugins() {
    final Set<String> workshopIds = _workshopManager.installedPlugins
        .map((PluginInstallInfo p) => p.id)
        .toSet();
    final Set<String> systemIds = _systemRegistry
        .getAllPlugins()
        .map((plugin_sys.Plugin p) => p.id)
        .toSet();
    return systemIds.difference(workshopIds).toList();
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('PluginSystemIntegrationAdapter未初始化，请先调用initialize()');
    }
  }

  /// 日志记录
  void _logInfo(String message) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] INFO: $message');
    }
  }

  void _logWarning(String message) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] WARNING: $message');
    }
  }

  void _logError(String message, Object? error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] ERROR: $message');
      if (error != null) {
        print('Error: $error');
      }
      if (stackTrace != null) {
        print('StackTrace: $stackTrace');
      }
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    for (final StreamSubscription subscription
        in _stateSubscriptions.values) {
      await subscription.cancel();
    }
    _stateSubscriptions.clear();

    _workshopManager.removeListener(_onWorkshopStateChanged);
    await _eventController.close();

    _isInitialized = false;
  }
}

/// 插件集成事件
class PluginIntegrationEvent {
  const PluginIntegrationEvent({
    required this.type,
    required this.pluginId,
    required this.source,
    required this.timestamp,
    this.data,
  });

  /// 插件安装事件
  factory PluginIntegrationEvent.installed({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) =>
      PluginIntegrationEvent(
        type: PluginIntegrationEventType.installed,
        pluginId: pluginId,
        source: source,
        timestamp: timestamp,
        data: data,
      );

  /// 插件启用事件
  factory PluginIntegrationEvent.enabled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) =>
      PluginIntegrationEvent(
        type: PluginIntegrationEventType.enabled,
        pluginId: pluginId,
        source: source,
        timestamp: timestamp,
        data: data,
      );

  /// 插件禁用事件
  factory PluginIntegrationEvent.disabled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) =>
      PluginIntegrationEvent(
        type: PluginIntegrationEventType.disabled,
        pluginId: pluginId,
        source: source,
        timestamp: timestamp,
        data: data,
      );

  /// 插件卸载事件
  factory PluginIntegrationEvent.uninstalled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) =>
      PluginIntegrationEvent(
        type: PluginIntegrationEventType.uninstalled,
        pluginId: pluginId,
        source: source,
        timestamp: timestamp,
        data: data,
      );

  final PluginIntegrationEventType type;
  final String pluginId;
  final IntegrationSource source;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  @override
  String toString() =>
      'PluginIntegrationEvent(type: $type, pluginId: $pluginId, '
      'source: $source, timestamp: $timestamp)';
}

/// 插件集成事件类型
enum PluginIntegrationEventType {
  installed('installed'),
  enabled('enabled'),
  disabled('disabled'),
  uninstalled('uninstalled'),
  synchronized('synchronized'),
  error('error');

  const PluginIntegrationEventType(this.name);
  final String name;
}

/// 集成来源
enum IntegrationSource {
  workshop('workshop'),
  system('system'),
  adapter('adapter');

  const IntegrationSource(this.name);
  final String name;
}
