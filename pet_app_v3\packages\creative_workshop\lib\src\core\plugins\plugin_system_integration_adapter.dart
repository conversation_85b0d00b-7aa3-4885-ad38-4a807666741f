/*
---------------------------------------------------------------
File name:          plugin_system_integration_adapter.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop与Plugin System集成适配器
---------------------------------------------------------------
Change History:
    2025-07-29: P0.2.1 - 创建集成适配器，实现两套插件系统的统一管理;
---------------------------------------------------------------
*/

import 'dart:async';
import 'package:flutter/foundation.dart';

// Creative Workshop 导入
import 'plugin_manager.dart';
import 'plugin_install_info.dart';
import 'plugin_operation_result.dart';
import '../../../creative_workshop.dart';

// Plugin System 导入
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

/// Creative Workshop与Plugin System集成适配器
///
/// 负责协调Creative Workshop的PluginManager与Plugin System的
/// PluginRegistry、PluginLoader之间的交互，实现统一的插件管理。
///
/// 核心功能：
/// 1. 统一插件安装流程
/// 2. 统一插件启用/禁用管理
/// 3. 状态同步和事件传播
/// 4. 错误处理和恢复
class PluginSystemIntegrationAdapter {
  PluginSystemIntegrationAdapter._();
  static final PluginSystemIntegrationAdapter _instance =
      PluginSystemIntegrationAdapter._();

  /// 获取适配器单例实例
  static PluginSystemIntegrationAdapter get instance => _instance;

  // Plugin System 组件
  late final plugin_sys.PluginRegistry _systemRegistry;
  late final plugin_sys.PluginLoader _systemLoader;

  // Creative Workshop 组件
  late final PluginManager _workshopManager;

  // 状态管理
  bool _isInitialized = false;
  final Map<String, StreamSubscription> _stateSubscriptions = {};
  final StreamController<PluginIntegrationEvent> _eventController =
      StreamController.broadcast();

  /// 事件流
  Stream<PluginIntegrationEvent> get events => _eventController.stream;

  /// 初始化适配器
  Future<void> initialize() async {
    if (_isInitialized) {
      return;
    }

    try {
      // 初始化 Plugin System 组件
      _systemRegistry = plugin_sys.PluginRegistry.instance;
      _systemLoader = plugin_sys.PluginLoader.instance;

      // 初始化 Creative Workshop 组件
      _workshopManager = PluginManager.instance;
      await _workshopManager.initialize();

      // 建立状态同步
      await _setupStateSynchronization();

      _isInitialized = true;
      _logInfo('插件系统集成适配器初始化完成');
    } catch (e, stackTrace) {
      _logError('适配器初始化失败', e, stackTrace);
      rethrow;
    }
  }

  /// 集成安装插件
  ///
  /// 统一Creative Workshop和Plugin System的插件安装流程
  Future<PluginOperationResult> installPluginWithSystemIntegration(
    String pluginId, {
    String? version,
    bool autoUpdate = true,
  }) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成安装插件: $pluginId');

      // 1. 使用Creative Workshop安装插件文件
      final workshopResult = await _workshopManager.installPlugin(
        pluginId,
        version: version,
        autoUpdate: autoUpdate,
      );

      if (!workshopResult.success) {
        return workshopResult;
      }

      // 2. 创建Plugin System插件实例
      final pluginInstance = await _createPluginInstanceFromWorkshop(pluginId);
      if (pluginInstance == null) {
        return PluginOperationResult.failure(
          '无法为插件 $pluginId 创建Plugin System实例',
        );
      }

      // 3. 注册到Plugin System
      try {
        await _systemRegistry.register(pluginInstance);
        _logInfo('插件 $pluginId 已注册到Plugin System');
      } on Exception {
        // 如果注册失败，尝试安全注册
        final registered =
            await _systemRegistry.registerIfNotExists(pluginInstance);
        if (!registered) {
          _logWarning('插件 $pluginId 已存在于Plugin System中');
        }
      }

      // 4. 发送集成事件
      _eventController.add(PluginIntegrationEvent.installed(
        pluginId: pluginId,
        source: IntegrationSource.workshop,
        timestamp: DateTime.now(),
      ));

      return PluginOperationResult.success('插件安装并集成成功');
    } catch (e, stackTrace) {
      _logError('集成安装插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成安装失败: $e');
    }
  }

  /// 集成启用插件
  Future<PluginOperationResult> enablePluginWithSystemIntegration(
      String pluginId) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成启用插件: $pluginId');

      // 1. Creative Workshop启用插件
      final workshopResult = await _workshopManager.enablePlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 2. Plugin System启动插件
      final plugin = _systemRegistry.get(pluginId);
      if (plugin != null) {
        try {
          await _systemLoader.loadPlugin(plugin);
          _logInfo('插件 $pluginId 已在Plugin System中启动');
        } catch (e) {
          _logWarning('Plugin System启动插件失败: $pluginId, 错误: $e');
          // 不阻止Creative Workshop的启用操作
        }
      } else {
        _logWarning('插件 $pluginId 在Plugin System中不存在');
      }

      // 3. 发送集成事件
      _eventController.add(PluginIntegrationEvent.enabled(
        pluginId: pluginId,
        source: IntegrationSource.workshop,
        timestamp: DateTime.now(),
      ));

      return PluginOperationResult.success('插件启用并集成成功');
    } catch (e, stackTrace) {
      _logError('集成启用插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成启用失败: $e');
    }
  }

  /// 集成禁用插件
  Future<PluginOperationResult> disablePluginWithSystemIntegration(
      String pluginId) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成禁用插件: $pluginId');

      // 1. Plugin System停止插件
      final plugin = _systemRegistry.get(pluginId);
      if (plugin != null) {
        try {
          await _systemLoader.unloadPlugin(pluginId);
          _logInfo('插件 $pluginId 已在Plugin System中停止');
        } catch (e) {
          _logWarning('Plugin System停止插件失败: $pluginId, 错误: $e');
        }
      }

      // 2. Creative Workshop禁用插件
      final workshopResult = await _workshopManager.disablePlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 3. 发送集成事件
      _eventController.add(PluginIntegrationEvent.disabled(
        pluginId: pluginId,
        source: IntegrationSource.workshop,
        timestamp: DateTime.now(),
      ));

      return PluginOperationResult.success('插件禁用并集成成功');
    } catch (e, stackTrace) {
      _logError('集成禁用插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成禁用失败: $e');
    }
  }

  /// 集成卸载插件
  Future<PluginOperationResult> uninstallPluginWithSystemIntegration(
      String pluginId) async {
    _ensureInitialized();

    try {
      _logInfo('开始集成卸载插件: $pluginId');

      // 1. 先禁用插件
      await disablePluginWithSystemIntegration(pluginId);

      // 2. Plugin System注销插件
      try {
        await _systemRegistry.unregister(pluginId);
        _logInfo('插件 $pluginId 已从Plugin System注销');
      } catch (e) {
        _logWarning('Plugin System注销插件失败: $pluginId, 错误: $e');
      }

      // 3. Creative Workshop卸载插件
      final workshopResult = await _workshopManager.uninstallPlugin(pluginId);
      if (!workshopResult.success) {
        return workshopResult;
      }

      // 4. 发送集成事件
      _eventController.add(PluginIntegrationEvent.uninstalled(
        pluginId: pluginId,
        source: IntegrationSource.workshop,
        timestamp: DateTime.now(),
      ));

      return PluginOperationResult.success('插件卸载并集成成功');
    } catch (e, stackTrace) {
      _logError('集成卸载插件失败: $pluginId', e, stackTrace);
      return PluginOperationResult.failure('集成卸载失败: $e');
    }
  }

  /// 获取集成状态统计
  Map<String, dynamic> getIntegrationStatistics() {
    _ensureInitialized();

    final workshopPlugins = _workshopManager.installedPlugins;
    final systemPlugins = _systemRegistry.getAllPlugins();

    return {
      'workshop_plugins_count': workshopPlugins.length,
      'system_plugins_count': systemPlugins.length,
      'integrated_plugins': _getIntegratedPluginIds(),
      'orphaned_workshop_plugins': _getOrphanedWorkshopPlugins(),
      'orphaned_system_plugins': _getOrphanedSystemPlugins(),
      'last_sync_time': DateTime.now().toIso8601String(),
    };
  }

  /// 建立状态同步
  Future<void> _setupStateSynchronization() async {
    // 监听Creative Workshop插件状态变化
    _workshopManager.addListener(_onWorkshopStateChanged);

    // 监听Plugin System插件状态变化
    // TODO(lgnorant-lu): 实现Plugin System状态变化监听
  }

  /// Creative Workshop状态变化处理
  void _onWorkshopStateChanged() {
    // 同步状态到Plugin System
    // TODO(lgnorant-lu): 实现状态同步逻辑
  }

  /// 从Creative Workshop创建Plugin System插件实例
  Future<plugin_sys.Plugin?> _createPluginInstanceFromWorkshop(
      String pluginId) async {
    try {
      _logInfo('开始为插件 $pluginId 创建Plugin System实例');

      // 1. 获取Creative Workshop中的插件信息
      final PluginInstallInfo? workshopPlugin = _workshopManager
          .installedPlugins
          .where((p) => p.id == pluginId)
          .firstOrNull;

      if (workshopPlugin == null) {
        _logError('在Creative Workshop中未找到插件: $pluginId', null);
        return null;
      }

      // 2. 从文件系统读取插件清单
      final manifestParser = plugin_sys.PluginManifestParser.instance;
      final parseResult = await manifestParser.parseFromPlugin(pluginId);

      if (!parseResult.success || parseResult.manifest == null) {
        _logError('解析插件清单失败: $pluginId', parseResult.error);
        return null;
      }

      final manifest = parseResult.manifest!;

      // 3. 创建Plugin System插件实例
      final pluginInstance = WorkshopPluginAdapter(
        manifest: manifest,
        workshopInfo: workshopPlugin,
      );

      _logInfo('成功为插件 $pluginId 创建Plugin System实例');
      return pluginInstance;
    } catch (e, stackTrace) {
      _logError('创建插件实例失败: $pluginId', e, stackTrace);
      return null;
    }
  }

  /// 获取集成的插件ID列表
  List<String> _getIntegratedPluginIds() {
    final workshopIds =
        _workshopManager.installedPlugins.map((p) => p.id).toSet();
    final systemIds = _systemRegistry.getAllPlugins().map((p) => p.id).toSet();
    return workshopIds.intersection(systemIds).toList();
  }

  /// 获取孤立的Creative Workshop插件
  List<String> _getOrphanedWorkshopPlugins() {
    final workshopIds =
        _workshopManager.installedPlugins.map((p) => p.id).toSet();
    final systemIds = _systemRegistry.getAllPlugins().map((p) => p.id).toSet();
    return workshopIds.difference(systemIds).toList();
  }

  /// 获取孤立的Plugin System插件
  List<String> _getOrphanedSystemPlugins() {
    final workshopIds =
        _workshopManager.installedPlugins.map((p) => p.id).toSet();
    final systemIds = _systemRegistry.getAllPlugins().map((p) => p.id).toSet();
    return systemIds.difference(workshopIds).toList();
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_isInitialized) {
      throw StateError('PluginSystemIntegrationAdapter未初始化，请先调用initialize()');
    }
  }

  /// 日志记录
  void _logInfo(String message) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] INFO: $message');
    }
  }

  void _logWarning(String message) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] WARNING: $message');
    }
  }

  void _logError(String message, Object? error, [StackTrace? stackTrace]) {
    if (kDebugMode) {
      print('[PluginSystemIntegrationAdapter] ERROR: $message');
      if (error != null) print('Error: $error');
      if (stackTrace != null) print('StackTrace: $stackTrace');
    }
  }

  /// 清理资源
  Future<void> dispose() async {
    for (final subscription in _stateSubscriptions.values) {
      await subscription.cancel();
    }
    _stateSubscriptions.clear();

    _workshopManager.removeListener(_onWorkshopStateChanged);
    await _eventController.close();

    _isInitialized = false;
  }
}

/// 插件集成事件
class PluginIntegrationEvent {
  const PluginIntegrationEvent({
    required this.type,
    required this.pluginId,
    required this.source,
    required this.timestamp,
    this.data,
  });

  final PluginIntegrationEventType type;
  final String pluginId;
  final IntegrationSource source;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  /// 插件安装事件
  factory PluginIntegrationEvent.installed({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) {
    return PluginIntegrationEvent(
      type: PluginIntegrationEventType.installed,
      pluginId: pluginId,
      source: source,
      timestamp: timestamp,
      data: data,
    );
  }

  /// 插件启用事件
  factory PluginIntegrationEvent.enabled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) {
    return PluginIntegrationEvent(
      type: PluginIntegrationEventType.enabled,
      pluginId: pluginId,
      source: source,
      timestamp: timestamp,
      data: data,
    );
  }

  /// 插件禁用事件
  factory PluginIntegrationEvent.disabled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) {
    return PluginIntegrationEvent(
      type: PluginIntegrationEventType.disabled,
      pluginId: pluginId,
      source: source,
      timestamp: timestamp,
      data: data,
    );
  }

  /// 插件卸载事件
  factory PluginIntegrationEvent.uninstalled({
    required String pluginId,
    required IntegrationSource source,
    required DateTime timestamp,
    Map<String, dynamic>? data,
  }) {
    return PluginIntegrationEvent(
      type: PluginIntegrationEventType.uninstalled,
      pluginId: pluginId,
      source: source,
      timestamp: timestamp,
      data: data,
    );
  }

  @override
  String toString() {
    return 'PluginIntegrationEvent(type: $type, pluginId: $pluginId, '
        'source: $source, timestamp: $timestamp)';
  }
}

/// 插件集成事件类型
enum PluginIntegrationEventType {
  installed('installed'),
  enabled('enabled'),
  disabled('disabled'),
  uninstalled('uninstalled'),
  synchronized('synchronized'),
  error('error');

  const PluginIntegrationEventType(this.name);
  final String name;
}

/// 集成来源
enum IntegrationSource {
  workshop('workshop'),
  system('system'),
  adapter('adapter');

  const IntegrationSource(this.name);
  final String name;
}

/// Creative Workshop插件到Plugin System的适配器
///
/// 将Creative Workshop的插件信息适配为Plugin System的Plugin接口
class WorkshopPluginAdapter implements plugin_sys.Plugin {
  WorkshopPluginAdapter({
    required plugin_sys.PluginManifest manifest,
    required this.workshopInfo,
  })  : _manifest = manifest,
        _stateController = StreamController<plugin_sys.PluginState>.broadcast();

  final plugin_sys.PluginManifest _manifest;
  final PluginInstallInfo workshopInfo;
  final StreamController<plugin_sys.PluginState> _stateController;

  plugin_sys.PluginState _currentState = plugin_sys.PluginState.loaded;
  Duration? _loadTime;

  @override
  String get id => _manifest.id;

  @override
  String get name => _manifest.name;

  @override
  String get version => _manifest.version;

  @override
  String get description => _manifest.description;

  @override
  String get author => _manifest.author;

  @override
  plugin_sys.PluginType get category => _convertCategory(_manifest.category);

  @override
  List<plugin_sys.PluginPermission> get requiredPermissions =>
      _manifest.permissions.map(_convertPermission).toList();

  @override
  List<plugin_sys.PluginDependency> get dependencies => _manifest.dependencies
      .map<plugin_sys.PluginDependency>(_convertDependency)
      .toList();

  @override
  List<plugin_sys.SupportedPlatform> get supportedPlatforms =>
      _manifest.platforms
          .map<plugin_sys.SupportedPlatform>(_convertPlatform)
          .toList();

  @override
  plugin_sys.PluginState get currentState => _currentState;

  @override
  Stream<plugin_sys.PluginState> get stateChanges => _stateController.stream;

  @override
  plugin_sys.PluginManifest get manifest => _manifest;

  @override
  bool get isEnabled => _currentState == plugin_sys.PluginState.started;

  @override
  Duration? get loadTime => _loadTime;

  @override
  Future<void> initialize() async {
    _updateState(plugin_sys.PluginState.loaded);
    try {
      // 初始化逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));
      _updateState(plugin_sys.PluginState.initialized);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    _updateState(plugin_sys.PluginState.initialized);
    try {
      final startTime = DateTime.now();
      // 启动逻辑
      await Future<void>.delayed(const Duration(milliseconds: 100));
      _loadTime = DateTime.now().difference(startTime);
      _updateState(plugin_sys.PluginState.started);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> pause() async {
    _updateState(plugin_sys.PluginState.paused);
  }

  @override
  Future<void> resume() async {
    _updateState(plugin_sys.PluginState.started);
  }

  @override
  Future<void> stop() async {
    _updateState(plugin_sys.PluginState.stopped);
    try {
      // 停止逻辑
      await Future<void>.delayed(const Duration(milliseconds: 50));
      _updateState(plugin_sys.PluginState.stopped);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    _updateState(plugin_sys.PluginState.unloaded);
    try {
      // 清理逻辑
      await _stateController.close();
      _updateState(plugin_sys.PluginState.unloaded);
    } catch (e) {
      _updateState(plugin_sys.PluginState.error);
      rethrow;
    }
  }

  @override
  Object? getConfigWidget() {
    // 返回配置界面，如果有的话
    return null;
  }

  @override
  Object getMainWidget() {
    // 返回主界面
    return 'Workshop Plugin: $name ($id)';
  }

  @override
  Future<dynamic> handleMessage(
      String action, Map<String, dynamic> data) async {
    // 处理插件间消息
    switch (action) {
      case 'ping':
        return {'status': 'pong', 'pluginId': id};
      case 'getInfo':
        return {
          'id': id,
          'name': name,
          'version': version,
          'state': currentState.toString(),
          'workshopInfo': {
            'installedAt': workshopInfo.installedAt.toIso8601String(),
            'size': workshopInfo.size,
            'autoUpdate': workshopInfo.autoUpdate,
          },
        };
      default:
        return {'error': 'Unknown action: $action'};
    }
  }

  /// 更新插件状态
  void _updateState(plugin_sys.PluginState newState) {
    _currentState = newState;
    _stateController.add(newState);
  }

  /// 转换插件类型
  plugin_sys.PluginType _convertCategory(String category) {
    switch (category.toLowerCase()) {
      case 'tool':
        return plugin_sys.PluginType.tool;
      case 'game':
        return plugin_sys.PluginType.game;
      case 'theme':
        return plugin_sys.PluginType.theme;
      case 'service':
        return plugin_sys.PluginType.service;
      case 'widget':
        return plugin_sys.PluginType.widget;
      default:
        return plugin_sys.PluginType.tool;
    }
  }

  /// 转换权限
  plugin_sys.PluginPermission _convertPermission(String permission) {
    switch (permission.toLowerCase()) {
      case 'filesystem':
      case 'file_system':
        return plugin_sys.PluginPermission.fileSystem;
      case 'network':
        return plugin_sys.PluginPermission.network;
      case 'camera':
        return plugin_sys.PluginPermission.camera;
      case 'microphone':
        return plugin_sys.PluginPermission.microphone;
      case 'location':
        return plugin_sys.PluginPermission.location;
      case 'clipboard':
        return plugin_sys.PluginPermission.clipboard;
      case 'notifications':
        return plugin_sys.PluginPermission.notifications;
      case 'contacts':
        return plugin_sys.PluginPermission.contacts;
      case 'calendar':
        return plugin_sys.PluginPermission.calendar;
      case 'photos':
        return plugin_sys.PluginPermission.photos;
      case 'systemsettings':
      case 'system_settings':
        return plugin_sys.PluginPermission.systemSettings;
      case 'backgroundexecution':
      case 'background_execution':
        return plugin_sys.PluginPermission.backgroundExecution;
      case 'deviceinfo':
      case 'device_info':
        return plugin_sys.PluginPermission.deviceInfo;
      default:
        return plugin_sys.PluginPermission.fileSystem;
    }
  }

  /// 转换依赖
  plugin_sys.PluginDependency _convertDependency(
      plugin_sys.PluginManifestDependency dep) {
    return plugin_sys.PluginDependency(
      pluginId: dep.id,
      versionConstraint: dep.version,
      optional: !dep.required,
    );
  }

  /// 转换平台
  plugin_sys.SupportedPlatform _convertPlatform(String platform) {
    switch (platform.toLowerCase()) {
      case 'android':
        return plugin_sys.SupportedPlatform.android;
      case 'ios':
        return plugin_sys.SupportedPlatform.ios;
      case 'windows':
        return plugin_sys.SupportedPlatform.windows;
      case 'macos':
        return plugin_sys.SupportedPlatform.macos;
      case 'linux':
        return plugin_sys.SupportedPlatform.linux;
      case 'web':
        return plugin_sys.SupportedPlatform.web;
      default:
        return plugin_sys.SupportedPlatform.android;
    }
  }
}
