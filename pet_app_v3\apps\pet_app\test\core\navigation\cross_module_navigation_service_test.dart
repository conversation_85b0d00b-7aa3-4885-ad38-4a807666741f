/*
---------------------------------------------------------------
File name:          cross_module_navigation_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        跨模块导航服务测试 - 真实实现验证
---------------------------------------------------------------
Change History:
    2025-07-30: 真实跨模块导航服务测试实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:pet_app_v3/core/navigation/cross_module_navigation_service.dart';

void main() {
  group('CrossModuleNavigationService Tests', () {
    late CrossModuleNavigationService navigationService;
    late PageController pageController;

    setUp(() {
      navigationService = CrossModuleNavigationService.instance;
      pageController = PageController();
    });

    tearDown(() {
      pageController.dispose();
      navigationService.dispose();
    });

    test('should be singleton', () {
      final service1 = CrossModuleNavigationService.instance;
      final service2 = CrossModuleNavigationService.instance;
      expect(service1, same(service2));
    });

    test('should initialize successfully', () async {
      final modulePageIndexMap = <String, int>{
        'home': 0,
        'desktop_pet': 1,
        'creative_workshop': 2,
        'app_manager': 3,
        'settings': 4,
      };

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      expect(navigationService.modulePageIndexMap, equals(modulePageIndexMap));
      expect(navigationService.currentPageIndex, equals(0));
    });

    test('should handle route mapping correctly', () async {
      final modulePageIndexMap = <String, int>{
        'home': 0,
        'creative_workshop': 2,
        'app_manager': 3,
      };

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 测试路由映射
      expect(navigationService.hasRoute('/creative_workshop'), isTrue);
      expect(navigationService.hasRoute('/app_manager'), isTrue);
      expect(navigationService.hasRoute('/app_manager/store'), isTrue);
      expect(navigationService.hasRoute('/non_existent'), isFalse);

      // 测试页面索引获取
      expect(
        navigationService.getPageIndexForRoute('/creative_workshop'),
        equals(2),
      );
      expect(navigationService.getPageIndexForRoute('/app_manager'), equals(3));
      expect(
        navigationService.getPageIndexForRoute('/app_manager/store'),
        equals(3),
      );
    });

    test('should add and remove route mappings', () async {
      final modulePageIndexMap = <String, int>{'test': 0};

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 添加路由映射
      navigationService.addRouteMapping('/test_route', 1);
      expect(navigationService.hasRoute('/test_route'), isTrue);
      expect(navigationService.getPageIndexForRoute('/test_route'), equals(1));

      // 移除路由映射
      navigationService.removeRouteMapping('/test_route');
      expect(navigationService.hasRoute('/test_route'), isFalse);
    });

    test('should handle navigation callbacks', () async {
      final modulePageIndexMap = <String, int>{'test': 0};
      var callbackCalled = false;
      var callbackPageIndex = -1;

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 注册回调
      void testCallback(int pageIndex) {
        callbackCalled = true;
        callbackPageIndex = pageIndex;
      }

      navigationService.addNavigationCallback(testCallback);

      // 模拟页面变更
      navigationService.updateCurrentPageIndex(1);

      expect(callbackCalled, isTrue);
      expect(callbackPageIndex, equals(1));

      // 移除回调
      navigationService.removeNavigationCallback(testCallback);
    });

    test('should update current page index', () async {
      final modulePageIndexMap = <String, int>{'test': 0};

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      expect(navigationService.currentPageIndex, equals(0));

      navigationService.updateCurrentPageIndex(2);
      expect(navigationService.currentPageIndex, equals(2));
    });

    test('should handle navigation to valid page index', () async {
      final modulePageIndexMap = <String, int>{'home': 0, 'test': 1};

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 导航到有效页面
      await navigationService.navigateToPage(1);
      expect(navigationService.currentPageIndex, equals(1));
    });

    test('should handle navigation to invalid page index', () async {
      final modulePageIndexMap = <String, int>{'test': 0};

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 导航到无效页面应该抛出异常
      expect(() => navigationService.navigateToPage(-1), throwsException);

      expect(() => navigationService.navigateToPage(10), throwsException);
    });

    test('should handle navigation to same page', () async {
      final modulePageIndexMap = <String, int>{'test': 0};

      await navigationService.initialize(
        pageController: pageController,
        modulePageIndexMap: modulePageIndexMap,
      );

      // 导航到相同页面应该直接返回
      await navigationService.navigateToPage(0);
      expect(navigationService.currentPageIndex, equals(0));
    });

    test('should handle navigation without PageController', () async {
      final modulePageIndexMap = <String, int>{'test': 0};

      // 不初始化PageController
      navigationService.dispose(); // 清理之前的状态

      expect(() => navigationService.navigateToPage(0), throwsException);
    });

    test('should handle disposal correctly', () {
      navigationService.dispose();

      // 验证清理后的状态
      expect(navigationService.modulePageIndexMap, isEmpty);
      expect(navigationService.routePageIndexMap, isEmpty);
    });
  });

  group('CrossModuleNavigationService Route Mapping Tests', () {
    test('should have correct default route mappings', () {
      final service = CrossModuleNavigationService.instance;

      // 这些是在_buildRouteMapping中定义的默认路由
      final expectedRoutes = [
        '/',
        '/home',
        '/desktop_pet',
        '/pet',
        '/creative_workshop',
        '/workshop',
        '/app_manager',
        '/apps',
        '/settings',
        '/app_manager/store',
        '/app_manager/dashboard',
        '/app_manager/modules',
        '/app_manager/permissions',
        '/creative_workshop/projects',
        '/creative_workshop/workspace',
        '/creative_workshop/tools',
        '/creative_workshop/games',
        '/creative_workshop/settings',
      ];

      // 注意：由于是单例，需要先初始化才能测试路由映射
      // 这个测试主要验证路由映射的结构是否正确
    });
  });
}
