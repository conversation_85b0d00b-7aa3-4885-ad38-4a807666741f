/*
---------------------------------------------------------------
File name:          module_coordinator.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        模块协调器 - 管理模块生命周期和通信
---------------------------------------------------------------
Change History:
    2025-07-29: 模块协调器实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

// 导入Plugin System集成接口
import 'package:plugin_system/plugin_system.dart';
// 导入Creative Workshop集成接口
import 'package:creative_workshop/creative_workshop.dart';

/// Pet App V3 统一模块协调器
/// 
/// 负责管理Plugin System、Creative Workshop、Ming CLI三个核心模块
/// 的生命周期、通信和依赖关系。
class ModuleCoordinator implements IModuleCoordinator {
  /// 单例实例
  static ModuleCoordinator? _instance;
  
  /// 获取单例实例
  static ModuleCoordinator get instance {
    _instance ??= ModuleCoordinator._();
    return _instance!;
  }
  
  /// 私有构造函数
  ModuleCoordinator._();

  /// 已注册的模块
  final Map<String, IModuleAdapter> _modules = {};
  
  /// 模块通信实现
  late final ModuleCommunicationImpl _communication;
  
  /// 是否已初始化
  bool _initialized = false;
  
  /// 启动顺序配置
  final List<String> _startupOrder = [
    'plugin_system',
    'creative_workshop',
    // Ming CLI 作为外部工具，不在主应用启动序列中
  ];

  @override
  IModuleCommunication get communication => _communication;

  /// 初始化协调器
  Future<void> initialize() async {
    if (_initialized) {
      developer.log('Module Coordinator 已经初始化', name: 'ModuleCoordinator');
      return;
    }

    try {
      developer.log('开始初始化 Module Coordinator', name: 'ModuleCoordinator');

      // 1. 初始化通信系统
      _communication = ModuleCommunicationImpl(this);

      // 2. 注册核心模块
      await _registerCoreModules();

      _initialized = true;
      developer.log('Module Coordinator 初始化完成', name: 'ModuleCoordinator');
    } catch (e, stackTrace) {
      developer.log(
        'Module Coordinator 初始化失败: $e',
        name: 'ModuleCoordinator',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> registerModule(IModuleAdapter adapter) async {
    final moduleId = adapter.metadata.id;
    
    if (_modules.containsKey(moduleId)) {
      throw StateError('模块已注册: $moduleId');
    }

    try {
      developer.log('注册模块: $moduleId', name: 'ModuleCoordinator');

      // 检查依赖
      await _checkDependencies(adapter);

      // 初始化模块
      await adapter.initialize(this);

      // 注册模块
      _modules[moduleId] = adapter;

      developer.log('模块注册完成: $moduleId', name: 'ModuleCoordinator');
    } catch (e, stackTrace) {
      developer.log(
        '模块注册失败: $moduleId, 错误: $e',
        name: 'ModuleCoordinator',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> unregisterModule(String moduleId) async {
    final adapter = _modules[moduleId];
    if (adapter == null) {
      developer.log('模块未注册: $moduleId', name: 'ModuleCoordinator');
      return;
    }

    try {
      developer.log('注销模块: $moduleId', name: 'ModuleCoordinator');

      // 停止模块
      if (adapter.status == ModuleStatus.running) {
        await adapter.stop();
      }

      // 销毁模块
      await adapter.dispose();

      // 移除注册
      _modules.remove(moduleId);

      developer.log('模块注销完成: $moduleId', name: 'ModuleCoordinator');
    } catch (e, stackTrace) {
      developer.log(
        '模块注销失败: $moduleId, 错误: $e',
        name: 'ModuleCoordinator',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  IModuleAdapter? getModule(String moduleId) {
    return _modules[moduleId];
  }

  @override
  List<IModuleAdapter> getAllModules() {
    return List.unmodifiable(_modules.values);
  }

  @override
  Future<void> startAllModules() async {
    developer.log('开始启动所有模块', name: 'ModuleCoordinator');

    for (final moduleId in _startupOrder) {
      final adapter = _modules[moduleId];
      if (adapter != null && adapter.status == ModuleStatus.initialized) {
        try {
          await adapter.start();
          developer.log('模块启动成功: $moduleId', name: 'ModuleCoordinator');
        } catch (e, stackTrace) {
          developer.log(
            '模块启动失败: $moduleId, 错误: $e',
            name: 'ModuleCoordinator',
            error: e,
            stackTrace: stackTrace,
          );
          // 继续启动其他模块
        }
      }
    }

    developer.log('所有模块启动完成', name: 'ModuleCoordinator');
  }

  @override
  Future<void> stopAllModules() async {
    developer.log('开始停止所有模块', name: 'ModuleCoordinator');

    // 按相反顺序停止模块
    final stopOrder = _startupOrder.reversed.toList();
    
    for (final moduleId in stopOrder) {
      final adapter = _modules[moduleId];
      if (adapter != null && adapter.status == ModuleStatus.running) {
        try {
          await adapter.stop();
          developer.log('模块停止成功: $moduleId', name: 'ModuleCoordinator');
        } catch (e, stackTrace) {
          developer.log(
            '模块停止失败: $moduleId, 错误: $e',
            name: 'ModuleCoordinator',
            error: e,
            stackTrace: stackTrace,
          );
          // 继续停止其他模块
        }
      }
    }

    developer.log('所有模块停止完成', name: 'ModuleCoordinator');
  }

  @override
  Future<Map<String, dynamic>> getSystemStatus() async {
    final moduleStatuses = <String, dynamic>{};
    
    for (final entry in _modules.entries) {
      try {
        final healthStatus = await entry.value.getHealthStatus();
        moduleStatuses[entry.key] = healthStatus;
      } catch (e) {
        moduleStatuses[entry.key] = {
          'status': 'error',
          'error': e.toString(),
        };
      }
    }

    return {
      'coordinator': {
        'initialized': _initialized,
        'moduleCount': _modules.length,
        'timestamp': DateTime.now().toIso8601String(),
      },
      'modules': moduleStatuses,
    };
  }

  /// 销毁协调器
  Future<void> dispose() async {
    try {
      developer.log('销毁 Module Coordinator', name: 'ModuleCoordinator');

      // 停止所有模块
      await stopAllModules();

      // 注销所有模块
      final moduleIds = _modules.keys.toList();
      for (final moduleId in moduleIds) {
        await unregisterModule(moduleId);
      }

      _initialized = false;
      
      developer.log('Module Coordinator 销毁完成', name: 'ModuleCoordinator');
    } catch (e, stackTrace) {
      developer.log(
        'Module Coordinator 销毁失败: $e',
        name: 'ModuleCoordinator',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 注册核心模块
  Future<void> _registerCoreModules() async {
    try {
      // 1. 注册Plugin System
      await registerModule(PluginSystemAdapter.instance);

      // 2. 注册Creative Workshop
      await registerModule(CreativeWorkshopAdapter.instance);

      developer.log('核心模块注册完成', name: 'ModuleCoordinator');
    } catch (e, stackTrace) {
      developer.log(
        '核心模块注册失败: $e',
        name: 'ModuleCoordinator',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 检查模块依赖
  Future<void> _checkDependencies(IModuleAdapter adapter) async {
    final requiredServices = adapter.metadata.requiredServices;
    
    for (final service in requiredServices) {
      bool serviceAvailable = false;
      
      // 检查其他模块是否提供此服务
      for (final existingAdapter in _modules.values) {
        if (existingAdapter.metadata.providedServices.contains(service)) {
          serviceAvailable = true;
          break;
        }
      }
      
      if (!serviceAvailable) {
        throw StateError('模块 ${adapter.metadata.id} 需要的服务 $service 不可用');
      }
    }
  }
}

/// 模块通信实现
class ModuleCommunicationImpl implements IModuleCommunication {
  final ModuleCoordinator _coordinator;
  
  /// 事件订阅映射
  final Map<String, List<EventHandler>> _eventSubscriptions = {};

  ModuleCommunicationImpl(this._coordinator);

  @override
  Future<Map<String, dynamic>?> sendRequest(
    String targetModule,
    String action,
    Map<String, dynamic> data, {
    int timeoutMs = 5000,
  }) async {
    final targetAdapter = _coordinator.getModule(targetModule);
    if (targetAdapter == null) {
      throw Exception('目标模块不存在: $targetModule');
    }

    try {
      final response = await targetAdapter.handleRequest(
        action,
        data,
        'system', // 发送者ID
      ).timeout(Duration(milliseconds: timeoutMs));

      return response;
    } catch (e) {
      developer.log(
        '发送请求失败: $targetModule.$action, 错误: $e',
        name: 'ModuleCommunication',
      );
      rethrow;
    }
  }

  @override
  Future<void> sendNotification(
    String targetModule,
    String event,
    Map<String, dynamic> data,
  ) async {
    // 通知不等待响应，直接触发事件
    await broadcastEvent(event, data, excludeModules: []);
  }

  @override
  Future<void> broadcastEvent(
    String event,
    Map<String, dynamic> data, {
    List<String> excludeModules = const [],
  }) async {
    final handlers = _eventSubscriptions[event] ?? [];
    
    for (final handler in handlers) {
      try {
        await handler(data);
      } catch (e) {
        developer.log(
          '事件处理失败: $event, 错误: $e',
          name: 'ModuleCommunication',
        );
        // 继续处理其他处理器
      }
    }
  }

  @override
  void subscribeToEvent(String event, EventHandler handler) {
    _eventSubscriptions.putIfAbsent(event, () => []).add(handler);
    developer.log('订阅事件: $event', name: 'ModuleCommunication');
  }

  @override
  void unsubscribeFromEvent(String event, [EventHandler? handler]) {
    if (handler == null) {
      _eventSubscriptions.remove(event);
      developer.log('取消所有事件订阅: $event', name: 'ModuleCommunication');
    } else {
      _eventSubscriptions[event]?.remove(handler);
      developer.log('取消事件订阅: $event', name: 'ModuleCommunication');
    }
  }
}
