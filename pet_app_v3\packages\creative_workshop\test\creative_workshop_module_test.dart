import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/creative_workshop_module.dart';

void main() {
  group('CreativeWorkshopModule Tests', () {
    late CreativeWorkshopModule module;

    setUp(() {
      module = CreativeWorkshopModule.instance;
    });

    tearDown(() async {
      // 清理资源
      if (module.isInitialized) {
        await module.dispose();
      }
    });

    group('Module Lifecycle', () {
      test('should initialize successfully', () async {
        // 测试模块初始化
        expect(module.isInitialized, isFalse);

        await module.initialize();

        expect(module.isInitialized, isTrue);

        final Map<String, dynamic> moduleInfo = module.getModuleInfo();
        expect(moduleInfo['id'], equals('creative_workshop'));
        expect(moduleInfo['version'], equals('5.0.6'));
      });

      test('should handle duplicate initialization gracefully', () async {
        // 测试重复初始化
        await module.initialize();
        expect(module.isInitialized, isTrue);

        // 第二次初始化应该正常处理
        await module.initialize();

        expect(module.isInitialized, isTrue);
      });

      test('should dispose successfully', () async {
        // 测试模块销毁
        await module.initialize();
        expect(module.isInitialized, isTrue);

        await module.dispose();

        expect(module.isInitialized, isFalse);
      });

      test('should handle dispose when not initialized', () async {
        // 测试未初始化时的销毁
        expect(module.isInitialized, isFalse);

        // 应该正常处理
        await module.dispose();

        expect(module.isInitialized, isFalse);
      });
    });

    group('Module Information', () {
      test('should provide correct module metadata', () async {
        await module.initialize();

        final Map<String, dynamic> moduleInfo = module.getModuleInfo();
        expect(moduleInfo['id'], equals('creative_workshop'));
        expect(moduleInfo['version'], equals('5.0.6'));
        expect(moduleInfo['description'], contains('应用商店'));
        expect(moduleInfo['description'], contains('开发者平台'));
      });

      test('should provide module routes', () async {
        await module.initialize();

        final Map<String, Function> routes = module.registerRoutes();
        expect(routes, isA<Map<String, Function>>());
        expect(routes, isNotEmpty);
      });

      test('should handle module info when not initialized', () {
        // 测试未初始化时获取模块信息
        final Map<String, dynamic> moduleInfo = module.getModuleInfo();
        expect(moduleInfo, isA<Map<String, dynamic>>());
        expect(moduleInfo['id'], equals('creative_workshop'));
      });
    });

    group('Error Handling', () {
      test('should handle initialization errors gracefully', () async {
        // 测试重复初始化
        await module.initialize();
        expect(module.isInitialized, isTrue);

        // 第二次初始化应该正常处理
        await module.initialize();
        expect(module.isInitialized, isTrue);
      });

      test('should handle dispose errors gracefully', () async {
        // 测试多次销毁
        await module.initialize();
        await module.dispose();
        expect(module.isInitialized, isFalse);

        // 第二次销毁应该正常处理
        await module.dispose();
        expect(module.isInitialized, isFalse);
      });
    });
  });
}
