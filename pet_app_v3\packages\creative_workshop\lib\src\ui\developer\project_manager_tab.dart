/*
---------------------------------------------------------------
File name:          project_manager_tab.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-07-22
Dart Version:       3.2+
Description:        项目管理标签页
---------------------------------------------------------------
Change History:
    2025-07-22: Phase ******* - 项目管理功能实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:creative_workshop/src/core/projects/project_manager.dart';
import 'package:creative_workshop/src/core/projects/project_manager.dart'
    as core;

/// 开发者项目信息
class DeveloperProject {
  const DeveloperProject({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.status,
    required this.createdAt,
    required this.lastModified,
    this.version = '1.0.0',
    this.tags = const <String>[],
  });

  final String id;
  final String name;
  final String description;
  final ProjectType type;
  final ProjectStatus status;
  final DateTime createdAt;
  final DateTime lastModified;
  final String version;
  final List<String> tags;
}

/// 项目类型
enum ProjectType {
  tool('工具插件'),
  game('游戏插件'),
  utility('实用程序'),
  theme('主题插件'),
  other('其他');

  const ProjectType(this.displayName);
  final String displayName;
}

/// 项目状态
enum ProjectStatus {
  development('开发中'),
  testing('测试中'),
  ready('准备发布'),
  published('已发布'),
  archived('已归档');

  const ProjectStatus(this.displayName);
  final String displayName;
}

/// 项目管理标签页
class ProjectManagerTab extends StatefulWidget {
  const ProjectManagerTab({super.key});

  @override
  State<ProjectManagerTab> createState() => _ProjectManagerTabState();
}

class _ProjectManagerTabState extends State<ProjectManagerTab> {
  List<DeveloperProject> _projects = <DeveloperProject>[];
  bool _isLoading = true;
  String _searchQuery = '';
  ProjectType? _selectedType;
  ProjectStatus? _selectedStatus;

  @override
  void initState() {
    super.initState();
    _loadProjects();
  }

  /// 加载项目列表
  Future<void> _loadProjects() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 从ProjectManager获取真实项目数据
      final projectManager = ProjectManager.instance;
      final projects = projectManager.projects;

      // 转换为DeveloperProject格式
      _projects = projects
          .map(
            (CreativeProject project) => DeveloperProject(
              id: project.id,
              name: project.name,
              description: project.description,
              type: _convertProjectType(project.type),
              status: _convertProjectStatus(project.status),
              createdAt: project.createdAt,
              lastModified: project.updatedAt,
              version: project.metadata['version'] as String? ?? '1.0.0',
              tags: project.tags,
            ),
          )
          .toList();

      // 如果没有项目，创建一些示例项目
      if (_projects.isEmpty) {
        await _createSampleProjects();
      }
    } catch (e) {
      debugPrint('加载项目失败: $e');
      // 回退到示例数据
      await _createSampleProjects();
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// 创建示例项目
  Future<void> _createSampleProjects() async {
    final now = DateTime.now();
    _projects = <DeveloperProject>[
      DeveloperProject(
        id: 'proj_001',
        name: '高级画笔工具',
        description: '提供多种画笔效果和纹理的专业绘画工具',
        type: ProjectType.tool,
        status: ProjectStatus.published,
        createdAt: now.subtract(const Duration(days: 30)),
        lastModified: now.subtract(const Duration(days: 2)),
        version: '1.2.0',
        tags: <String>['绘画', '画笔', '艺术'],
      ),
      DeveloperProject(
        id: 'proj_002',
        name: '拼图游戏引擎',
        description: '可配置的拼图游戏框架，支持自定义图片和难度',
        type: ProjectType.game,
        status: ProjectStatus.testing,
        createdAt: now.subtract(const Duration(days: 15)),
        lastModified: now.subtract(const Duration(hours: 6)),
        version: '0.9.0',
        tags: <String>['游戏', '拼图', '引擎'],
      ),
      DeveloperProject(
        id: 'proj_003',
        name: '颜色管理器',
        description: '专业的颜色选择和管理工具',
        type: ProjectType.utility,
        status: ProjectStatus.development,
        createdAt: now.subtract(const Duration(days: 7)),
        lastModified: now.subtract(const Duration(hours: 2)),
        version: '0.5.0',
        tags: <String>['颜色', '工具', '设计'],
      ),
      DeveloperProject(
        id: 'proj_004',
        name: '暗色主题包',
        description: '现代化的暗色主题集合',
        type: ProjectType.theme,
        status: ProjectStatus.ready,
        createdAt: now.subtract(const Duration(days: 20)),
        lastModified: now.subtract(const Duration(days: 1)),
        tags: <String>['主题', '暗色', 'UI'],
      ),
      DeveloperProject(
        id: 'proj_005',
        name: '数据导出工具',
        description: '支持多种格式的数据导出功能',
        type: ProjectType.utility,
        status: ProjectStatus.archived,
        createdAt: now.subtract(const Duration(days: 60)),
        lastModified: now.subtract(const Duration(days: 30)),
        version: '1.1.0',
        tags: <String>['导出', '数据', '工具'],
      ),
    ];

    setState(() {
      _isLoading = false;
    });
  }

  /// 过滤项目
  List<DeveloperProject> get _filteredProjects =>
      _projects.where((DeveloperProject project) {
        // 搜索过滤
        if (_searchQuery.isNotEmpty) {
          final String query = _searchQuery.toLowerCase();
          if (!project.name.toLowerCase().contains(query) &&
              !project.description.toLowerCase().contains(query) &&
              !project.tags
                  .any((String tag) => tag.toLowerCase().contains(query))) {
            return false;
          }
        }

        // 类型过滤
        if (_selectedType != null && project.type != _selectedType) {
          return false;
        }

        // 状态过滤
        if (_selectedStatus != null && project.status != _selectedStatus) {
          return false;
        }

        return true;
      }).toList();

  @override
  Widget build(BuildContext context) => Column(
        children: <Widget>[
          // 搜索和过滤栏
          _buildSearchAndFilterBar(),

          // 项目列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildProjectList(),
          ),
        ],
      );

  /// 构建搜索和过滤栏
  Widget _buildSearchAndFilterBar() => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: <Widget>[
            // 搜索框
            TextField(
              onChanged: (String value) {
                setState(() {
                  _searchQuery = value;
                });
              },
              decoration: InputDecoration(
                hintText: '搜索项目...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
              ),
            ),

            const SizedBox(height: 12),

            // 过滤器
            Row(
              children: <Widget>[
                // 类型过滤
                Expanded(
                  child: DropdownButtonFormField<ProjectType?>(
                    value: _selectedType,
                    onChanged: (ProjectType? value) {
                      setState(() {
                        _selectedType = value;
                      });
                    },
                    decoration: InputDecoration(
                      labelText: '项目类型',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: <DropdownMenuItem<ProjectType?>>[
                      const DropdownMenuItem<ProjectType?>(
                        child: Text('全部类型'),
                      ),
                      ...ProjectType.values.map(
                        (ProjectType type) => DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        ),
                      ),
                    ],
                  ),
                ),

                const SizedBox(width: 12),

                // 状态过滤
                Expanded(
                  child: DropdownButtonFormField<ProjectStatus?>(
                    value: _selectedStatus,
                    onChanged: (ProjectStatus? value) {
                      setState(() {
                        _selectedStatus = value;
                      });
                    },
                    decoration: InputDecoration(
                      labelText: '项目状态',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    items: <DropdownMenuItem<ProjectStatus?>>[
                      const DropdownMenuItem<ProjectStatus?>(
                        child: Text('全部状态'),
                      ),
                      ...ProjectStatus.values.map(
                        (ProjectStatus status) => DropdownMenuItem(
                          value: status,
                          child: Text(status.displayName),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      );

  /// 构建项目列表
  Widget _buildProjectList() {
    final filteredProjects = _filteredProjects;

    if (filteredProjects.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Icon(
              Icons.folder_open,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _projects.isEmpty ? '暂无项目' : '没有找到匹配的项目',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            if (_projects.isEmpty) ...<Widget>[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: _createNewProject,
                icon: const Icon(Icons.add),
                label: const Text('创建第一个项目'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: filteredProjects.length,
      itemBuilder: (BuildContext context, int index) =>
          _buildProjectCard(filteredProjects[index]),
    );
  }

  /// 构建项目卡片
  Widget _buildProjectCard(DeveloperProject project) => Card(
        margin: const EdgeInsets.only(bottom: 12),
        child: InkWell(
          onTap: () => _openProject(project),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                // 项目标题和状态
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        project.name,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    _buildStatusChip(project.status),
                  ],
                ),

                const SizedBox(height: 8),

                // 项目描述
                Text(
                  project.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

                const SizedBox(height: 12),

                // 项目信息
                Row(
                  children: <Widget>[
                    Icon(
                      _getTypeIcon(project.type),
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      project.type.displayName,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: Colors.grey[600],
                    ),
                    const SizedBox(width: 4),
                    Text(
                      _formatDate(project.lastModified),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                      ),
                    ),
                    const Spacer(),
                    Text(
                      'v${project.version}',
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),

                // 标签
                if (project.tags.isNotEmpty) ...<Widget>[
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 6,
                    runSpacing: 4,
                    children: project.tags
                        .map(
                          (String tag) => Chip(
                            label: Text(
                              tag,
                              style: const TextStyle(fontSize: 10),
                            ),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            visualDensity: VisualDensity.compact,
                          ),
                        )
                        .toList(),
                  ),
                ],
              ],
            ),
          ),
        ),
      );

  /// 构建状态标签
  Widget _buildStatusChip(ProjectStatus status) {
    Color color;
    switch (status) {
      case ProjectStatus.development:
        color = Colors.blue;
      case ProjectStatus.testing:
        color = Colors.orange;
      case ProjectStatus.ready:
        color = Colors.green;
      case ProjectStatus.published:
        color = Colors.purple;
      case ProjectStatus.archived:
        color = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }

  /// 获取类型图标
  IconData _getTypeIcon(ProjectType type) {
    switch (type) {
      case ProjectType.tool:
        return Icons.build;
      case ProjectType.game:
        return Icons.games;
      case ProjectType.utility:
        return Icons.widgets;
      case ProjectType.theme:
        return Icons.palette;
      case ProjectType.other:
        return Icons.extension;
    }
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }

  /// 打开项目
  void _openProject(DeveloperProject project) {
    // TODO: Phase ******* - 实现项目详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('打开项目: ${project.name}')),
    );
  }

  /// 创建新项目
  void _createNewProject() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) => _CreateProjectDialog(
        onProjectCreated: (Map<String, dynamic> project) async {
          try {
            final projectManager = ProjectManager.instance;
            final result = await projectManager.createProject(
              name: project['name'] as String,
              type: _convertToCoreProjectType(project['type'] as ProjectType),
              description: project['description'] as String,
              tags: project['tags'] as List<String>,
            );

            if (result.success) {
              await _loadProjects();
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('项目 "${project['name']}" 创建成功！')),
                );
              }
            } else {
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('创建项目失败: ${result.error}'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          } catch (e) {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('创建项目时发生错误: $e'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
      ),
    );
  }

  /// 转换为核心项目类型
  core.ProjectType _convertToCoreProjectType(ProjectType uiType) {
    switch (uiType) {
      case ProjectType.tool:
        return core.ProjectType.plugin;
      case ProjectType.game:
        return core.ProjectType.plugin;
      case ProjectType.utility:
        return core.ProjectType.custom;
      case ProjectType.theme:
        return core.ProjectType.design;
      case ProjectType.other:
        return core.ProjectType.custom;
    }
  }

  /// 转换项目类型
  ProjectType _convertProjectType(core.ProjectType coreType) {
    switch (coreType) {
      case core.ProjectType.plugin:
        return ProjectType.tool;
      case core.ProjectType.design:
        return ProjectType.theme;
      case core.ProjectType.animation:
        return ProjectType.utility;
      case core.ProjectType.model3d:
        return ProjectType.utility;
      case core.ProjectType.mixed:
        return ProjectType.other;
      case core.ProjectType.custom:
        return ProjectType.other;
    }
  }

  /// 转换项目状态
  ProjectStatus _convertProjectStatus(core.ProjectStatus coreStatus) {
    switch (coreStatus) {
      case core.ProjectStatus.draft:
        return ProjectStatus.development;
      case core.ProjectStatus.inProgress:
        return ProjectStatus.testing;
      case core.ProjectStatus.completed:
        return ProjectStatus.ready;
      case core.ProjectStatus.published:
        return ProjectStatus.published;
      case core.ProjectStatus.archived:
        return ProjectStatus.archived;
    }
  }
}

/// 创建项目对话框
class _CreateProjectDialog extends StatefulWidget {
  const _CreateProjectDialog({
    required this.onProjectCreated,
  });

  final void Function(Map<String, dynamic>) onProjectCreated;

  @override
  State<_CreateProjectDialog> createState() => _CreateProjectDialogState();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(
        ObjectFlagProperty<void Function(Map<String, dynamic> p1)>.has(
            'onProjectCreated', onProjectCreated));
  }
}

class _CreateProjectDialogState extends State<_CreateProjectDialog> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();
  ProjectType _selectedType = ProjectType.tool;
  final List<String> _tags = <String>[];
  final TextEditingController _tagController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => AlertDialog(
        title: const Text('创建新项目'),
        content: SizedBox(
          width: 400,
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                // 项目名称
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: '项目名称',
                    hintText: '输入项目名称',
                  ),
                  validator: (String? value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入项目名称';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // 项目类型
                DropdownButtonFormField<ProjectType>(
                  value: _selectedType,
                  decoration: const InputDecoration(
                    labelText: '项目类型',
                  ),
                  items: ProjectType.values
                      .map(
                        (ProjectType type) => DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        ),
                      )
                      .toList(),
                  onChanged: (ProjectType? value) {
                    if (value != null) {
                      setState(() {
                        _selectedType = value;
                      });
                    }
                  },
                ),
                const SizedBox(height: 16),

                // 项目描述
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: '项目描述',
                    hintText: '输入项目描述（可选）',
                  ),
                  maxLines: 3,
                ),
                const SizedBox(height: 16),

                // 标签输入
                Row(
                  children: <Widget>[
                    Expanded(
                      child: TextFormField(
                        controller: _tagController,
                        decoration: const InputDecoration(
                          labelText: '添加标签',
                          hintText: '输入标签名称',
                        ),
                        onFieldSubmitted: _addTag,
                      ),
                    ),
                    IconButton(
                      onPressed: () => _addTag(_tagController.text),
                      icon: const Icon(Icons.add),
                    ),
                  ],
                ),

                // 标签显示
                if (_tags.isNotEmpty) ...<Widget>[
                  const SizedBox(height: 8),
                  Wrap(
                    spacing: 8,
                    children: _tags
                        .map(
                          (String tag) => Chip(
                            label: Text(tag),
                            onDeleted: () => _removeTag(tag),
                          ),
                        )
                        .toList(),
                  ),
                ],
              ],
            ),
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: _createProject,
            child: const Text('创建'),
          ),
        ],
      );

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isNotEmpty && !_tags.contains(trimmedTag)) {
      setState(() {
        _tags.add(trimmedTag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  void _createProject() {
    if (_formKey.currentState!.validate()) {
      final projectData = <String, Object>{
        'name': _nameController.text.trim(),
        'type': _selectedType,
        'description': _descriptionController.text.trim(),
        'tags': _tags,
      };

      Navigator.of(context).pop();
      widget.onProjectCreated(projectData);
    }
  }
}
