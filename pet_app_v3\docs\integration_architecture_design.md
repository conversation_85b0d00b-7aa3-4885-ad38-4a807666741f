# Pet App V3 集成调用架构设计

## 📋 概述

本文档设计了Plugin System、Creative Workshop、Ming CLI三个核心模块与主应用的集成调用架构，解决模块间通信、依赖管理和统一接口问题。

## 🏗️ 当前架构分析

### 现有模块接口

#### 1. Plugin System 对外接口
- **PluginRegistry**: 插件注册、查询、状态管理
- **PluginLoader**: 动态加载、卸载插件
- **PluginMessenger**: 插件间消息通信
- **EventBus**: 事件发布订阅机制
- **PluginPublisher**: 插件发布管理
- **PluginDownloader**: 插件下载管理

#### 2. Creative Workshop 对外接口  
- **WorkshopManager**: 创意工坊核心管理
- **PluginRegistry** (独立): 工坊内插件注册
- **ToolPlugin/GamePlugin**: 工具和游戏插件接口
- **ProjectManager**: 项目管理功能

#### 3. Ming CLI 集成接口
- **PluginValidator**: 插件验证功能
- **PluginBuilder**: 插件构建功能
- **PetAppBridge**: 与Pet App V3的桥接
- **LocalRegistry**: 本地插件注册表

### 现有集成问题

1. **重复的PluginRegistry**: Plugin System和Creative Workshop都有独立的插件注册中心
2. **缺乏统一通信**: 模块间通信依赖各自的接口，缺乏统一协调
3. **Ming CLI集成不完整**: 与Pet App V3的实际集成仍是模拟实现
4. **循环依赖风险**: Creative Workshop依赖Plugin System，但可能存在反向依赖

## 🎯 集成架构方案

### 方案选择：混合适应层架构

**决策**: 采用"各模块内建适应层 + 主应用统一协调"的混合架构

**理由**:
1. **单一职责**: 各模块保持独立性，通过适应层暴露标准接口
2. **统一协调**: 主应用层提供统一的模块管理和通信协调
3. **可扩展性**: 新模块可以轻松集成，不影响现有架构
4. **测试友好**: 各层可以独立测试，降低集成复杂度

### 架构层次设计

```
┌─────────────────────────────────────────────────────────────┐
│                    Pet App V3 主应用层                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            统一模块协调器 (ModuleCoordinator)              │ │
│  │  - 模块生命周期管理                                        │ │
│  │  - 统一通信总线                                           │ │
│  │  - 依赖解析和注入                                         │ │
│  │  │  - 配置管理和同步                                      │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┼─────────┐
                    │         │         │
┌─────────────────┐ │ ┌───────────────┐ │ ┌─────────────────┐
│  Plugin System  │ │ │Creative Workshop│ │   Ming CLI      │
│  ┌─────────────┐ │ │ ┌─────────────┐ │ │ ┌─────────────┐ │
│  │ 适应层接口   │ │ │ │ 适应层接口   │ │ │ │ 桥接适应层   │ │
│  │ PluginAPI   │ │ │ │WorkshopAPI  │ │ │ │ MingBridge  │ │
│  └─────────────┘ │ │ └─────────────┘ │ │ └─────────────┘ │
│  ┌─────────────┐ │ │ ┌─────────────┐ │ │ ┌─────────────┐ │
│  │   核心实现   │ │ │ │   核心实现   │ │ │ │   核心实现   │ │
│  │ Registry    │ │ │ │ Workshop    │ │ │ │ Validator   │ │
│  │ Loader      │ │ │ │ Tools       │ │ │ │ Builder     │ │
│  │ Messenger   │ │ │ │ Games       │ │ │ │ Publisher   │ │
│  └─────────────┘ │ │ └─────────────┘ │ │ └─────────────┘ │
└─────────────────┘ │ └───────────────┘ │ └─────────────────┘
```

## 🔧 实施计划

### Phase 2.1: 集成架构方案设计 ✅
- [x] 分析现有模块接口和集成问题
- [x] 设计混合适应层架构方案
- [x] 制定实施计划和接口规范

### Phase 2.2: Plugin System 集成接口实现
**目标**: 在Plugin System中实现标准化的适应层接口

**任务**:
1. 创建 `PluginSystemAdapter` 适应层类
2. 统一插件注册接口，解决与Creative Workshop的冲突
3. 实现标准化的插件通信接口
4. 提供模块状态查询和管理接口

### Phase 2.3: Creative Workshop 集成接口实现  
**目标**: 在Creative Workshop中实现与Plugin System的集成

**任务**:
1. 创建 `CreativeWorkshopAdapter` 适应层类
2. 重构内部PluginRegistry，使用Plugin System的统一注册中心
3. 实现工具和游戏插件的标准化接口
4. 集成项目管理功能到统一接口

### Phase 2.4: Ming CLI 集成桥接实现
**目标**: 实现Ming CLI与Pet App V3的真实集成

**任务**:
1. 完善 `PetAppBridge` 的实际实现
2. 实现插件模板与Pet App V3插件系统的兼容
3. 建立插件同步和验证的真实通信机制
4. 集成插件构建和发布流程

### Phase 2.5: 主应用层集成调用实现
**目标**: 在主应用中实现统一的模块协调器

**任务**:
1. 创建 `ModuleCoordinator` 统一协调器
2. 实现模块生命周期管理
3. 建立统一的通信总线和事件路由
4. 实现配置管理和依赖注入

## 📋 接口规范

### 统一模块接口 (IModuleAdapter)

```dart
abstract class IModuleAdapter {
  /// 模块唯一标识
  String get moduleId;
  
  /// 模块名称
  String get moduleName;
  
  /// 模块版本
  String get moduleVersion;
  
  /// 模块状态
  ModuleStatus get status;
  
  /// 初始化模块
  Future<void> initialize(ModuleCoordinator coordinator);
  
  /// 启动模块
  Future<void> start();
  
  /// 停止模块
  Future<void> stop();
  
  /// 处理来自其他模块的请求
  Future<Map<String, dynamic>?> handleRequest(
    String action,
    Map<String, dynamic> data,
    String senderId,
  );
  
  /// 获取模块提供的服务列表
  List<String> getProvidedServices();
  
  /// 获取模块依赖的服务列表
  List<String> getRequiredServices();
}
```

### 统一通信接口 (IModuleCommunication)

```dart
abstract class IModuleCommunication {
  /// 发送请求到其他模块
  Future<Map<String, dynamic>?> sendRequest(
    String targetModule,
    String action,
    Map<String, dynamic> data,
  );
  
  /// 发送通知到其他模块
  Future<void> sendNotification(
    String targetModule,
    String event,
    Map<String, dynamic> data,
  );
  
  /// 广播事件到所有模块
  Future<void> broadcastEvent(
    String event,
    Map<String, dynamic> data,
  );
  
  /// 订阅事件
  void subscribeToEvent(String event, EventHandler handler);
  
  /// 取消订阅事件
  void unsubscribeFromEvent(String event, EventHandler handler);
}
```

## 🎯 预期收益

1. **统一接口**: 所有模块通过标准接口集成，降低耦合度
2. **简化通信**: 统一的通信机制，避免模块间直接依赖
3. **易于扩展**: 新模块只需实现适应层接口即可集成
4. **独立测试**: 各层可以独立测试，提高代码质量
5. **配置统一**: 统一的配置管理，避免配置冲突
6. **生命周期管理**: 统一的模块生命周期管理，确保启动顺序正确

## 📝 下一步行动

1. 开始实施 Phase 2.2: Plugin System 集成接口实现
2. 创建适应层基础类和接口定义
3. 重构现有的模块间调用方式
4. 编写集成测试验证架构设计
