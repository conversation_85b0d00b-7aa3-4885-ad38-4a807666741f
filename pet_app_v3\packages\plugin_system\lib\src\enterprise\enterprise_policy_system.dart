/*
---------------------------------------------------------------
File name:          enterprise_policy_system.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-26
Dart Version:       3.2+
Description:        企业策略管理系统 - Phase 5.4 企业级集成
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 5.4 - 企业策略管理系统实现;
---------------------------------------------------------------
*/

import 'dart:async';

/// 策略类型
enum PolicyType {
  /// 安全策略
  security,

  /// 访问控制策略
  accessControl,

  /// 数据保护策略
  dataProtection,

  /// 合规性策略
  compliance,

  /// 审计策略
  audit,

  /// 性能策略
  performance,

  /// 资源使用策略
  resourceUsage,
}

/// 策略严重程度
enum PolicySeverity {
  /// 信息
  info,

  /// 警告
  warning,

  /// 错误
  error,

  /// 严重
  critical,
}

/// 策略执行模式
enum PolicyEnforcementMode {
  /// 监控模式（仅记录）
  monitor,

  /// 警告模式（记录并警告）
  warn,

  /// 强制模式（阻止违规操作）
  enforce,

  /// 自动修复模式
  autoRemediate,
}

/// 企业策略
class EnterprisePolicy {
  const EnterprisePolicy({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.rules,
    this.severity = PolicySeverity.warning,
    this.enforcementMode = PolicyEnforcementMode.warn,
    this.enabled = true,
    this.version = '1.0.0',
    this.tags = const <String>[],
    this.metadata = const <String, dynamic>{},
  });

  /// 策略ID
  final String id;

  /// 策略名称
  final String name;

  /// 策略类型
  final PolicyType type;

  /// 描述
  final String description;

  /// 规则列表
  final List<PolicyRule> rules;

  /// 严重程度
  final PolicySeverity severity;

  /// 执行模式
  final PolicyEnforcementMode enforcementMode;

  /// 是否启用
  final bool enabled;

  /// 版本
  final String version;

  /// 标签
  final List<String> tags;

  /// 元数据
  final Map<String, dynamic> metadata;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'name': name,
        'type': type.name,
        'description': description,
        'rules': rules.map((PolicyRule r) => r.toJson()).toList(),
        'severity': severity.name,
        'enforcementMode': enforcementMode.name,
        'enabled': enabled,
        'version': version,
        'tags': tags,
        'metadata': metadata,
      };
}

/// 策略规则
class PolicyRule {
  const PolicyRule({
    required this.id,
    required this.name,
    required this.condition,
    required this.action,
    this.enabled = true,
    this.description,
    this.priority = 0,
  });

  /// 规则ID
  final String id;

  /// 规则名称
  final String name;

  /// 条件
  final PolicyCondition condition;

  /// 动作
  final PolicyAction action;

  /// 是否启用
  final bool enabled;

  /// 描述
  final String? description;

  /// 优先级
  final int priority;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'name': name,
        'condition': condition.toJson(),
        'action': action.toJson(),
        'enabled': enabled,
        'description': description,
        'priority': priority,
      };
}

/// 策略条件
abstract class PolicyCondition {
  /// 评估条件
  bool evaluate(PolicyContext context);

  /// 转换为JSON
  Map<String, dynamic> toJson();
}

/// 策略动作
abstract class PolicyAction {
  /// 执行动作
  Future<PolicyActionResult> execute(PolicyContext context);

  /// 转换为JSON
  Map<String, dynamic> toJson();
}

/// 策略上下文
class PolicyContext {
  const PolicyContext({
    required this.operation,
    required this.resource,
    required this.user,
    this.environment = const <String, dynamic>{},
    this.timestamp,
  });

  /// 操作
  final String operation;

  /// 资源
  final Map<String, dynamic> resource;

  /// 用户
  final Map<String, dynamic> user;

  /// 环境信息
  final Map<String, dynamic> environment;

  /// 时间戳
  final DateTime? timestamp;
}

/// 策略动作结果
class PolicyActionResult {
  const PolicyActionResult({
    required this.success,
    required this.action,
    this.message,
    this.details = const <String, dynamic>{},
  });

  /// 是否成功
  final bool success;

  /// 执行的动作
  final String action;

  /// 消息
  final String? message;

  /// 详细信息
  final Map<String, dynamic> details;
}

/// 策略违规事件
class PolicyViolationEvent {
  const PolicyViolationEvent({
    required this.id,
    required this.policyId,
    required this.ruleId,
    required this.timestamp,
    required this.context,
    required this.severity,
    required this.message,
    this.resolved = false,
    this.resolvedAt,
    this.resolvedBy,
  });

  /// 事件ID
  final String id;

  /// 策略ID
  final String policyId;

  /// 规则ID
  final String ruleId;

  /// 时间戳
  final DateTime timestamp;

  /// 上下文
  final PolicyContext context;

  /// 严重程度
  final PolicySeverity severity;

  /// 消息
  final String message;

  /// 是否已解决
  final bool resolved;

  /// 解决时间
  final DateTime? resolvedAt;

  /// 解决人
  final String? resolvedBy;
}

/// 合规性检查结果
class ComplianceCheckResult {
  const ComplianceCheckResult({
    required this.checkId,
    required this.timestamp,
    required this.target,
    required this.framework,
    required this.results,
    required this.summary,
  });

  /// 检查ID
  final String checkId;

  /// 检查时间
  final DateTime timestamp;

  /// 检查目标
  final String target;

  /// 合规框架
  final String framework;

  /// 检查结果
  final List<ComplianceResult> results;

  /// 摘要
  final ComplianceSummary summary;
}

/// 合规性结果
class ComplianceResult {
  const ComplianceResult({
    required this.controlId,
    required this.controlName,
    required this.status,
    required this.score,
    this.findings = const <String>[],
    this.recommendations = const <String>[],
  });

  /// 控制ID
  final String controlId;

  /// 控制名称
  final String controlName;

  /// 状态
  final ComplianceStatus status;

  /// 评分
  final double score;

  /// 发现的问题
  final List<String> findings;

  /// 建议
  final List<String> recommendations;
}

/// 合规性状态
enum ComplianceStatus {
  /// 符合
  compliant,

  /// 部分符合
  partiallyCompliant,

  /// 不符合
  nonCompliant,

  /// 未评估
  notAssessed,
}

/// 合规性摘要
class ComplianceSummary {
  const ComplianceSummary({
    required this.totalControls,
    required this.compliantControls,
    required this.partiallyCompliantControls,
    required this.nonCompliantControls,
    required this.notAssessedControls,
    required this.overallScore,
    required this.riskLevel,
  });

  /// 总控制数
  final int totalControls;

  /// 符合控制数
  final int compliantControls;

  /// 部分符合控制数
  final int partiallyCompliantControls;

  /// 不符合控制数
  final int nonCompliantControls;

  /// 未评估控制数
  final int notAssessedControls;

  /// 总体评分
  final double overallScore;

  /// 风险级别
  final String riskLevel;
}

/// 企业策略管理系统
class EnterprisePolicySystem {
  EnterprisePolicySystem._();
  static final EnterprisePolicySystem _instance = EnterprisePolicySystem._();
  static EnterprisePolicySystem get instance => _instance;

  /// 策略存储
  final Map<String, EnterprisePolicy> _policies = <String, EnterprisePolicy>{};

  /// 违规事件
  final List<PolicyViolationEvent> _violations = <PolicyViolationEvent>[];

  /// 事件流控制器
  final StreamController<PolicyViolationEvent> _violationController =
      StreamController<PolicyViolationEvent>.broadcast();

  /// 合规框架
  final Map<String, ComplianceFramework> _complianceFrameworks =
      <String, ComplianceFramework>{};

  /// 获取违规事件流
  Stream<PolicyViolationEvent> get violationStream =>
      _violationController.stream;

  /// 添加策略
  void addPolicy(EnterprisePolicy policy) {
    _policies[policy.id] = policy;
  }

  /// 移除策略
  void removePolicy(String policyId) {
    _policies.remove(policyId);
  }

  /// 获取策略
  EnterprisePolicy? getPolicy(String policyId) => _policies[policyId];

  /// 获取所有策略
  List<EnterprisePolicy> getAllPolicies() => _policies.values.toList();

  /// 评估策略
  Future<PolicyEvaluationResult> evaluatePolicy({
    required String policyId,
    required PolicyContext context,
  }) async {
    final policy = _policies[policyId];
    if (policy == null || !policy.enabled) {
      return PolicyEvaluationResult(
        policyId: policyId,
        success: true,
        violations: <PolicyViolationEvent>[],
        actions: <PolicyActionResult>[],
      );
    }

    final violations = <PolicyViolationEvent>[];
    final actions = <PolicyActionResult>[];

    for (final rule in policy.rules) {
      if (!rule.enabled) continue;

      if (rule.condition.evaluate(context)) {
        // 记录违规
        final violation = PolicyViolationEvent(
          id: _generateViolationId(),
          policyId: policy.id,
          ruleId: rule.id,
          timestamp: DateTime.now(),
          context: context,
          severity: policy.severity,
          message: '策略违规: ${rule.name}',
        );

        violations.add(violation);
        _violations.add(violation);
        _violationController.add(violation);

        // 执行动作
        if (policy.enforcementMode != PolicyEnforcementMode.monitor) {
          final actionResult = await rule.action.execute(context);
          actions.add(actionResult);
        }
      }
    }

    return PolicyEvaluationResult(
      policyId: policyId,
      success: violations.isEmpty,
      violations: violations,
      actions: actions,
    );
  }

  /// 批量评估策略
  Future<List<PolicyEvaluationResult>> evaluateAllPolicies(
      PolicyContext context,) async {
    final results = <PolicyEvaluationResult>[];

    for (final policy in _policies.values) {
      final result =
          await evaluatePolicy(policyId: policy.id, context: context);
      results.add(result);
    }

    return results;
  }

  /// 执行合规性检查
  Future<ComplianceCheckResult> performComplianceCheck({
    required String target,
    required String framework,
  }) async {
    final complianceFramework = _complianceFrameworks[framework];
    if (complianceFramework == null) {
      throw ArgumentError('Unknown compliance framework: $framework');
    }

    final results = <ComplianceResult>[];

    for (final control in complianceFramework.controls) {
      final result = await _evaluateComplianceControl(control, target);
      results.add(result);
    }

    final summary = _calculateComplianceSummary(results);

    return ComplianceCheckResult(
      checkId: _generateCheckId(),
      timestamp: DateTime.now(),
      target: target,
      framework: framework,
      results: results,
      summary: summary,
    );
  }

  /// 获取策略统计
  Map<String, dynamic> getPolicyStatistics() {
    final totalPolicies = _policies.length;
    final enabledPolicies =
        _policies.values.where((EnterprisePolicy p) => p.enabled).length;
    final violationCount = _violations.length;
    final recentViolations = _violations
        .where(
          (PolicyViolationEvent v) =>
              DateTime.now().difference(v.timestamp).inHours < 24,
        )
        .length;

    final violationsByType = <PolicyType, int>{};
    for (final violation in _violations) {
      final policy = _policies[violation.policyId];
      if (policy != null) {
        violationsByType[policy.type] =
            (violationsByType[policy.type] ?? 0) + 1;
      }
    }

    return <String, dynamic>{
      'totalPolicies': totalPolicies,
      'enabledPolicies': enabledPolicies,
      'totalViolations': violationCount,
      'recentViolations': recentViolations,
      'violationsByType':
          violationsByType.map((PolicyType k, int v) => MapEntry(k.name, v)),
    };
  }

  /// 注册合规框架
  void registerComplianceFramework(ComplianceFramework framework) {
    _complianceFrameworks[framework.id] = framework;
  }

  /// 创建内置策略
  void createBuiltinPolicies() {
    // 安全策略
    addPolicy(
      const EnterprisePolicy(
        id: 'security_001',
        name: '强密码策略',
        type: PolicyType.security,
        description: '要求使用强密码',
        rules: <PolicyRule>[
          PolicyRule(
            id: 'password_strength',
            name: '密码强度检查',
            condition: PasswordStrengthCondition(
                minLength: 8, requireSpecialChars: true,),
            action: RejectAction(message: '密码不符合强度要求'),
          ),
        ],
        severity: PolicySeverity.error,
        enforcementMode: PolicyEnforcementMode.enforce,
      ),
    );

    // 访问控制策略
    addPolicy(
      const EnterprisePolicy(
        id: 'access_001',
        name: '工作时间访问策略',
        type: PolicyType.accessControl,
        description: '限制非工作时间的系统访问',
        rules: <PolicyRule>[
          PolicyRule(
            id: 'business_hours',
            name: '工作时间检查',
            condition: BusinessHoursCondition(startHour: 9, endHour: 18),
            action: LogAction(message: '非工作时间访问'),
          ),
        ],
      ),
    );

    // 数据保护策略
    addPolicy(
      EnterprisePolicy(
        id: 'data_001',
        name: '敏感数据保护策略',
        type: PolicyType.dataProtection,
        description: '保护敏感数据不被未授权访问',
        rules: <PolicyRule>[
          PolicyRule(
            id: 'sensitive_data_access',
            name: '敏感数据访问检查',
            condition: SensitiveDataCondition(),
            action: EncryptAction(),
          ),
        ],
        severity: PolicySeverity.critical,
        enforcementMode: PolicyEnforcementMode.autoRemediate,
      ),
    );
  }

  /// 评估合规控制
  Future<ComplianceResult> _evaluateComplianceControl(
    ComplianceControl control,
    String target,
  ) async {
    // 简化的合规性评估逻辑
    await Future<void>.delayed(const Duration(milliseconds: 10));

    final score = 0.8 + (DateTime.now().millisecondsSinceEpoch % 20) / 100;
    final status = score >= 0.9
        ? ComplianceStatus.compliant
        : score >= 0.7
            ? ComplianceStatus.partiallyCompliant
            : ComplianceStatus.nonCompliant;

    return ComplianceResult(
      controlId: control.id,
      controlName: control.name,
      status: status,
      score: score,
      findings:
          status != ComplianceStatus.compliant ? <String>['需要改进'] : <String>[],
      recommendations: status != ComplianceStatus.compliant
          ? <String>['实施最佳实践']
          : <String>[],
    );
  }

  /// 计算合规性摘要
  ComplianceSummary _calculateComplianceSummary(
      List<ComplianceResult> results,) {
    final compliant = results
        .where((ComplianceResult r) => r.status == ComplianceStatus.compliant)
        .length;
    final partiallyCompliant = results
        .where((ComplianceResult r) =>
            r.status == ComplianceStatus.partiallyCompliant,)
        .length;
    final nonCompliant = results
        .where(
            (ComplianceResult r) => r.status == ComplianceStatus.nonCompliant,)
        .length;
    final notAssessed = results
        .where((ComplianceResult r) => r.status == ComplianceStatus.notAssessed)
        .length;

    final overallScore = results.isNotEmpty
        ? results
                .map((ComplianceResult r) => r.score)
                .reduce((double a, double b) => a + b) /
            results.length
        : 0.0;

    String riskLevel;
    if (overallScore >= 0.9) {
      riskLevel = 'Low';
    } else if (overallScore >= 0.7) {
      riskLevel = 'Medium';
    } else if (overallScore >= 0.5) {
      riskLevel = 'High';
    } else {
      riskLevel = 'Critical';
    }

    return ComplianceSummary(
      totalControls: results.length,
      compliantControls: compliant,
      partiallyCompliantControls: partiallyCompliant,
      nonCompliantControls: nonCompliant,
      notAssessedControls: notAssessed,
      overallScore: overallScore,
      riskLevel: riskLevel,
    );
  }

  /// 生成违规ID
  String _generateViolationId() =>
      'violation_${DateTime.now().millisecondsSinceEpoch}';

  /// 生成检查ID
  String _generateCheckId() => 'check_${DateTime.now().millisecondsSinceEpoch}';

  /// 清理资源
  Future<void> dispose() async {
    await _violationController.close();
    _policies.clear();
    _violations.clear();
    _complianceFrameworks.clear();
  }
}

/// 策略评估结果
class PolicyEvaluationResult {
  const PolicyEvaluationResult({
    required this.policyId,
    required this.success,
    required this.violations,
    required this.actions,
  });

  final String policyId;
  final bool success;
  final List<PolicyViolationEvent> violations;
  final List<PolicyActionResult> actions;
}

/// 合规框架
class ComplianceFramework {
  const ComplianceFramework({
    required this.id,
    required this.name,
    required this.description,
    required this.controls,
  });

  final String id;
  final String name;
  final String description;
  final List<ComplianceControl> controls;
}

/// 合规控制
class ComplianceControl {
  const ComplianceControl({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
  });

  final String id;
  final String name;
  final String description;
  final String category;
}

// 具体的策略条件实现
class PasswordStrengthCondition implements PolicyCondition {
  const PasswordStrengthCondition({
    required this.minLength,
    required this.requireSpecialChars,
  });

  final int minLength;
  final bool requireSpecialChars;

  @override
  bool evaluate(PolicyContext context) {
    final password = context.resource['password'] as String?;
    if (password == null) return false;

    if (password.length < minLength) return true; // 违规
    if (requireSpecialChars &&
        !RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) {
      return true; // 违规
    }

    return false; // 符合要求
  }

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{
        'type': 'password_strength',
        'minLength': minLength,
        'requireSpecialChars': requireSpecialChars,
      };
}

class BusinessHoursCondition implements PolicyCondition {
  const BusinessHoursCondition({
    required this.startHour,
    required this.endHour,
  });

  final int startHour;
  final int endHour;

  @override
  bool evaluate(PolicyContext context) {
    final now = DateTime.now();
    final hour = now.hour;
    return hour < startHour || hour >= endHour; // 非工作时间为违规
  }

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{
        'type': 'business_hours',
        'startHour': startHour,
        'endHour': endHour,
      };
}

class SensitiveDataCondition implements PolicyCondition {
  @override
  bool evaluate(PolicyContext context) {
    final data = context.resource['data'] as String?;
    if (data == null) return false;

    // 检查是否包含敏感信息
    final sensitivePatterns = <String>[
      r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b', // 信用卡号
      r'\b\d{3}-\d{2}-\d{4}\b', // SSN
    ];

    for (final pattern in sensitivePatterns) {
      if (RegExp(pattern).hasMatch(data)) {
        return true; // 包含敏感数据
      }
    }

    return false;
  }

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{'type': 'sensitive_data'};
}

// 具体的策略动作实现
class RejectAction implements PolicyAction {
  const RejectAction({required this.message});

  final String message;

  @override
  Future<PolicyActionResult> execute(PolicyContext context) async =>
      PolicyActionResult(
        success: true,
        action: 'reject',
        message: message,
      );

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{
        'type': 'reject',
        'message': message,
      };
}

class LogAction implements PolicyAction {
  const LogAction({required this.message});

  final String message;

  @override
  Future<PolicyActionResult> execute(PolicyContext context) async {
    // 记录日志
    print('Policy Log: $message');

    return PolicyActionResult(
      success: true,
      action: 'log',
      message: message,
    );
  }

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{
        'type': 'log',
        'message': message,
      };
}

class EncryptAction implements PolicyAction {
  @override
  Future<PolicyActionResult> execute(PolicyContext context) async {
    // 执行加密操作
    return const PolicyActionResult(
      success: true,
      action: 'encrypt',
      message: '敏感数据已加密',
    );
  }

  @override
  Map<String, dynamic> toJson() => <String, dynamic>{'type': 'encrypt'};
}
