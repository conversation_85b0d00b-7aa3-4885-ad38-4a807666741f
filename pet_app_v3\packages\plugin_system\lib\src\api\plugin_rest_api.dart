/*
---------------------------------------------------------------
File name:          plugin_rest_api.dart
Author:             lgnorant-lu
Date created:       2025-07-26
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件REST API实现 - 需要重构拆分
---------------------------------------------------------------
Change History:
    2025-07-26: Phase 4.3 - REST API实现;
    2025-07-27: 标记为需要重构 - 文件过大(4317行)，TODO过多(51个)
---------------------------------------------------------------
注意：此文件需要重构拆分为多个模块：
1. HTTP服务器管理 -> plugin_http_server.dart
2. API路由处理 -> plugin_api_router.dart
3. 插件安装管理 -> plugin_installation_manager.dart
4. 插件配置管理 -> plugin_config_manager.dart
5. 插件更新管理 -> plugin_update_manager.dart
6. 性能监控接口 -> plugin_monitoring_api.dart
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';
import 'package:plugin_system/src/core/plugin_batch_manager.dart';
import 'package:plugin_system/src/monitoring/plugin_monitoring_system.dart';
import 'package:plugin_system/src/optimization/plugin_performance_optimizer.dart';
import 'package:plugin_system/src/api/plugin_api_interface.dart';
import 'package:plugin_system/src/api/plugin_http_server.dart';
import 'package:plugin_system/src/api/plugin_api_router.dart';
import 'package:plugin_system/src/api/plugin_installation_manager.dart';
import 'package:plugin_system/src/api/plugin_config_manager.dart';
import 'package:plugin_system/src/api/plugin_update_manager.dart';
import 'package:plugin_system/src/api/plugin_monitoring_api.dart';

// RestApiConfig 已移动到 plugin_http_server.dart

/// 插件REST API实现 - 重构后的协调器
///
/// 此类现在作为各个专门模块的协调器，不再包含具体的业务逻辑实现
class PluginRestApi implements IPluginRestApi {
  PluginRestApi({
    required this.registry,
    required this.batchManager,
    required this.monitoringSystem,
    required this.performanceOptimizer,
    RestApiConfig? config,
  }) : _config = config ?? const RestApiConfig() {
    // 初始化各个管理器
    _httpServer = PluginHttpServer(
      config: _config,
      requestHandler: _router.handleRequest,
    );
    _installationManager = PluginInstallationManager(registry: registry);
    _configManager = PluginConfigManager(registry: registry);
    _updateManager = PluginUpdateManager(registry: registry);
    _monitoringApi = PluginMonitoringApi(
      registry: registry,
      monitoringSystem: monitoringSystem,
      performanceOptimizer: performanceOptimizer,
    );
  }

  /// 插件注册表
  final PluginRegistry registry;

  /// 批量管理器
  final PluginBatchManager batchManager;

  /// 监控系统
  final PluginMonitoringSystem monitoringSystem;

  /// 性能优化器
  final PluginPerformanceOptimizer performanceOptimizer;

  /// 配置
  final RestApiConfig _config;

  /// HTTP服务器
  late final PluginHttpServer _httpServer;

  /// API路由器
  final PluginApiRouter _router = PluginApiRouter();

  /// 安装管理器
  late final PluginInstallationManager _installationManager;

  /// 配置管理器
  late final PluginConfigManager _configManager;

  /// 更新管理器
  late final PluginUpdateManager _updateManager;

  /// 监控API
  late final PluginMonitoringApi _monitoringApi;

  /// 是否已启动
  bool get isStarted => _httpServer.isStarted;

  @override
  ApiVersion get version => _router.version;

  @override
  List<ApiVersion> get supportedVersions => _router.supportedVersions;

  @override
  void registerEndpoint(ApiEndpoint endpoint) {
    _router.registerEndpoint(endpoint);
  }

  @override
  void unregisterEndpoint(HttpMethod method, String path) {
    _router.unregisterEndpoint(method, path);
  }

  @override
  void addMiddleware(ApiMiddleware middleware) {
    _router.addMiddleware(middleware);
  }

  @override
  void removeMiddleware(String name) {
    _router.removeMiddleware(name);
  }

  @override
  Future<ApiResponse<dynamic>> handleRequest(ApiRequest request) async => _router.handleRequest(request);

  @override
  Map<String, dynamic> getApiDocumentation() => _router.getApiDocumentation();

  @override
  List<ApiEndpoint> getEndpoints() => _router.getEndpoints();

  @override
  bool validateVersion(ApiVersion requestVersion) =>
      _router.validateVersion(requestVersion);

  @override
  Future<void> start() async {
    try {
      // 注册默认端点
      _registerDefaultEndpoints();

      // 启动HTTP服务器
      await _httpServer.start();

      print(
          'Plugin REST API server started on ${_config.host}:${_config.port}',);
    } catch (e) {
      print('Failed to start Plugin REST API server: $e');
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    try {
      await _httpServer.stop();
      print('Plugin REST API server stopped');
    } catch (e) {
      print('Failed to stop Plugin REST API server: $e');
      rethrow;
    }
  }

  /// 注册默认端点
  void _registerDefaultEndpoints() {
    // 注册插件安装相关端点
    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.post,
      path: '/api/plugins/{id}/install',
      handler: (ApiRequest request) => _installationManager.installPlugin(
        _router.extractPathParameter(request.path, 'id') ?? '',
        version: request.queryParameters['version'] as String?,
        source: request.queryParameters['source'] as String?,
        config: request.body as Map<String, dynamic>?,
      ),
      description: '安装插件',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.delete,
      path: '/api/plugins/{id}/uninstall',
      handler: (ApiRequest request) => _installationManager.uninstallPlugin(
        _router.extractPathParameter(request.path, 'id') ?? '',
        force: request.queryParameters['force'] == 'true',
      ),
      description: '卸载插件',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    // 注册插件配置相关端点
    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.get,
      path: '/api/plugins/{id}/config',
      handler: (ApiRequest request) => _configManager.getPluginConfig(
        _router.extractPathParameter(request.path, 'id') ?? '',
      ),
      description: '获取插件配置',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.put,
      path: '/api/plugins/{id}/config',
      handler: (ApiRequest request) => _configManager.updatePluginConfig(
        _router.extractPathParameter(request.path, 'id') ?? '',
        request.body as Map<String, dynamic>,
      ),
      description: '更新插件配置',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    // 注册插件更新相关端点
    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.get,
      path: '/api/plugins/{id}/update/check',
      handler: (ApiRequest request) => _updateManager.checkPluginUpdate(
        _router.extractPathParameter(request.path, 'id') ?? '',
      ),
      description: '检查插件更新',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.post,
      path: '/api/plugins/{id}/update',
      handler: (ApiRequest request) => _updateManager.updatePlugin(
        _router.extractPathParameter(request.path, 'id') ?? '',
        targetVersion: request.queryParameters['version'] as String?,
      ),
      description: '更新插件',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    // 注册监控相关端点
    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.get,
      path: '/api/system/health',
      handler: (ApiRequest request) => _monitoringApi.getSystemHealth(),
      description: '获取系统健康状态',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.get,
      path: '/api/system/metrics',
      handler: (ApiRequest request) => _monitoringApi.getPerformanceMetrics(
        timeWindow: request.queryParameters['timeWindow'] != null
            ? Duration(
                minutes:
                    int.parse(request.queryParameters['timeWindow'] as String),)
            : null,
      ),
      description: '获取性能指标',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);

    _router.registerEndpoint(ApiEndpoint(
      method: HttpMethod.get,
      path: '/api/plugins/{id}/status',
      handler: (ApiRequest request) => _monitoringApi.getPluginStatus(
        _router.extractPathParameter(request.path, 'id') ?? '',
      ),
      description: '获取插件状态',
      parameters: <ApiParameter>[],
      responses: <int, String>{},
    ),);
  }

  // 以下是IPluginRestApi接口的其他方法实现，委托给相应的管理器

  @override
  Future<ApiResponse<Map<String, dynamic>>> installPlugin(
    String pluginId, {
    String? version,
    String? source,
    Map<String, dynamic>? config,
  }) async => _installationManager.installPlugin(
      pluginId,
      version: version,
      source: source,
      config: config,
    );

  @override
  Future<ApiResponse<Map<String, dynamic>>> uninstallPlugin(
    String pluginId, {
    bool force = false,
  }) async => _installationManager.uninstallPlugin(pluginId, force: force);

  @override
  Future<ApiResponse<Map<String, dynamic>>> getPluginConfig(
    String pluginId,
  ) async => _configManager.getPluginConfig(pluginId);

  @override
  Future<ApiResponse<Map<String, dynamic>>> updatePluginConfig(
    String pluginId,
    Map<String, dynamic> config,
  ) async => _configManager.updatePluginConfig(pluginId, config);

  Future<ApiResponse<Map<String, dynamic>>> getSystemHealth() async => _monitoringApi.getSystemHealth();

  @override
  Future<ApiResponse<Map<String, dynamic>>> getPerformanceMetrics({
    Duration? timeWindow,
  }) async => _monitoringApi.getPerformanceMetrics(timeWindow: timeWindow);

  // 其他方法的简化实现，主要用于向后兼容
  @override
  Future<ApiResponse<Map<String, dynamic>>> getPlugin(String pluginId) async {
    try {
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '插件不存在',
          statusCode: 404,
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'id': plugin.id,
          'name': plugin.name,
          'version': plugin.version,
          'description': plugin.description,
          'author': plugin.author,
          'state': plugin.currentState.name,
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '获取插件信息失败: $e',
        statusCode: 500,
      );
    }
  }

  Future<ApiResponse<List<Map<String, dynamic>>>> getAllPlugins() async {
    try {
      final plugins = registry.getAllPlugins();
      final pluginList = plugins
          .map(
            (Plugin plugin) => <String, dynamic>{
              'id': plugin.id,
              'name': plugin.name,
              'version': plugin.version,
              'description': plugin.description,
              'author': plugin.author,
              'state': plugin.currentState.name,
            },
          )
          .toList();

      return ApiResponse.success(data: pluginList);
    } catch (e) {
      return ApiResponse.error(
        message: '获取插件列表失败: $e',
        statusCode: 500,
      );
    }
  }

  // 其他接口方法的简化实现
  @override
  Future<ApiResponse<Map<String, dynamic>>> enablePlugin(
      String pluginId,) async {
    try {
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '插件不存在',
          statusCode: 404,
        );
      }

      // 检查插件当前状态
      if (plugin.isEnabled) {
        return ApiResponse.error(
          message: '插件已启用',
          statusCode: 409,
        );
      }

      // 启用插件
      await plugin.start();

      return ApiResponse.success(
        data: <String, dynamic>{
          'id': plugin.id,
          'name': plugin.name,
          'status': 'enabled',
          'message': '插件启用成功',
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '启用插件失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> disablePlugin(
      String pluginId,) async {
    try {
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '插件不存在',
          statusCode: 404,
        );
      }

      // 检查插件当前状态
      if (!plugin.isEnabled) {
        return ApiResponse.error(
          message: '插件未启用',
          statusCode: 409,
        );
      }

      // 禁用插件
      await plugin.stop();

      return ApiResponse.success(
        data: <String, dynamic>{
          'id': plugin.id,
          'name': plugin.name,
          'status': 'disabled',
          'message': '插件禁用成功',
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '禁用插件失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> updatePlugin(
    String pluginId, {
    String? version,
  }) async => _updateManager.updatePlugin(pluginId, targetVersion: version);

  Future<ApiResponse<Map<String, dynamic>>> checkPluginUpdate(
    String pluginId,
  ) async => _updateManager.checkPluginUpdate(pluginId);

  @override
  Future<ApiResponse<Map<String, dynamic>>> getSystemConfig() async => _configManager.getSystemConfig();

  @override
  Future<ApiResponse<Map<String, dynamic>>> updateSystemConfig(
    Map<String, dynamic> config,
  ) async => _configManager.updateSystemConfig(config);

  Future<ApiResponse<Map<String, dynamic>>> getPluginLogs({
    String? pluginId,
    String? level,
    int? limit,
    DateTime? since,
  }) async => _monitoringApi.getPluginLogs(
      pluginId: pluginId,
      level: level,
      limit: limit,
      since: since,
    );

  Future<ApiResponse<Map<String, dynamic>>> validatePluginPackage(
    List<int> packageData,
  ) async => _installationManager.validatePluginPackage(packageData);

  // 实现缺失的接口方法
  @override
  Future<ApiResponse<Map<String, dynamic>>> batchInstallPlugins(
    List<String> pluginIds, {
    Map<String, String>? versions,
    Map<String, String>? sources,
  }) async {
    // 委托给批量管理器
    try {
      final results = <String, dynamic>{};
      for (final pluginId in pluginIds) {
        final result = await installPlugin(
          pluginId,
          version: versions?[pluginId],
          source: sources?[pluginId],
        );
        results[pluginId] = result.toJson();
      }
      return ApiResponse.success(data: results);
    } catch (e) {
      return ApiResponse.error(
        message: '批量安装失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> batchUninstallPlugins(
    List<String> pluginIds, {
    bool force = false,
  }) async {
    try {
      final results = <String, dynamic>{};
      for (final pluginId in pluginIds) {
        final result = await uninstallPlugin(pluginId, force: force);
        results[pluginId] = result.toJson();
      }
      return ApiResponse.success(data: results);
    } catch (e) {
      return ApiResponse.error(
        message: '批量卸载失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> batchUpdatePlugins(
    List<String> pluginIds, {
    Map<String, String>? targetVersions,
  }) async {
    try {
      final results = <String, dynamic>{};
      for (final pluginId in pluginIds) {
        final result = await updatePlugin(
          pluginId,
          version: targetVersions?[pluginId],
        );
        results[pluginId] = result.toJson();
      }
      return ApiResponse.success(data: results);
    } catch (e) {
      return ApiResponse.error(
        message: '批量更新失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<List<Map<String, dynamic>>>> getPlugins({
    int? limit,
    int? page,
    String? search,
    List<String>? tags,
  }) async {
    try {
      final allPlugins = registry.getAllPlugins();
      var filteredPlugins = allPlugins;

      // 按搜索关键词过滤
      if (search != null && search.isNotEmpty) {
        filteredPlugins = filteredPlugins
            .where((Plugin plugin) =>
                plugin.name.toLowerCase().contains(search.toLowerCase()) ||
                plugin.description.toLowerCase().contains(search.toLowerCase()),)
            .toList();
      }

      // 按标签过滤（简化实现）
      if (tags != null && tags.isNotEmpty) {
        filteredPlugins = filteredPlugins
            .where((Plugin plugin) => tags.any((String tag) =>
                plugin.description.toLowerCase().contains(tag.toLowerCase()),),)
            .toList();
      }

      // 分页处理
      final offset = page != null ? (page - 1) * (limit ?? 10) : 0;
      if (offset > 0) {
        filteredPlugins = filteredPlugins.skip(offset).toList();
      }
      if (limit != null) {
        filteredPlugins = filteredPlugins.take(limit).toList();
      }

      final pluginList = filteredPlugins
          .map(
            (Plugin plugin) => <String, dynamic>{
              'id': plugin.id,
              'name': plugin.name,
              'version': plugin.version,
              'description': plugin.description,
              'author': plugin.author,
              'category': plugin.category.name,
              'state': plugin.currentState.name,
            },
          )
          .toList();

      return ApiResponse.success(data: pluginList);
    } catch (e) {
      return ApiResponse.error(
        message: '获取插件列表失败: $e',
        statusCode: 500,
      );
    }
  }

  @override
  Future<ApiResponse<Map<String, dynamic>>> getSystemStatus() async => _monitoringApi.getSystemHealth();

  @override
  Future<ApiResponse<List<Map<String, dynamic>>>> getSystemLogs({
    DateTime? startTime,
    DateTime? endTime,
    String? level,
    int? limit,
    int? page,
  }) async {
    try {
      // 模拟日志数据
      final List<Map<String, dynamic>> logs = <Map<String, dynamic>>[
        <String, dynamic>{
          'timestamp': DateTime.now().toIso8601String(),
          'level': level ?? 'INFO',
          'message': '系统运行正常',
          'source': 'plugin_system',
        },
      ];

      // 分页处理
      final int offset = page != null ? (page - 1) * (limit ?? 10) : 0;
      List<Map<String, dynamic>> filteredLogs = logs;

      if (offset > 0) {
        filteredLogs = filteredLogs.skip(offset).toList();
      }
      if (limit != null) {
        filteredLogs = filteredLogs.take(limit).toList();
      }

      return ApiResponse.success(data: filteredLogs);
    } on Exception catch (e) {
      return ApiResponse.error(
        message: '获取系统日志失败: $e',
        statusCode: 500,
      );
    }
  }
}
