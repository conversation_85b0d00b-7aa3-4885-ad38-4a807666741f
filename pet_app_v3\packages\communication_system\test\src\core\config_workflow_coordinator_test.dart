/*
---------------------------------------------------------------
File name:          config_workflow_coordinator_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        ConfigWorkflowCoordinator 测试文件
---------------------------------------------------------------
*/

import 'dart:async';
import 'package:flutter_test/flutter_test.dart';
import 'package:communication_system/src/core/config_workflow_coordinator.dart';
import 'package:communication_system/src/core/module_communication_coordinator.dart';
import 'package:communication_system/src/core/unified_message_bus.dart';
import 'package:communication_system/src/core/data_sync_manager.dart';
import 'package:communication_system/src/core/conflict_resolution_engine.dart';

void main() {
  group('ConfigWorkflowCoordinator Tests', () {
    late ConfigWorkflowCoordinator coordinator;
    late ModuleCommunicationCoordinator moduleCoordinator;
    late UnifiedMessageBus messageBus;
    late DataSyncManager syncManager;
    late ConflictResolutionEngine conflictEngine;

    setUp(() {
      coordinator = ConfigWorkflowCoordinator.instance;
      moduleCoordinator = ModuleCommunicationCoordinator.instance;
      messageBus = UnifiedMessageBus.instance;
      syncManager = DataSyncManager.instance;
      conflictEngine = ConflictResolutionEngine.instance;

      // 初始化冲突解决引擎
      conflictEngine.initialize();

      // 注册测试模块
      moduleCoordinator.registerModule(
        ModuleInfo(
          id: 'test_source',
          name: 'Test Source Module',
          version: '1.0.0',
          type: 'test',
          priority: 10,
        ),
      );

      moduleCoordinator.registerModule(
        ModuleInfo(
          id: 'test_target',
          name: 'Test Target Module',
          version: '1.0.0',
          type: 'test',
          priority: 5,
        ),
      );

      // 注册同步配置
      syncManager.registerSyncConfig(
        SyncConfig(
          moduleId: 'test_target',
          dataKeys: {'config'},
          strategy: SyncStrategy.realtime,
        ),
      );
    });

    tearDown(() {
      // 清理资源
      moduleCoordinator.dispose();
      syncManager.dispose();
      conflictEngine.dispose();
      messageBus.dispose();
    });

    group('Basic Functionality', () {
      test('should create instance successfully', () {
        expect(coordinator, isNotNull);
        expect(coordinator, same(ConfigWorkflowCoordinator.instance));
      });

      test('should start config workflow successfully', () async {
        final configChanges = {
          'theme': 'dark',
          'language': 'zh-CN',
          'notifications': true,
        };

        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          targetModules: ['test_target'],
        );

        expect(result.isSuccess, isTrue);
        expect(result.appliedModules, contains('test_target'));
        expect(result.failedModules, isEmpty);
        expect(result.workflowId, isNotEmpty);
      });

      test('should handle empty config changes', () async {
        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: {},
          targetModules: ['test_target'],
        );

        expect(result.isSuccess, isFalse);
        expect(result.error, contains('配置验证失败'));
      });

      test('should handle non-existent source module', () async {
        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'non_existent',
          configChanges: {'test': 'value'},
          targetModules: ['test_target'],
        );

        expect(result.isSuccess, isFalse);
        expect(result.error, contains('源模块未注册'));
      });

      test('should handle non-existent target module', () async {
        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: {'test': 'value'},
          targetModules: ['non_existent'],
        );

        expect(result.isSuccess, isFalse);
        expect(result.error, contains('目标模块未注册'));
      });
    });

    group('Workflow Management', () {
      test('should track workflow status', () async {
        final configChanges = {'test': 'value'};

        final resultFuture = coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          targetModules: ['test_target'],
        );

        // 等待工作流开始
        await Future.delayed(const Duration(milliseconds: 10));

        final result = await resultFuture;
        final status = coordinator.getWorkflowStatus(result.workflowId);

        expect(status, equals(ConfigWorkflowStatus.completed));
      });

      test('should maintain workflow history', () async {
        final configChanges = {'test': 'value'};

        await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          targetModules: ['test_target'],
        );

        final history = coordinator.getWorkflowHistory();
        expect(history, isNotEmpty);
        expect(history.first.status, equals(ConfigWorkflowStatus.completed));
      });

      test('should cleanup expired workflows', () {
        // 添加一些历史记录
        coordinator.cleanupExpiredWorkflows(maxAge: const Duration(milliseconds: 1));

        // 验证清理功能不会崩溃
        expect(() => coordinator.cleanupExpiredWorkflows(), returnsNormally);
      });
    });

    group('Broadcast Configuration', () {
      test('should broadcast to all modules when no targets specified', () async {
        // 注册另一个模块
        moduleCoordinator.registerModule(
          ModuleInfo(
            id: 'test_target2',
            name: 'Test Target Module 2',
            version: '1.0.0',
            type: 'test',
          ),
        );

        syncManager.registerSyncConfig(
          SyncConfig(
            moduleId: 'test_target2',
            dataKeys: {'config'},
            strategy: SyncStrategy.realtime,
          ),
        );

        final configChanges = {'global': 'setting'};

        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          // 不指定目标模块，应该广播到所有模块
        );

        expect(result.isSuccess, isTrue);
        expect(result.appliedModules.length, greaterThanOrEqualTo(1));
      });
    });

    group('Error Handling', () {
      test('should handle workflow exceptions gracefully', () async {
        // 使用无效的配置触发异常
        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: {'test': 'value'},
          targetModules: ['test_target'],
        );

        // 即使有异常，也应该返回结果而不是抛出异常
        expect(result, isNotNull);
        expect(result.workflowId, isNotEmpty);
      });

      test('should handle rollback scenarios', () async {
        final configChanges = {'test': 'value'};

        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          targetModules: ['test_target'],
        );

        // 验证回滚不会导致异常
        expect(result, isNotNull);
      });
    });

    group('Conflict Detection', () {
      test('should detect and resolve configuration conflicts', () async {
        final configChanges = {
          'conflicting_setting': 'value1',
          'timestamp': DateTime.now().toIso8601String(),
        };

        final result = await coordinator.startConfigWorkflow(
          sourceModule: 'test_source',
          configChanges: configChanges,
          targetModules: ['test_target'],
        );

        expect(result, isNotNull);
        expect(result.workflowId, isNotEmpty);
        // 冲突解决应该不会阻止工作流完成
      });
    });

    group('Performance', () {
      test('should handle multiple concurrent workflows', () async {
        final futures = <Future<ConfigWorkflowResult>>[];

        // 启动多个并发工作流
        for (int i = 0; i < 5; i++) {
          futures.add(
            coordinator.startConfigWorkflow(
              sourceModule: 'test_source',
              configChanges: {'test_$i': 'value_$i'},
              targetModules: ['test_target'],
            ),
          );
        }

        final results = await Future.wait(futures);

        // 验证所有工作流都完成
        expect(results.length, equals(5));
        for (final result in results) {
          expect(result.workflowId, isNotEmpty);
        }
      });
    });
  });
}
