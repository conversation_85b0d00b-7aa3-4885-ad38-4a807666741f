/*
---------------------------------------------------------------
File name:          app_store_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        应用商店服务测试 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 应用商店服务测试实现;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:app_manager/src/services/app_store_service.dart';

void main() {
  group('AppStoreService Tests', () {
    late AppStoreService service;

    setUp(() {
      service = AppStoreService.instance;
    });

    tearDown(() {
      // 清理测试环境
    });

    test('should be singleton', () {
      final service1 = AppStoreService.instance;
      final service2 = AppStoreService.instance;
      expect(service1, same(service2));
    });

    test('should initialize successfully', () async {
      expect(service.isInitialized, isFalse);
      
      // 注意：实际测试中需要mock Plugin System
      // await service.initialize();
      // expect(service.isInitialized, isTrue);
    });

    test('should handle search queries', () async {
      // 注意：实际测试中需要mock Plugin System
      // final results = await service.searchPlugins('test');
      // expect(results, isA<List<plugin_sys.PluginStoreEntry>>());
    });

    test('should filter by category', () async {
      // 注意：实际测试中需要mock Plugin System
      // final results = await service.filterByCategory('tools');
      // expect(results, isA<List<plugin_sys.PluginStoreEntry>>());
    });

    test('should get available categories', () {
      final categories = service.getAvailableCategories();
      expect(categories, isA<List<String>>());
    });

    test('should handle errors gracefully', () async {
      // 测试错误处理
      expect(service.error, isNull);
    });

    test('should notify listeners on state changes', () {
      var notified = false;
      service.addListener(() {
        notified = true;
      });

      // 触发状态变化
      // service.notifyListeners();
      
      // expect(notified, isTrue);
    });

    test('should manage loading state', () {
      expect(service.isLoading, isFalse);
    });

    test('should handle refresh store', () async {
      // 注意：实际测试中需要mock Plugin System
      // await service.refreshStore();
      // 验证刷新逻辑
    });

    test('should emit events correctly', () async {
      var eventReceived = false;
      
      service.events.listen((event) {
        eventReceived = true;
      });

      // 触发事件
      // await service.initialize();
      
      // expect(eventReceived, isTrue);
    });
  });

  group('AppStoreEvent Tests', () {
    test('should create initialized event', () {
      final event = AppStoreEvent.initialized();
      expect(event.type, equals('initialized'));
      expect(event.data, isEmpty);
    });

    test('should create search completed event', () {
      final event = AppStoreEvent.searchCompleted('test', 5);
      expect(event.type, equals('searchCompleted'));
      expect(event.data['query'], equals('test'));
      expect(event.data['resultCount'], equals(5));
    });

    test('should create category changed event', () {
      final event = AppStoreEvent.categoryChanged('tools');
      expect(event.type, equals('categoryChanged'));
      expect(event.data['category'], equals('tools'));
    });

    test('should create plugin details loaded event', () {
      final event = AppStoreEvent.pluginDetailsLoaded('plugin1');
      expect(event.type, equals('pluginDetailsLoaded'));
      expect(event.data['pluginId'], equals('plugin1'));
    });

    test('should create plugin installed event', () {
      final event = AppStoreEvent.pluginInstalled('plugin1');
      expect(event.type, equals('pluginInstalled'));
      expect(event.data['pluginId'], equals('plugin1'));
    });

    test('should create store refreshed event', () {
      final event = AppStoreEvent.storeRefreshed();
      expect(event.type, equals('storeRefreshed'));
      expect(event.data, isEmpty);
    });

    test('should create error event', () {
      final event = AppStoreEvent.error('Test error');
      expect(event.type, equals('error'));
      expect(event.data['message'], equals('Test error'));
    });
  });
}
