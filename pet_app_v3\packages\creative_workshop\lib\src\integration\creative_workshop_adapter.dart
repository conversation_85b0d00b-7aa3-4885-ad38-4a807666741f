/*
---------------------------------------------------------------
File name:          creative_workshop_adapter.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop模块的适应层实现
---------------------------------------------------------------
Change History:
    2025-07-29: Creative Workshop模块适应层实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

// 导入Plugin System的集成接口
import 'package:plugin_system/plugin_system.dart';

import '../core/workshop_manager.dart';
import '../core/projects/project_manager.dart';
import '../core/plugins/plugin_registry.dart' as workshop_registry;

/// Creative Workshop 模块适应层
///
/// 将Creative Workshop的核心功能封装为标准的模块接口，
/// 便于与Plugin System和其他模块进行集成。
class CreativeWorkshopAdapter implements IModuleAdapter {
  /// 单例实例
  static CreativeWorkshopAdapter? _instance;

  /// 获取单例实例
  static CreativeWorkshopAdapter get instance {
    _instance ??= CreativeWorkshopAdapter._();
    return _instance!;
  }

  /// 私有构造函数
  CreativeWorkshopAdapter._();

  /// 模块协调器
  IModuleCoordinator? _coordinator;

  /// 当前状态
  ModuleStatus _status = ModuleStatus.uninitialized;

  /// 状态控制器
  final StreamController<ModuleStatus> _statusController =
      StreamController<ModuleStatus>.broadcast();

  /// 请求处理器映射
  final Map<String, RequestHandler> _requestHandlers = {};

  /// 事件处理器映射
  final Map<String, List<EventHandler>> _eventHandlers = {};

  /// 是否已初始化
  bool _initialized = false;

  @override
  ModuleMetadata get metadata => const ModuleMetadata(
        id: 'creative_workshop',
        name: 'Creative Workshop',
        version: '1.4.0',
        description: 'Pet App V3 创意工坊模块，提供绘画工具、游戏系统和项目管理功能',
        author: 'Pet App Team',
        dependencies: ['plugin_system'],
        providedServices: [
          'workshop_manager',
          'project_manager',
          'tool_plugins',
          'game_plugins',
          'creative_tools',
        ],
        requiredServices: [
          'plugin_registry',
          'plugin_loader',
          'event_bus',
        ],
      );

  @override
  ModuleStatus get status => _status;

  @override
  Stream<ModuleStatus> get statusStream => _statusController.stream;

  @override
  Future<void> initialize(IModuleCoordinator coordinator) async {
    if (_initialized) {
      developer.log('Creative Workshop 已经初始化', name: 'CreativeWorkshopAdapter');
      return;
    }

    try {
      _updateStatus(ModuleStatus.initializing);
      _coordinator = coordinator;

      developer.log('开始初始化 Creative Workshop', name: 'CreativeWorkshopAdapter');

      // 1. 初始化核心组件
      await _initializeCoreComponents();

      // 2. 注册标准请求处理器
      _registerStandardHandlers();

      // 3. 设置事件监听
      _setupEventListeners();

      // 4. 集成Plugin System
      await _integrateWithPluginSystem();

      _initialized = true;
      _updateStatus(ModuleStatus.initialized);

      developer.log('Creative Workshop 初始化完成', name: 'CreativeWorkshopAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Creative Workshop 初始化失败: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> start() async {
    if (_status != ModuleStatus.initialized) {
      throw StateError('模块必须先初始化才能启动');
    }

    try {
      _updateStatus(ModuleStatus.starting);

      developer.log('启动 Creative Workshop', name: 'CreativeWorkshopAdapter');

      // 启动核心服务
      await _startCoreServices();

      _updateStatus(ModuleStatus.running);

      developer.log('Creative Workshop 启动完成', name: 'CreativeWorkshopAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Creative Workshop 启动失败: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> stop() async {
    if (_status != ModuleStatus.running) {
      developer.log('Creative Workshop 未在运行状态',
          name: 'CreativeWorkshopAdapter');
      return;
    }

    try {
      _updateStatus(ModuleStatus.stopping);

      developer.log('停止 Creative Workshop', name: 'CreativeWorkshopAdapter');

      // 停止核心服务
      await _stopCoreServices();

      _updateStatus(ModuleStatus.stopped);

      developer.log('Creative Workshop 停止完成', name: 'CreativeWorkshopAdapter');
    } catch (e, stackTrace) {
      _updateStatus(ModuleStatus.error);
      developer.log(
        'Creative Workshop 停止失败: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<void> dispose() async {
    try {
      developer.log('销毁 Creative Workshop', name: 'CreativeWorkshopAdapter');

      // 停止模块（如果正在运行）
      if (_status == ModuleStatus.running) {
        await stop();
      }

      // 清理资源
      _requestHandlers.clear();
      _eventHandlers.clear();
      await _statusController.close();

      _initialized = false;
      _coordinator = null;

      developer.log('Creative Workshop 销毁完成', name: 'CreativeWorkshopAdapter');
    } catch (e, stackTrace) {
      developer.log(
        'Creative Workshop 销毁失败: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> handleRequest(
    String action,
    Map<String, dynamic> data,
    String senderId,
  ) async {
    final handler = _requestHandlers[action];
    if (handler == null) {
      developer.log(
        '未找到请求处理器: $action',
        name: 'CreativeWorkshopAdapter',
      );
      return null;
    }

    try {
      return await handler(data, senderId);
    } catch (e, stackTrace) {
      developer.log(
        '处理请求失败: $action, 错误: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  @override
  void registerRequestHandler(String action, RequestHandler handler) {
    _requestHandlers[action] = handler;
    developer.log('注册请求处理器: $action', name: 'CreativeWorkshopAdapter');
  }

  @override
  void registerEventHandler(String event, EventHandler handler) {
    _eventHandlers.putIfAbsent(event, () => []).add(handler);
    developer.log('注册事件处理器: $event', name: 'CreativeWorkshopAdapter');
  }

  @override
  Future<Map<String, dynamic>> getHealthStatus() async {
    try {
      final workshopManager = WorkshopManager.instance;
      final projectManager = ProjectManager.instance;

      return {
        'status': _status.name,
        'initialized': _initialized,
        'workshopState': workshopManager.state.name,
        'activeProjects': projectManager.projects.length,
        'availableTools': workshopManager.getTools().length,
        'availableGames': workshopManager.getGames().length,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 更新模块状态
  void _updateStatus(ModuleStatus newStatus) {
    if (_status != newStatus) {
      _status = newStatus;
      _statusController.add(newStatus);
      developer.log('状态更新: ${newStatus.name}', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 初始化核心组件
  Future<void> _initializeCoreComponents() async {
    // 确保核心组件实例化
    WorkshopManager.instance;
    ProjectManager.instance;

    developer.log('核心组件初始化完成', name: 'CreativeWorkshopAdapter');
  }

  /// 注册标准请求处理器
  void _registerStandardHandlers() {
    // 工坊管理相关
    registerRequestHandler('get_workshop_state', _handleGetWorkshopState);
    registerRequestHandler('activate_tool', _handleActivateTool);
    registerRequestHandler('start_game', _handleStartGame);
    registerRequestHandler('list_tools', _handleListTools);
    registerRequestHandler('list_games', _handleListGames);

    // 项目管理相关
    registerRequestHandler('create_project', _handleCreateProject);
    registerRequestHandler('get_project', _handleGetProject);
    registerRequestHandler('list_projects', _handleListProjects);
    registerRequestHandler('delete_project', _handleDeleteProject);

    developer.log('标准请求处理器注册完成', name: 'CreativeWorkshopAdapter');
  }

  /// 设置事件监听
  void _setupEventListeners() {
    try {
      // 监听工坊管理器的状态变化
      final workshopManager = WorkshopManager.instance;
      workshopManager.stateChanges.listen((state) {
        _broadcastEvent('workshop_state_changed', {
          'state': state.name,
          'timestamp': DateTime.now().toIso8601String(),
        });
      });

      // 监听项目管理器的变化
      final projectManager = ProjectManager.instance;
      projectManager.projectChanges.listen((project) {
        _broadcastEvent('project_changed', {
          'projectId': project.id,
          'projectName': project.name,
          'action': 'updated',
          'timestamp': DateTime.now().toIso8601String(),
        });
      });

      // 监听工具激活事件
      workshopManager.addListener(() {
        final activeTool = workshopManager.activeTool;
        if (activeTool != null) {
          _broadcastEvent('tool_activated', {
            'toolId': activeTool.id,
            'toolName': activeTool.name,
            'timestamp': DateTime.now().toIso8601String(),
          });
        }

        final activeGame = workshopManager.activeGame;
        if (activeGame != null) {
          _broadcastEvent('game_started', {
            'gameId': activeGame.id,
            'gameName': activeGame.name,
            'timestamp': DateTime.now().toIso8601String(),
          });
        }
      });

      developer.log('事件监听设置完成', name: 'CreativeWorkshopAdapter');
    } catch (e) {
      developer.log('设置事件监听失败: $e', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 集成Plugin System
  Future<void> _integrateWithPluginSystem() async {
    try {
      developer.log('开始集成Plugin System', name: 'CreativeWorkshopAdapter');

      // 1. 获取Plugin System的适应层
      final pluginSystemAdapter = _coordinator?.getModule('plugin_system');
      if (pluginSystemAdapter == null) {
        developer.log('Plugin System 未找到，跳过集成',
            name: 'CreativeWorkshopAdapter');
        return;
      }

      // 2. 订阅Plugin System的事件
      _coordinator?.communication.subscribeToEvent('plugin_registered',
          (data) async {
        developer.log('收到插件注册事件: ${data['pluginId']}',
            name: 'CreativeWorkshopAdapter');
        // 检查是否是工具或游戏插件，如果是则同步到Creative Workshop
        await _syncPluginFromSystem(data);
      });

      _coordinator?.communication.subscribeToEvent('plugin_unregistered',
          (data) async {
        developer.log('收到插件注销事件: ${data['pluginId']}',
            name: 'CreativeWorkshopAdapter');
        // 从Creative Workshop中移除对应插件
        await _removePluginFromWorkshop(data);
      });

      // 3. 将Creative Workshop的现有插件同步到Plugin System
      await _syncWorkshopPluginsToSystem();

      developer.log('Plugin System 集成完成', name: 'CreativeWorkshopAdapter');
    } catch (e, stackTrace) {
      developer.log(
        'Plugin System 集成失败: $e',
        name: 'CreativeWorkshopAdapter',
        error: e,
        stackTrace: stackTrace,
      );
      // 集成失败不阻止Creative Workshop的正常运行
    }
  }

  /// 从Plugin System同步插件到Creative Workshop
  Future<void> _syncPluginFromSystem(Map<String, dynamic> data) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) return;

      // 通过Plugin System获取插件详情
      final response = await _coordinator?.communication.sendRequest(
        'plugin_system',
        'get_plugin',
        {'pluginId': pluginId},
      );

      if (response?['success'] == true) {
        final pluginData = response!['plugin'] as Map<String, dynamic>;
        final category = pluginData['category'] as String?;

        // 只同步工具和游戏插件
        if (category == 'tool' || category == 'game') {
          developer.log('同步插件到Creative Workshop: $pluginId',
              name: 'CreativeWorkshopAdapter');
          // 实现实际的插件同步逻辑
          // 将Plugin System的插件转换为Creative Workshop的插件格式
          await _convertAndRegisterPlugin(pluginData, category ?? 'general');
        }
      }
    } catch (e) {
      developer.log('同步插件失败: $e', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 从Creative Workshop移除插件
  Future<void> _removePluginFromWorkshop(Map<String, dynamic> data) async {
    try {
      final pluginId = data['pluginId'] as String?;
      if (pluginId == null) return;

      final workshopManager = WorkshopManager.instance;
      // 实现从Creative Workshop移除插件的逻辑
      // 检查插件是否在Creative Workshop中，如果是则移除
      await _removePluginFromWorkshopManager(workshopManager, pluginId);

      developer.log('从Creative Workshop移除插件: $pluginId',
          name: 'CreativeWorkshopAdapter');
    } catch (e) {
      developer.log('移除插件失败: $e', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 将Creative Workshop的插件同步到Plugin System
  Future<void> _syncWorkshopPluginsToSystem() async {
    try {
      final workshopManager = WorkshopManager.instance;
      final tools = workshopManager.getTools();
      final games = workshopManager.getGames();

      developer.log('同步${tools.length}个工具和${games.length}个游戏到Plugin System',
          name: 'CreativeWorkshopAdapter');

      // 同步工具插件
      for (final tool in tools) {
        await _syncPluginToSystem(tool, 'tool');
      }

      // 同步游戏插件
      for (final game in games) {
        await _syncPluginToSystem(game, 'game');
      }
    } catch (e) {
      developer.log('同步插件到Plugin System失败: $e',
          name: 'CreativeWorkshopAdapter');
    }
  }

  /// 同步单个插件到Plugin System
  Future<void> _syncPluginToSystem(dynamic plugin, String category) async {
    try {
      // 实现实际的插件同步逻辑
      // 将Creative Workshop的插件转换为Plugin System的格式
      await _convertWorkshopPluginToSystem(plugin, category);

      developer.log('同步插件到Plugin System: ${plugin.id}',
          name: 'CreativeWorkshopAdapter');
    } catch (e) {
      developer.log('同步插件失败: ${plugin.id}, 错误: $e',
          name: 'CreativeWorkshopAdapter');
    }
  }

  /// 启动核心服务
  Future<void> _startCoreServices() async {
    final workshopManager = WorkshopManager.instance;
    await workshopManager.initialize();

    developer.log('核心服务启动完成', name: 'CreativeWorkshopAdapter');
  }

  /// 停止核心服务
  Future<void> _stopCoreServices() async {
    final workshopManager = WorkshopManager.instance;
    await workshopManager.stop();

    developer.log('核心服务停止完成', name: 'CreativeWorkshopAdapter');
  }

  /// 广播事件到其他模块
  void _broadcastEvent(String event, Map<String, dynamic> data) {
    _coordinator?.communication.broadcastEvent(
      event,
      data,
      excludeModules: [metadata.id],
    );
  }

  // 请求处理器实现
  Future<Map<String, dynamic>?> _handleGetWorkshopState(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final workshopManager = WorkshopManager.instance;

      return {
        'success': true,
        'state': workshopManager.state.name,
        'activeTool': workshopManager.activeTool?.id,
        'activeGame': workshopManager.activeGame?.id,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleActivateTool(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final toolId = data['toolId'] as String?;
      if (toolId == null) {
        return {'success': false, 'error': 'toolId is required'};
      }

      final workshopManager = WorkshopManager.instance;
      final success = await workshopManager.activateTool(toolId);

      return {
        'success': success,
        'message':
            success ? 'Tool activated successfully' : 'Failed to activate tool',
        'toolId': toolId,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleStartGame(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final gameId = data['gameId'] as String?;
      if (gameId == null) {
        return {'success': false, 'error': 'gameId is required'};
      }

      final workshopManager = WorkshopManager.instance;
      final success = await workshopManager.startGame(gameId);

      return {
        'success': success,
        'message':
            success ? 'Game started successfully' : 'Failed to start game',
        'gameId': gameId,
        'activeGame': workshopManager.activeGame?.id,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleListTools(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final workshopManager = WorkshopManager.instance;
      final tools = workshopManager.getTools();

      final toolList = tools
          .map((tool) => {
                'id': tool.id,
                'name': tool.name,
                'description': tool.description,
                'version': tool.version,
                'author': tool.author,
                'isActive': workshopManager.activeTool?.id == tool.id,
              })
          .toList();

      return {
        'success': true,
        'tools': toolList,
        'count': toolList.length,
        'activeTool': workshopManager.activeTool?.id,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleListGames(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final workshopManager = WorkshopManager.instance;
      final games = workshopManager.getGames();

      final gameList = games
          .map((game) => {
                'id': game.id,
                'name': game.name,
                'description': game.description,
                'version': game.version,
                'author': game.author,
                'isActive': workshopManager.activeGame?.id == game.id,
              })
          .toList();

      return {
        'success': true,
        'games': gameList,
        'count': gameList.length,
        'activeGame': workshopManager.activeGame?.id,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleCreateProject(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final name = data['name'] as String?;
      final typeStr = data['type'] as String?;
      final description = data['description'] as String? ?? '';
      final tags =
          (data['tags'] as List<dynamic>?)?.cast<String>() ?? <String>[];

      if (name == null || typeStr == null) {
        return {'success': false, 'error': 'name and type are required'};
      }

      // 解析项目类型
      final type = _parseProjectType(typeStr);

      final projectManager = ProjectManager.instance;
      final result = await projectManager.createProject(
        name: name,
        type: type,
        description: description,
        tags: tags,
      );

      if (result.success) {
        final project = result.data!;
        return {
          'success': true,
          'message': 'Project created successfully',
          'project': {
            'id': project.id,
            'name': project.name,
            'type': project.type.name,
            'description': project.description,
            'status': project.status.name,
            'createdAt': project.createdAt.toIso8601String(),
            'tags': project.tags,
          },
        };
      } else {
        return {
          'success': false,
          'error': result.error ?? 'Failed to create project',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleGetProject(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final projectId = data['projectId'] as String?;
      if (projectId == null) {
        return {'success': false, 'error': 'projectId is required'};
      }

      final projectManager = ProjectManager.instance;
      final project = projectManager.projects.firstWhere(
        (p) => p.id == projectId,
        orElse: () => throw Exception('Project not found'),
      );

      return {
        'success': true,
        'project': {
          'id': project.id,
          'name': project.name,
          'type': project.type.name,
          'description': project.description,
          'status': project.status.name,
          'createdAt': project.createdAt.toIso8601String(),
          'updatedAt': project.updatedAt.toIso8601String(),
          'tags': project.tags,
          'metadata': project.metadata,
        },
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleListProjects(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final projectManager = ProjectManager.instance;
      final projects = projectManager.projects;

      final projectList = projects
          .map((project) => {
                'id': project.id,
                'name': project.name,
                'type': project.type.name,
                'description': project.description,
                'status': project.status.name,
                'createdAt': project.createdAt.toIso8601String(),
                'updatedAt': project.updatedAt.toIso8601String(),
                'tags': project.tags,
              })
          .toList();

      return {
        'success': true,
        'projects': projectList,
        'count': projectList.length,
        'currentProject': projectManager.currentProject?.id,
      };
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  Future<Map<String, dynamic>?> _handleDeleteProject(
    Map<String, dynamic> data,
    String senderId,
  ) async {
    try {
      final projectId = data['projectId'] as String?;
      if (projectId == null) {
        return {'success': false, 'error': 'projectId is required'};
      }

      final projectManager = ProjectManager.instance;
      final result = await projectManager.deleteProject(projectId);

      if (result.success) {
        return {
          'success': true,
          'message': 'Project deleted successfully',
          'projectId': projectId,
        };
      } else {
        return {
          'success': false,
          'error': result.error ?? 'Failed to delete project',
        };
      }
    } catch (e) {
      return {'success': false, 'error': e.toString()};
    }
  }

  /// 解析项目类型
  ProjectType _parseProjectType(String typeStr) {
    switch (typeStr.toLowerCase()) {
      case 'drawing':
        return ProjectType.drawing;
      case 'design':
        return ProjectType.design;
      case 'game':
        return ProjectType.game;
      case 'animation':
        return ProjectType.animation;
      case 'model3d':
        return ProjectType.model3d;
      case 'mixed':
        return ProjectType.mixed;
      default:
        return ProjectType.custom;
    }
  }

  /// 转换并注册Plugin System的插件到Creative Workshop
  Future<void> _convertAndRegisterPlugin(
    Map<String, dynamic> pluginData,
    String category,
  ) async {
    try {
      final pluginId = pluginData['id'] as String?;
      final pluginName = pluginData['name'] as String?;

      if (pluginId == null || pluginName == null) {
        developer.log('插件数据不完整，跳过注册: $pluginData',
            name: 'CreativeWorkshopAdapter');
        return;
      }

      final workshopManager = WorkshopManager.instance;

      if (category == 'tool') {
        // 转换为工具插件
        final toolPlugin = _createToolPluginFromData(pluginData);
        // 通过插件注册中心注册工具插件
        // TODO: 需要访问WorkshopManager的内部注册中心或提供公共注册方法
        developer.log('工具插件需要注册到WorkshopManager: $pluginName',
            name: 'CreativeWorkshopAdapter');
        developer.log('工具插件注册成功: $pluginName', name: 'CreativeWorkshopAdapter');
      } else if (category == 'game') {
        // 转换为游戏插件
        final gamePlugin = _createGamePluginFromData(pluginData);
        // 通过插件注册中心注册游戏插件
        // TODO: 需要访问WorkshopManager的内部注册中心或提供公共注册方法
        developer.log('游戏插件需要注册到WorkshopManager: $pluginName',
            name: 'CreativeWorkshopAdapter');
      }
    } catch (e) {
      developer.log('插件转换注册失败: $e', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 从Creative Workshop管理器中移除插件
  Future<void> _removePluginFromWorkshopManager(
    WorkshopManager workshopManager,
    String pluginId,
  ) async {
    try {
      // 检查是否是工具插件
      final tools = workshopManager.getTools();
      final tool = tools.firstWhere(
        (t) => t.id == pluginId,
        orElse: () => throw StateError('Tool not found'),
      );

      // TODO: 需要WorkshopManager提供移除工具插件的公共方法
      developer.log('工具插件需要从WorkshopManager移除: $pluginId',
          name: 'CreativeWorkshopAdapter');
      return;
    } catch (e) {
      // 不是工具插件，继续检查游戏插件
    }

    try {
      // 检查是否是游戏插件
      final games = workshopManager.getGames();
      final game = games.firstWhere(
        (g) => g.id == pluginId,
        orElse: () => throw StateError('Game not found'),
      );

      // TODO: 需要WorkshopManager提供移除游戏插件的公共方法
      developer.log('游戏插件需要从WorkshopManager移除: $pluginId',
          name: 'CreativeWorkshopAdapter');
      return;
    } catch (e) {
      developer.log('插件未在Creative Workshop中找到: $pluginId',
          name: 'CreativeWorkshopAdapter');
    }
  }

  /// 将Creative Workshop插件转换为Plugin System格式
  Future<void> _convertWorkshopPluginToSystem(
    dynamic plugin,
    String category,
  ) async {
    try {
      // 创建插件清单
      final manifest = {
        'id': plugin.id as String,
        'name': plugin.name as String,
        'version': plugin.version as String,
        'description': plugin.description as String,
        'author': plugin.author as String,
        'category': category,
        'main': 'lib/main.dart',
        'permissions': <String>[],
        'dependencies': <Map<String, dynamic>>[],
        'platforms': ['android', 'ios', 'windows', 'macos', 'linux'],
      };

      // 通过Plugin System注册插件
      final response = await _coordinator?.communication.sendRequest(
        'plugin_system',
        'register_plugin',
        {
          'pluginId': plugin.id,
          'manifest': manifest,
        },
      );

      if (response?['success'] == true) {
        developer.log('插件同步到Plugin System成功: ${plugin.id}',
            name: 'CreativeWorkshopAdapter');
      } else {
        developer.log('插件同步到Plugin System失败: ${response?['error']}',
            name: 'CreativeWorkshopAdapter');
      }
    } catch (e) {
      developer.log('插件转换失败: $e', name: 'CreativeWorkshopAdapter');
    }
  }

  /// 从插件数据创建工具插件
  _AdapterToolPlugin? _createToolPluginFromData(Map<String, dynamic> data) {
    try {
      // 实现实际的工具插件创建逻辑
      // 根据Plugin System的数据结构创建Creative Workshop的工具插件
      final pluginId = data['id'] as String?;
      final pluginName = data['name'] as String?;
      final pluginVersion = data['version'] as String?;
      final pluginDescription = data['description'] as String?;

      if (pluginId == null || pluginName == null || pluginVersion == null) {
        developer.log('插件数据不完整，无法创建工具插件: $data',
            name: 'CreativeWorkshopAdapter');
        return null;
      }

      // 创建工具插件实例
      final toolPlugin = _AdapterToolPlugin(
        pluginId: pluginId,
        pluginName: pluginName,
        pluginVersion: pluginVersion,
        pluginDescription:
            pluginDescription ?? 'Tool plugin from Plugin System',
        pluginCategory: data['category'] as String? ?? 'general',
        pluginMetadata: data['metadata'] as Map<String, dynamic>? ?? {},
      );

      developer.log('创建工具插件成功: $pluginName', name: 'CreativeWorkshopAdapter');
      return toolPlugin;
    } catch (e) {
      developer.log('创建工具插件失败: $e', name: 'CreativeWorkshopAdapter');
      return null;
    }
  }

  /// 从插件数据创建游戏插件
  _AdapterGamePlugin? _createGamePluginFromData(Map<String, dynamic> data) {
    try {
      // 实现实际的游戏插件创建逻辑
      // 根据Plugin System的数据结构创建Creative Workshop的游戏插件
      final pluginId = data['id'] as String?;
      final pluginName = data['name'] as String?;
      final pluginVersion = data['version'] as String?;
      final pluginDescription = data['description'] as String?;

      if (pluginId == null || pluginName == null || pluginVersion == null) {
        developer.log('插件数据不完整，无法创建游戏插件: $data',
            name: 'CreativeWorkshopAdapter');
        return null;
      }

      // 创建游戏插件实例
      final gamePlugin = _AdapterGamePlugin(
        pluginId: pluginId,
        pluginName: pluginName,
        pluginVersion: pluginVersion,
        pluginDescription:
            pluginDescription ?? 'Game plugin from Plugin System',
        pluginCategory: data['category'] as String? ?? 'general',
        pluginMetadata: data['metadata'] as Map<String, dynamic>? ?? {},
      );

      developer.log('创建游戏插件成功: $pluginName', name: 'CreativeWorkshopAdapter');
      return gamePlugin;
    } catch (e) {
      developer.log('创建游戏插件失败: $e', name: 'CreativeWorkshopAdapter');
      return null;
    }
  }
}

/// 适应层工具插件实现
class _AdapterToolPlugin {
  final String pluginId;
  final String pluginName;
  final String pluginVersion;
  final String pluginDescription;
  final String pluginCategory;
  final Map<String, dynamic> pluginMetadata;

  _AdapterToolPlugin({
    required this.pluginId,
    required this.pluginName,
    required this.pluginVersion,
    required this.pluginDescription,
    required this.pluginCategory,
    required this.pluginMetadata,
  });

  String get id => pluginId;
  String get name => pluginName;
  String get version => pluginVersion;
  String get description => pluginDescription;
  String get author => 'Plugin System';
  String get category => pluginCategory;

  Future<void> initialize() async {
    developer.log('初始化适应层工具插件: $name', name: 'AdapterToolPlugin');
  }

  Future<void> dispose() async {
    developer.log('销毁适应层工具插件: $name', name: 'AdapterToolPlugin');
  }
}

/// 适应层游戏插件实现
class _AdapterGamePlugin {
  final String pluginId;
  final String pluginName;
  final String pluginVersion;
  final String pluginDescription;
  final String pluginCategory;
  final Map<String, dynamic> pluginMetadata;

  _AdapterGamePlugin({
    required this.pluginId,
    required this.pluginName,
    required this.pluginVersion,
    required this.pluginDescription,
    required this.pluginCategory,
    required this.pluginMetadata,
  });

  String get id => pluginId;
  String get name => pluginName;
  String get version => pluginVersion;
  String get description => pluginDescription;
  String get author => 'Plugin System';
  String get category => pluginCategory;

  Future<void> initialize() async {
    developer.log('初始化适应层游戏插件: $name', name: 'AdapterGamePlugin');
  }

  Future<void> dispose() async {
    developer.log('销毁适应层游戏插件: $name', name: 'AdapterGamePlugin');
  }
}
