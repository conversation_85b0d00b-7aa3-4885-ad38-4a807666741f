/*
---------------------------------------------------------------
File name:          plugin_installation_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        插件安装服务测试 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 插件安装服务测试实现;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:app_manager/src/services/plugin_installation_service.dart';

void main() {
  group('PluginInstallationService Tests', () {
    late PluginInstallationService service;

    setUp(() {
      service = PluginInstallationService.instance;
    });

    tearDown(() {
      // 清理测试环境
    });

    test('should be singleton', () {
      final service1 = PluginInstallationService.instance;
      final service2 = PluginInstallationService.instance;
      expect(service1, same(service2));
    });

    test('should initialize successfully', () async {
      expect(service.isInitialized, isFalse);
      
      // 注意：实际测试中需要mock Plugin System
      // await service.initialize();
      // expect(service.isInitialized, isTrue);
    });

    test('should track active installations', () {
      expect(service.activeInstallations, isEmpty);
    });

    test('should track active uninstallations', () {
      expect(service.activeUninstallations, isEmpty);
    });

    test('should handle plugin installation', () async {
      // 注意：实际测试中需要mock Plugin System
      // final result = await service.installPlugin('test_plugin');
      // expect(result, isA<PluginInstallationResult>());
    });

    test('should handle plugin uninstallation', () async {
      // 注意：实际测试中需要mock Plugin System
      // final result = await service.uninstallPlugin('test_plugin');
      // expect(result, isA<UninstallationResult>());
    });

    test('should prevent duplicate installations', () async {
      // 测试重复安装保护
      // final result1 = service.installPlugin('test_plugin');
      // final result2 = service.installPlugin('test_plugin');
      // 验证第二次安装被拒绝
    });

    test('should handle installation cancellation', () async {
      // 测试安装取消
      // final cancelled = await service.cancelInstallation('test_plugin');
      // expect(cancelled, isTrue);
    });

    test('should emit installation events', () async {
      var eventReceived = false;
      
      service.events.listen((event) {
        eventReceived = true;
      });

      // 触发安装事件
      // await service.installPlugin('test_plugin');
      
      // expect(eventReceived, isTrue);
    });
  });

  group('InstallationProgress Tests', () {
    test('should create installation progress', () {
      final progress = InstallationProgress(
        pluginId: 'test_plugin',
        status: InstallationStatus.starting,
        progress: 0.0,
        startTime: DateTime.now(),
      );

      expect(progress.pluginId, equals('test_plugin'));
      expect(progress.status, equals(InstallationStatus.starting));
      expect(progress.progress, equals(0.0));
    });

    test('should copy with new values', () {
      final original = InstallationProgress(
        pluginId: 'test_plugin',
        status: InstallationStatus.starting,
        progress: 0.0,
        startTime: DateTime.now(),
      );

      final updated = original.copyWith(
        status: InstallationStatus.downloading,
        progress: 0.5,
      );

      expect(updated.pluginId, equals(original.pluginId));
      expect(updated.status, equals(InstallationStatus.downloading));
      expect(updated.progress, equals(0.5));
      expect(updated.startTime, equals(original.startTime));
    });
  });

  group('UninstallationProgress Tests', () {
    test('should create uninstallation progress', () {
      final progress = UninstallationProgress(
        pluginId: 'test_plugin',
        status: UninstallationStatus.starting,
        progress: 0.0,
        startTime: DateTime.now(),
      );

      expect(progress.pluginId, equals('test_plugin'));
      expect(progress.status, equals(UninstallationStatus.starting));
      expect(progress.progress, equals(0.0));
    });

    test('should copy with new values', () {
      final original = UninstallationProgress(
        pluginId: 'test_plugin',
        status: UninstallationStatus.starting,
        progress: 0.0,
        startTime: DateTime.now(),
      );

      final updated = original.copyWith(
        status: UninstallationStatus.removingFiles,
        progress: 0.5,
      );

      expect(updated.pluginId, equals(original.pluginId));
      expect(updated.status, equals(UninstallationStatus.removingFiles));
      expect(updated.progress, equals(0.5));
      expect(updated.startTime, equals(original.startTime));
    });
  });

  group('PluginInstallationResult Tests', () {
    test('should create success result', () {
      final result = PluginInstallationResult.success(
        'test_plugin',
        'Installation successful',
        '/path/to/plugin',
      );

      expect(result.isSuccess, isTrue);
      expect(result.pluginId, equals('test_plugin'));
      expect(result.message, equals('Installation successful'));
      expect(result.installPath, equals('/path/to/plugin'));
      expect(result.error, isNull);
    });

    test('should create failure result', () {
      final result = PluginInstallationResult.failure(
        'test_plugin',
        'Installation failed',
        PluginInstallationError.downloadFailed,
      );

      expect(result.isSuccess, isFalse);
      expect(result.pluginId, equals('test_plugin'));
      expect(result.message, equals('Installation failed'));
      expect(result.installPath, isNull);
      expect(result.error, equals(PluginInstallationError.downloadFailed));
    });
  });

  group('UninstallationResult Tests', () {
    test('should create success result', () {
      final result = UninstallationResult.success(
        'test_plugin',
        'Uninstallation successful',
      );

      expect(result.isSuccess, isTrue);
      expect(result.pluginId, equals('test_plugin'));
      expect(result.message, equals('Uninstallation successful'));
      expect(result.error, isNull);
    });

    test('should create failure result', () {
      final result = UninstallationResult.failure(
        'test_plugin',
        'Uninstallation failed',
        UninstallationError.fileRemovalFailed,
      );

      expect(result.isSuccess, isFalse);
      expect(result.pluginId, equals('test_plugin'));
      expect(result.message, equals('Uninstallation failed'));
      expect(result.error, equals(UninstallationError.fileRemovalFailed));
    });
  });

  group('PluginInstallationEvent Tests', () {
    test('should create service initialized event', () {
      final event = PluginInstallationEvent.serviceInitialized();
      expect(event.type, equals('serviceInitialized'));
      expect(event.data, isEmpty);
    });

    test('should create installation started event', () {
      final event = PluginInstallationEvent.installationStarted('test_plugin');
      expect(event.type, equals('installationStarted'));
      expect(event.data['pluginId'], equals('test_plugin'));
    });

    test('should create progress updated event', () {
      final event = PluginInstallationEvent.progressUpdated(
        'test_plugin',
        0.5,
        InstallationStatus.downloading,
      );
      expect(event.type, equals('progressUpdated'));
      expect(event.data['pluginId'], equals('test_plugin'));
      expect(event.data['progress'], equals(0.5));
      expect(event.data['status'], equals('downloading'));
    });

    test('should create installation completed event', () {
      final event = PluginInstallationEvent.installationCompleted('test_plugin');
      expect(event.type, equals('installationCompleted'));
      expect(event.data['pluginId'], equals('test_plugin'));
    });

    test('should create installation failed event', () {
      final event = PluginInstallationEvent.installationFailed(
        'test_plugin',
        'Installation failed',
      );
      expect(event.type, equals('installationFailed'));
      expect(event.data['pluginId'], equals('test_plugin'));
      expect(event.data['message'], equals('Installation failed'));
    });

    test('should create error event', () {
      final event = PluginInstallationEvent.error('Test error');
      expect(event.type, equals('error'));
      expect(event.data['message'], equals('Test error'));
    });
  });
}
