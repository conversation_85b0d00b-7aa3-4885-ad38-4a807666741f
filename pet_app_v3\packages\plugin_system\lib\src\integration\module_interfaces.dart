/*
---------------------------------------------------------------
File name:          module_interfaces.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        插件系统模块适应层接口
---------------------------------------------------------------
Change History:
    2025-07-29: 插件系统模块适应层接口;
---------------------------------------------------------------
*/

import 'dart:async';

/// 模块状态枚举
enum ModuleStatus {
  /// 未初始化
  uninitialized,
  /// 初始化中
  initializing,
  /// 已初始化
  initialized,
  /// 启动中
  starting,
  /// 运行中
  running,
  /// 停止中
  stopping,
  /// 已停止
  stopped,
  /// 错误状态
  error,
}

/// 模块元数据
class ModuleMetadata {
  const ModuleMetadata({
    required this.id,
    required this.name,
    required this.version,
    required this.description,
    this.author = 'Pet App Team',
    this.dependencies = const [],
    this.providedServices = const [],
    this.requiredServices = const [],
  });

  /// 模块唯一标识
  final String id;
  
  /// 模块名称
  final String name;
  
  /// 模块版本
  final String version;
  
  /// 模块描述
  final String description;
  
  /// 模块作者
  final String author;
  
  /// 模块依赖列表
  final List<String> dependencies;
  
  /// 模块提供的服务列表
  final List<String> providedServices;
  
  /// 模块需要的服务列表
  final List<String> requiredServices;

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'version': version,
      'description': description,
      'author': author,
      'dependencies': dependencies,
      'providedServices': providedServices,
      'requiredServices': requiredServices,
    };
  }
}

/// 事件处理器类型定义
typedef EventHandler = Future<void> Function(Map<String, dynamic> data);

/// 请求处理器类型定义
typedef RequestHandler = Future<Map<String, dynamic>?> Function(
  Map<String, dynamic> data,
  String senderId,
);

/// 统一模块适应层接口
/// 
/// 所有模块都必须实现此接口，以便与主应用和其他模块进行标准化集成。
abstract class IModuleAdapter {
  /// 模块元数据
  ModuleMetadata get metadata;
  
  /// 模块当前状态
  ModuleStatus get status;
  
  /// 模块状态变化流
  Stream<ModuleStatus> get statusStream;
  
  /// 初始化模块
  /// 
  /// [coordinator] 模块协调器实例
  Future<void> initialize(IModuleCoordinator coordinator);
  
  /// 启动模块
  Future<void> start();
  
  /// 停止模块
  Future<void> stop();
  
  /// 销毁模块，释放资源
  Future<void> dispose();
  
  /// 处理来自其他模块的请求
  /// 
  /// [action] 请求动作
  /// [data] 请求数据
  /// [senderId] 发送者模块ID
  Future<Map<String, dynamic>?> handleRequest(
    String action,
    Map<String, dynamic> data,
    String senderId,
  );
  
  /// 注册请求处理器
  /// 
  /// [action] 动作名称
  /// [handler] 处理器函数
  void registerRequestHandler(String action, RequestHandler handler);
  
  /// 注册事件处理器
  /// 
  /// [event] 事件名称
  /// [handler] 处理器函数
  void registerEventHandler(String event, EventHandler handler);
  
  /// 获取模块健康状态
  Future<Map<String, dynamic>> getHealthStatus();
}

/// 统一模块通信接口
/// 
/// 提供模块间通信的标准化接口。
abstract class IModuleCommunication {
  /// 发送请求到其他模块
  /// 
  /// [targetModule] 目标模块ID
  /// [action] 请求动作
  /// [data] 请求数据
  /// [timeoutMs] 超时时间（毫秒）
  Future<Map<String, dynamic>?> sendRequest(
    String targetModule,
    String action,
    Map<String, dynamic> data, {
    int timeoutMs = 5000,
  });
  
  /// 发送通知到其他模块（不等待响应）
  /// 
  /// [targetModule] 目标模块ID
  /// [event] 事件名称
  /// [data] 事件数据
  Future<void> sendNotification(
    String targetModule,
    String event,
    Map<String, dynamic> data,
  );
  
  /// 广播事件到所有模块
  /// 
  /// [event] 事件名称
  /// [data] 事件数据
  /// [excludeModules] 排除的模块ID列表
  Future<void> broadcastEvent(
    String event,
    Map<String, dynamic> data, {
    List<String> excludeModules = const [],
  });
  
  /// 订阅事件
  /// 
  /// [event] 事件名称
  /// [handler] 事件处理器
  void subscribeToEvent(String event, EventHandler handler);
  
  /// 取消订阅事件
  /// 
  /// [event] 事件名称
  /// [handler] 事件处理器（可选，如果不提供则取消所有处理器）
  void unsubscribeFromEvent(String event, [EventHandler? handler]);
}

/// 模块协调器接口
/// 
/// 主应用层的统一模块协调器接口。
abstract class IModuleCoordinator {
  /// 注册模块
  /// 
  /// [adapter] 模块适应层实例
  Future<void> registerModule(IModuleAdapter adapter);
  
  /// 注销模块
  /// 
  /// [moduleId] 模块ID
  Future<void> unregisterModule(String moduleId);
  
  /// 获取模块适应层实例
  /// 
  /// [moduleId] 模块ID
  IModuleAdapter? getModule(String moduleId);
  
  /// 获取所有已注册的模块
  List<IModuleAdapter> getAllModules();
  
  /// 获取模块通信接口
  IModuleCommunication get communication;
  
  /// 启动所有模块
  Future<void> startAllModules();
  
  /// 停止所有模块
  Future<void> stopAllModules();
  
  /// 获取系统状态
  Future<Map<String, dynamic>> getSystemStatus();
}

/// 模块服务接口
/// 
/// 定义模块可以提供的服务接口。
abstract class IModuleService {
  /// 服务名称
  String get serviceName;
  
  /// 服务版本
  String get serviceVersion;
  
  /// 服务是否可用
  bool get isAvailable;
  
  /// 初始化服务
  Future<void> initialize();
  
  /// 销毁服务
  Future<void> dispose();
  
  /// 调用服务方法
  /// 
  /// [method] 方法名称
  /// [parameters] 方法参数
  Future<dynamic> invoke(String method, Map<String, dynamic> parameters);
}

/// 模块配置接口
/// 
/// 定义模块配置的标准接口。
abstract class IModuleConfig {
  /// 获取配置值
  /// 
  /// [key] 配置键
  /// [defaultValue] 默认值
  T? get<T>(String key, [T? defaultValue]);
  
  /// 设置配置值
  /// 
  /// [key] 配置键
  /// [value] 配置值
  Future<void> set<T>(String key, T value);
  
  /// 删除配置
  /// 
  /// [key] 配置键
  Future<void> remove(String key);
  
  /// 获取所有配置
  Map<String, dynamic> getAll();
  
  /// 保存配置到持久化存储
  Future<void> save();
  
  /// 从持久化存储加载配置
  Future<void> load();
}
