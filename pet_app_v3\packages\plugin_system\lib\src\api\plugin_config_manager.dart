/*
---------------------------------------------------------------
File name:          plugin_config_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件配置管理模块
---------------------------------------------------------------
Change History:
    2025-07-27: 从plugin_rest_api.dart重构拆分出插件配置管理功能;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:path/path.dart' as path;
import 'package:plugin_system/src/api/plugin_api_interface.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

/// 插件配置管理器
///
/// 负责插件配置的获取、更新、验证等功能
class PluginConfigManager {
  PluginConfigManager({
    required this.registry,
  });

  /// 插件注册表
  final PluginRegistry registry;

  /// 获取插件配置
  Future<ApiResponse<Map<String, dynamic>>> getPluginConfig(
    String pluginId,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '插件不存在',
          statusCode: 404,
        );
      }

      // 3. 加载插件配置
      final config = await _loadPluginConfig(plugin);

      return ApiResponse.success(
        data: <String, dynamic>{
          'pluginId': pluginId,
          'config': config['config'],
          'schema': config['schema'],
          'lastModified': config['lastModified'],
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '获取配置失败: $e',
        statusCode: 500,
      );
    }
  }

  /// 更新插件配置
  Future<ApiResponse<Map<String, dynamic>>> updatePluginConfig(
    String pluginId,
    Map<String, dynamic> newConfig,
  ) async {
    try {
      // 1. 验证插件ID
      if (pluginId.isEmpty) {
        return ApiResponse.error(
          message: '插件ID不能为空',
          statusCode: 400,
        );
      }

      // 2. 检查插件是否存在
      final plugin = registry.get(pluginId);
      if (plugin == null) {
        return ApiResponse.error(
          message: '插件不存在',
          statusCode: 404,
        );
      }

      // 3. 验证配置
      final validationResult = await _validatePluginConfig(
        plugin,
        newConfig,
      );

      if (!(validationResult['valid'] as bool)) {
        return ApiResponse.error(
          message: validationResult['error'] as String,
          statusCode: 400,
        );
      }

      // 4. 更新配置
      final updateResult = await _updatePluginConfig(plugin, newConfig);

      if (!(updateResult['success'] as bool)) {
        return ApiResponse.error(
          message: updateResult['error'] as String,
          statusCode: 500,
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'pluginId': pluginId,
          'updated': true,
          'updatedAt': DateTime.now().toIso8601String(),
          'appliedConfig': updateResult['config'],
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '更新配置失败: $e',
        statusCode: 500,
      );
    }
  }

  /// 获取系统配置
  Future<ApiResponse<Map<String, dynamic>>> getSystemConfig() async {
    try {
      final config = await _getSystemConfig();

      return ApiResponse.success(
        data: <String, dynamic>{
          'config': config['config'],
          'schema': config['schema'],
          'version': '1.0.0',
          'lastModified': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '获取系统配置失败: $e',
        statusCode: 500,
      );
    }
  }

  /// 更新系统配置
  Future<ApiResponse<Map<String, dynamic>>> updateSystemConfig(
    Map<String, dynamic> newConfig,
  ) async {
    try {
      // 1. 验证配置
      final validationResult = await _validateSystemConfig(newConfig);

      if (!(validationResult['valid'] as bool)) {
        return ApiResponse.error(
          message: validationResult['error'] as String,
          statusCode: 400,
        );
      }

      // 2. 更新配置
      final updateResult = await _updateSystemConfig(newConfig);

      if (!(updateResult['success'] as bool)) {
        return ApiResponse.error(
          message: updateResult['error'] as String,
          statusCode: 500,
        );
      }

      return ApiResponse.success(
        data: <String, dynamic>{
          'updated': true,
          'updatedAt': DateTime.now().toIso8601String(),
          'appliedConfig': updateResult['config'],
        },
      );
    } catch (e) {
      return ApiResponse.error(
        message: '更新系统配置失败: $e',
        statusCode: 500,
      );
    }
  }

  /// 加载插件配置
  Future<Map<String, dynamic>> _loadPluginConfig(Plugin plugin) async {
    try {
      // 从插件manifest和配置文件读取真实配置
      final config = await _loadPluginConfigFromSources(plugin);

      // 合并默认配置
      final mergedConfig = _mergeWithDefaultConfig(config, plugin);

      // 配置模式定义
      final schema = <String, dynamic>{
        'type': 'object',
        'properties': <String, dynamic>{
          'enabled': <String, String>{'type': 'boolean'},
          'autoStart': <String, String>{'type': 'boolean'},
          'logLevel': <String, dynamic>{
            'type': 'string',
            'enum': <String>['debug', 'info', 'warn', 'error'],
          },
          'maxMemory': <String, String>{'type': 'string'},
          'timeout': <String, String>{'type': 'integer'},
        },
        'required': <String>['enabled', 'logLevel'],
      };

      return <String, dynamic>{
        'config': mergedConfig,
        'schema': schema,
        'lastModified': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('加载插件配置失败: $e');
    }
  }

  /// 从配置源加载配置
  Future<Map<String, dynamic>> _loadPluginConfigFromSources(
    Plugin plugin,
  ) async {
    try {
      final Map<String, dynamic> config = <String, dynamic>{};

      // 1. 从插件manifest读取默认配置
      final manifestConfig = await _loadConfigFromManifest(plugin);
      config.addAll(manifestConfig);

      // 2. 从系统配置文件读取
      final systemConfig = await _loadConfigFromSystemFile(plugin);
      config.addAll(systemConfig);

      // 3. 从用户配置文件读取
      final userConfig = await _loadConfigFromUserFile(plugin);
      config.addAll(userConfig);

      // 4. 从环境变量读取
      final envConfig = await _loadConfigFromEnvironment(plugin);
      config.addAll(envConfig);

      return config;
    } catch (e) {
      // 返回空配置，将使用默认值
      return <String, dynamic>{};
    }
  }

  /// 合并默认配置
  Map<String, dynamic> _mergeWithDefaultConfig(
    Map<String, dynamic> config,
    Plugin plugin,
  ) {
    // 默认配置
    final defaultConfig = <String, dynamic>{
      'enabled': false,
      'autoStart': true,
      'logLevel': 'info',
      'maxMemory': '128MB',
      'timeout': 30,
      'features': <String, bool>{
        'notifications': true,
        'background': false,
        'autoUpdate': true,
      },
      'customSettings': <String, dynamic>{
        'theme': 'default',
        'language': 'zh_CN',
      },
    };

    // 合并配置（用户配置覆盖默认配置）
    final merged = Map<String, dynamic>.from(defaultConfig);
    merged.addAll(config);

    return merged;
  }

  /// 验证插件配置
  Future<Map<String, dynamic>> _validatePluginConfig(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      // 基本验证
      if (config.isEmpty) {
        return <String, dynamic>{
          'valid': false,
          'error': '配置不能为空',
        };
      }

      // 验证必需的配置项
      final requiredKeys = <String>['enabled'];
      for (final key in requiredKeys) {
        if (!config.containsKey(key)) {
          return <String, dynamic>{
            'valid': false,
            'error': '缺少必需的配置项: $key',
          };
        }
      }

      // 验证配置值类型
      if (config.containsKey('enabled') && config['enabled'] is! bool) {
        return <String, dynamic>{
          'valid': false,
          'error': 'enabled 必须是布尔值',
        };
      }

      if (config.containsKey('logLevel') && config['logLevel'] is! String) {
        return <String, dynamic>{
          'valid': false,
          'error': 'logLevel 必须是字符串',
        };
      }

      return <String, dynamic>{
        'valid': true,
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '配置验证失败: $e',
      };
    }
  }

  /// 更新插件配置
  Future<Map<String, dynamic>> _updatePluginConfig(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 验证配置
      final validationResult = await _validatePluginConfig(plugin, config);
      if (!(validationResult['valid'] as bool)) {
        return <String, dynamic>{
          'success': false,
          'error': validationResult['error'],
        };
      }

      // 2. 保存到用户配置文件
      final saved = await _saveConfigToUserFile(plugin, config);
      if (!saved) {
        return <String, dynamic>{
          'success': false,
          'error': '保存配置文件失败',
        };
      }

      // 3. 通知插件配置变更
      await _notifyPluginConfigChanged(plugin, config);

      return <String, dynamic>{
        'success': true,
        'config': config,
        'updatedAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '配置更新失败: $e',
      };
    }
  }

  /// 获取系统配置
  Future<Map<String, dynamic>> _getSystemConfig() async {
    try {
      await Future<void>.delayed(const Duration(milliseconds: 100));

      return <String, dynamic>{
        'config': <String, dynamic>{
          'maxPlugins': 100,
          'autoStart': true,
          'logLevel': 'info',
          'cacheSize': '128MB',
          'timeout': 30,
          'security': <String, dynamic>{
            'enableSandbox': true,
            'allowUnsignedPlugins': false,
            'maxPermissions': 10,
          },
          'performance': <String, dynamic>{
            'maxConcurrentPlugins': 20,
            'memoryLimit': '512MB',
            'cpuThreshold': 80,
          },
        },
        'schema': <String, dynamic>{
          'type': 'object',
          'properties': <String, dynamic>{
            'maxPlugins': <String, String>{'type': 'integer'},
            'autoStart': <String, String>{'type': 'boolean'},
            'logLevel': <String, dynamic>{
              'type': 'string',
              'enum': <String>['debug', 'info', 'warn', 'error'],
            },
          },
        },
      };
    } catch (e) {
      throw Exception('获取系统配置失败: $e');
    }
  }

  /// 验证系统配置
  Future<Map<String, dynamic>> _validateSystemConfig(
    Map<String, dynamic> config,
  ) async {
    try {
      // 验证必需的配置项
      final requiredKeys = <String>['maxPlugins', 'autoStart', 'logLevel'];
      for (final key in requiredKeys) {
        if (!config.containsKey(key)) {
          return <String, dynamic>{
            'valid': false,
            'error': '缺少必需的系统配置项: $key',
          };
        }
      }

      // 验证配置值类型和范围
      if (config.containsKey('maxPlugins')) {
        final maxPlugins = config['maxPlugins'];
        if (maxPlugins is! int || maxPlugins <= 0 || maxPlugins > 1000) {
          return <String, dynamic>{
            'valid': false,
            'error': 'maxPlugins 必须是1-1000之间的整数',
          };
        }
      }

      if (config.containsKey('autoStart') && config['autoStart'] is! bool) {
        return <String, dynamic>{
          'valid': false,
          'error': 'autoStart 必须是布尔值',
        };
      }

      if (config.containsKey('logLevel')) {
        final logLevel = config['logLevel'];
        if (logLevel is! String ||
            !<String>['debug', 'info', 'warn', 'error'].contains(logLevel)) {
          return <String, dynamic>{
            'valid': false,
            'error': 'logLevel 必须是 debug、info、warn 或 error 之一',
          };
        }
      }

      return <String, dynamic>{
        'valid': true,
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '系统配置验证失败: $e',
      };
    }
  }

  /// 更新系统配置
  Future<Map<String, dynamic>> _updateSystemConfig(
    Map<String, dynamic> config,
  ) async {
    try {
      // 1. 验证配置
      final validationResult = await _validateSystemConfig(config);
      if (!(validationResult['valid'] as bool)) {
        return <String, dynamic>{
          'success': false,
          'error': validationResult['error'],
        };
      }

      // 2. 保存到系统配置文件
      final saved = await _saveSystemConfigToFile(config);
      if (!saved) {
        return <String, dynamic>{
          'success': false,
          'error': '保存系统配置文件失败',
        };
      }

      // 3. 通知系统配置变更
      await _notifySystemConfigChanged(config);

      return <String, dynamic>{
        'success': true,
        'config': config,
        'updatedAt': DateTime.now().toIso8601String(),
      };
    } on Exception catch (e) {
      return <String, dynamic>{
        'success': false,
        'error': '系统配置更新失败: $e',
      };
    }
  }

  /// 从插件manifest读取配置
  Future<Map<String, dynamic>> _loadConfigFromManifest(Plugin plugin) async {
    try {
      // 从插件的manifest中读取默认配置
      final manifest = plugin.manifest;

      return <String, dynamic>{
        'enabled': false, // 默认禁用
        'autoStart': false,
        'logLevel': 'info',
        'permissions': manifest.permissions,
        'dependencies': manifest.dependencies.map((PluginManifestDependency d) => d.id).toList(),
        'platforms': manifest.platforms,
        'category': manifest.category,
        'version': manifest.version,
        'author': manifest.author,
        'config': manifest.config?.toMap() ?? <String, dynamic>{},
      };
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  /// 从系统配置文件读取配置
  Future<Map<String, dynamic>> _loadConfigFromSystemFile(Plugin plugin) async {
    try {
      // 系统配置文件路径
      final systemConfigPath = path.join(
        Directory.current.path,
        'config',
        'plugins',
        'system',
        '${plugin.id}.json',
      );

      final configFile = File(systemConfigPath);
      if (!await configFile.exists()) {
        return <String, dynamic>{};
      }

      final configContent = await configFile.readAsString();
      final config = jsonDecode(configContent) as Map<String, dynamic>;

      return config;
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  /// 从用户配置文件读取配置
  Future<Map<String, dynamic>> _loadConfigFromUserFile(Plugin plugin) async {
    try {
      // 用户配置文件路径
      final userConfigPath = path.join(
        Directory.current.path,
        'config',
        'plugins',
        'user',
        '${plugin.id}.json',
      );

      final configFile = File(userConfigPath);
      if (!await configFile.exists()) {
        return <String, dynamic>{};
      }

      final configContent = await configFile.readAsString();
      final config = jsonDecode(configContent) as Map<String, dynamic>;

      return config;
    } catch (e) {
      return <String, dynamic>{};
    }
  }

  /// 从环境变量读取配置
  Future<Map<String, dynamic>> _loadConfigFromEnvironment(Plugin plugin) async {
    try {
      final config = <String, dynamic>{};
      final envPrefix = 'PLUGIN_${plugin.id.toUpperCase()}_';

      // 读取常见的环境变量配置
      final envVars = Platform.environment;

      for (final entry in envVars.entries) {
        if (entry.key.startsWith(envPrefix)) {
          final configKey = entry.key
              .substring(envPrefix.length)
              .toLowerCase()
              .replaceAll('_', '');

          // 尝试解析为不同类型
          final value = entry.value;
          if (value.toLowerCase() == 'true') {
            config[configKey] = true;
          } else if (value.toLowerCase() == 'false') {
            config[configKey] = false;
          } else if (int.tryParse(value) != null) {
            config[configKey] = int.parse(value);
          } else if (double.tryParse(value) != null) {
            config[configKey] = double.parse(value);
          } else {
            config[configKey] = value;
          }
        }
      }

      return config;
    } on Exception {
      return <String, dynamic>{};
    }
  }

  /// 保存配置到用户文件
  Future<bool> _saveConfigToUserFile(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      // 确保用户配置目录存在
      final String userConfigDir = path.join(
        Directory.current.path,
        'config',
        'plugins',
        'user',
      );

      final Directory configDir = Directory(userConfigDir);
      if (!configDir.existsSync()) {
        await configDir.create(recursive: true);
      }

      // 保存配置文件
      final String userConfigPath =
          path.join(userConfigDir, '${plugin.id}.json');
      final File configFile = File(userConfigPath);

      final String configJson = jsonEncode(config);
      await configFile.writeAsString(configJson);

      return true;
    } on Exception {
      return false;
    }
  }

  /// 保存系统配置到文件
  Future<bool> _saveSystemConfigToFile(Map<String, dynamic> config) async {
    try {
      // 确保系统配置目录存在
      final systemConfigDir = path.join(
        Directory.current.path,
        'config',
        'system',
      );

      final configDir = Directory(systemConfigDir);
      if (!configDir.existsSync()) {
        await configDir.create(recursive: true);
      }

      // 保存系统配置文件
      final systemConfigPath = path.join(systemConfigDir, 'system.json');
      final configFile = File(systemConfigPath);

      final configJson = jsonEncode(config);
      await configFile.writeAsString(configJson);

      return true;
    } on Exception {
      return false;
    }
  }

  /// 通知系统配置变更
  Future<void> _notifySystemConfigChanged(Map<String, dynamic> config) async {
    try {
      // 通知所有已启用的插件系统配置变更
      final allPlugins = registry.getAllPlugins();

      for (final plugin in allPlugins) {
        if (plugin.isEnabled) {
          try {
            await plugin.handleMessage('system_config_changed', config);
          } on Exception {
            // 忽略单个插件的通知失败
          }
        }
      }
    } on Exception {
      // 忽略通知失败，不影响配置更新
    }
  }

  /// 通知插件配置变更
  Future<void> _notifyPluginConfigChanged(
    Plugin plugin,
    Map<String, dynamic> config,
  ) async {
    try {
      // 如果插件支持配置变更通知，则调用相应方法
      if (plugin.isEnabled) {
        await plugin.handleMessage('config_changed', config);
      }
    } on Exception {
      // 忽略通知失败，不影响配置更新
    }
  }
}
