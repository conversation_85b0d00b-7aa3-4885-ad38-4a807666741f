/*
---------------------------------------------------------------
File name:          cross_module_navigation_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        跨模块导航功能测试 - 策略A重构阶段5
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 跨模块导航功能测试实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/ui/developer/developer_platform_page.dart';

void main() {
  group('Cross Module Navigation Tests', () {
    testWidgets('should display navigation buttons in app bar', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证应用商店按钮存在
      expect(find.byIcon(Icons.store), findsOneWidget);
      expect(find.byTooltip('访问应用商店'), findsOneWidget);

      // 验证应用管理器按钮存在
      expect(find.byIcon(Icons.apps), findsOneWidget);
      expect(find.byTooltip('应用管理器'), findsOneWidget);

      // 验证帮助按钮存在
      expect(find.byIcon(Icons.help_outline), findsOneWidget);
      expect(find.byTooltip('开发者帮助'), findsOneWidget);
    });

    testWidgets('should show snackbar when navigating to app store', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击应用商店按钮
      await tester.tap(find.byIcon(Icons.store));
      await tester.pump();

      // 验证显示了SnackBar
      expect(find.byType(SnackBar), findsOneWidget);
      expect(find.text('正在跳转到应用商店...'), findsOneWidget);
    });

    testWidgets('should show snackbar when navigating to app manager', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击应用管理器按钮
      await tester.tap(find.byIcon(Icons.apps));
      await tester.pump();

      // 验证显示了SnackBar
      expect(find.byType(SnackBar), findsOneWidget);
      expect(find.text('正在跳转到应用管理器...'), findsOneWidget);
    });

    testWidgets('should show app store info dialog when manual open clicked', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击应用商店按钮
      await tester.tap(find.byIcon(Icons.store));
      await tester.pump();

      // 点击手动打开按钮
      await tester.tap(find.text('手动打开'));
      await tester.pump();

      // 验证显示了应用商店信息对话框
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('应用商店'), findsOneWidget);
      expect(find.text('应用商店提供丰富的插件资源'), findsOneWidget);
    });

    testWidgets('should show app manager info dialog when manual open clicked', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击应用管理器按钮
      await tester.tap(find.byIcon(Icons.apps));
      await tester.pump();

      // 点击手动打开按钮
      await tester.tap(find.text('手动打开'));
      await tester.pump();

      // 验证显示了应用管理器信息对话框
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('应用管理器'), findsOneWidget);
      expect(find.text('应用管理器是系统的核心管理工具'), findsOneWidget);
    });

    testWidgets('should show developer help dialog', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击帮助按钮
      await tester.tap(find.byIcon(Icons.help_outline));
      await tester.pump();

      // 验证显示了帮助对话框
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('开发者帮助'), findsOneWidget);
      expect(find.text('欢迎使用 Creative Workshop 开发者平台！'), findsOneWidget);
    });

    testWidgets('should close dialogs when close button clicked', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 打开帮助对话框
      await tester.tap(find.byIcon(Icons.help_outline));
      await tester.pump();

      // 验证对话框存在
      expect(find.byType(AlertDialog), findsOneWidget);

      // 点击关闭按钮
      await tester.tap(find.text('关闭'));
      await tester.pump();

      // 验证对话框已关闭
      expect(find.byType(AlertDialog), findsNothing);
    });

    testWidgets('should display developer stats panel', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证统计面板存在
      expect(find.text('项目总数'), findsOneWidget);
      expect(find.text('活跃项目'), findsOneWidget);
      expect(find.text('已发布'), findsOneWidget);
      expect(find.text('下载量'), findsOneWidget);
    });

    testWidgets('should display tab bar with correct tabs', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证标签页存在
      expect(find.text('项目管理'), findsOneWidget);
      expect(find.text('插件开发'), findsOneWidget);
      expect(find.text('发布管理'), findsOneWidget);
      expect(find.text('Ming CLI'), findsOneWidget);
    });

    testWidgets('should display floating action button', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证浮动操作按钮存在
      expect(find.byType(FloatingActionButton), findsOneWidget);
      expect(find.text('新建项目'), findsOneWidget);
    });

    testWidgets('should show new project snackbar when FAB clicked', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击新建项目按钮
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pump();

      // 验证显示了SnackBar
      expect(find.byType(SnackBar), findsOneWidget);
      expect(find.text('新建项目功能即将推出...'), findsOneWidget);
    });
  });

  group('Developer Platform Page Widget Tests', () {
    testWidgets('should build without errors', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证页面构建成功
      expect(find.byType(DeveloperPlatformPage), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should have correct app bar title', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 验证应用栏标题
      expect(find.text('开发者平台'), findsOneWidget);
    });

    testWidgets('should switch between tabs', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: DeveloperPlatformPage(),
        ),
      );

      // 点击插件开发标签
      await tester.tap(find.text('插件开发'));
      await tester.pump();

      // 验证标签切换成功（这里只是验证没有错误）
      expect(find.byType(DeveloperPlatformPage), findsOneWidget);

      // 点击发布管理标签
      await tester.tap(find.text('发布管理'));
      await tester.pump();

      // 验证标签切换成功
      expect(find.byType(DeveloperPlatformPage), findsOneWidget);

      // 点击Ming CLI标签
      await tester.tap(find.text('Ming CLI'));
      await tester.pump();

      // 验证标签切换成功
      expect(find.byType(DeveloperPlatformPage), findsOneWidget);
    });
  });
}
