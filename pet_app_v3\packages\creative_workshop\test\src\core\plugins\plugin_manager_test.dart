/*
---------------------------------------------------------------
File name:          plugin_manager_test.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-07-22
Dart Version:       3.2+
Description:        插件管理器测试
---------------------------------------------------------------
Change History:
    2025-07-22: Phase ******* - 插件管理器测试实现;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/core/plugins/plugin_manager.dart';
// import '../../../helpers/mock_plugin_store_manager.dart';

/// 设置测试环境
Future<void> _setupTestEnvironment() async {
  try {
    final pluginManager = PluginManager.instance;
    await pluginManager.initialize();
    // 强制加载默认插件用于测试
    await pluginManager.loadDefaultPluginsForTesting();
  } catch (e) {
    print('设置测试环境时出错: $e');
  }
}

void main() {
  group('PluginManager Tests', () {
    late PluginManager pluginManager;
    var testCounter = 0;

    setUp(() async {
      pluginManager = PluginManager.instance;
      testCounter++;

      // 设置测试环境
      await _setupTestEnvironment();
    });

    tearDown(() async {
      // 清理测试插件
      final testPluginIds = <String>[
        'test_plugin_$testCounter',
        'test_plugin_enable_$testCounter',
        'test_plugin_disable_$testCounter',
        'test_plugin_update_$testCounter',
        'test_plugin',
        'test_plugin_enable_1',
        'test_plugin_for_uninstall',
      ];

      for (final pluginId in testPluginIds) {
        try {
          await pluginManager.uninstallPlugin(pluginId);
        } on Exception {
          // 忽略未安装的插件错误
        }
      }
    });

    group('初始化测试', () {
      test('应该能够获取单例实例', () {
        final instance1 = PluginManager.instance;
        final instance2 = PluginManager.instance;
        expect(instance1, same(instance2));
      });

      test('应该能够初始化插件管理器', () async {
        await pluginManager.initialize();
        expect(pluginManager.installedPlugins, isNotEmpty);
      });
    });

    group('插件状态管理测试', () {
      test('应该能够获取已安装插件列表', () async {
        await pluginManager.initialize();
        final plugins = pluginManager.installedPlugins;
        expect(plugins, isA<List<PluginInstallInfo>>());
        expect(plugins.length, greaterThan(0));
      });

      test('应该能够获取已启用插件列表', () async {
        await pluginManager.initialize();
        final enabledPlugins = pluginManager.enabledPlugins;
        expect(enabledPlugins, isA<List<PluginInstallInfo>>());
        expect(enabledPlugins.every((PluginInstallInfo p) => p.state == PluginState.enabled),
            isTrue,);
      });

      test('应该能够获取需要更新的插件列表', () async {
        await pluginManager.initialize();
        final updatablePlugins = pluginManager.updatablePlugins;
        expect(updatablePlugins, isA<List<PluginInstallInfo>>());
        expect(
            updatablePlugins
                .every((PluginInstallInfo p) => p.state == PluginState.updateAvailable),
            isTrue,);
      });
    });

    group('插件操作测试', () {
      test('应该能够启用已安装的插件', () async {
        // 使用默认的已安装插件 'shape_designer' (状态为 disabled)
        const pluginId = 'shape_designer';

        // 调试：检查所有已安装的插件
        final allPlugins = pluginManager.installedPlugins;
        print('已安装插件数量: ${allPlugins.length}');
        for (final plugin in allPlugins) {
          print('插件: ${plugin.id} - ${plugin.name} - ${plugin.state}');
        }

        // 验证插件存在且状态为 disabled
        final installedPlugin = pluginManager.getPluginInfo(pluginId);
        expect(installedPlugin, isNotNull);
        expect(installedPlugin?.state, PluginState.disabled);

        // 启用插件
        final result = await pluginManager.enablePlugin(pluginId);
        expect(result.success, isTrue);

        // 验证插件状态已更改为 enabled
        final enabledPlugin = pluginManager.getPluginInfo(pluginId);
        expect(enabledPlugin?.state, PluginState.enabled);
      });

      test('应该能够禁用已启用的插件', () async {
        await pluginManager.initialize();

        // 使用默认的已启用插件 'advanced_brush' (状态为 enabled)
        const pluginId = 'advanced_brush';

        // 验证插件存在且状态为 enabled
        final enabledPlugin = pluginManager.getPluginInfo(pluginId);
        expect(enabledPlugin, isNotNull);
        expect(enabledPlugin?.state, PluginState.enabled);

        final result = await pluginManager.disablePlugin(pluginId);
        expect(result.success, isTrue);

        final updatedPlugin = pluginManager.getPluginInfo(pluginId);
        expect(updatedPlugin?.state, PluginState.disabled);
      });

      test('应该能够更新有更新的插件', () async {
        await pluginManager.initialize();

        // 跳过更新测试 - 需要真实的插件商店连接和更新检查
        // TODO: 实现模拟插件商店后重新启用此测试
      }, skip: '需要网络连接和真实的插件商店',);

      test('应该能够安装新插件', () async {
        await pluginManager.initialize();

        // 跳过网络相关测试 - 需要真实的插件商店连接
        // TODO: 实现模拟插件商店后重新启用此测试
      }, skip: '需要网络连接和真实的插件商店',);

      test('应该能够卸载已安装的插件', () async {
        await pluginManager.initialize();

        // 跳过网络相关测试 - 需要真实的插件商店连接
        // TODO: 实现模拟插件商店后重新启用此测试
      }, skip: '需要网络连接和真实的插件商店',);
    });

    group('错误处理测试', () {
      test('启用不存在的插件应该返回错误', () async {
        await pluginManager.initialize();

        final result = await pluginManager.enablePlugin('non_existent_plugin');
        expect(result.success, isFalse);
        expect(result.error, contains('插件未安装'));
      });

      test('禁用不存在的插件应该返回错误', () async {
        await pluginManager.initialize();

        final result = await pluginManager.disablePlugin('non_existent_plugin');
        expect(result.success, isFalse);
        expect(result.error, contains('插件未安装'));
      });

      test('更新不存在的插件应该返回错误', () async {
        await pluginManager.initialize();

        final result = await pluginManager.updatePlugin('non_existent_plugin');
        expect(result.success, isFalse);
        expect(result.error, contains('插件未安装'));
      });

      test('重复安装插件应该返回错误', () async {
        await pluginManager.initialize();

        // 尝试安装已存在的插件
        final existingPlugin = pluginManager.installedPlugins.first;
        final result = await pluginManager.installPlugin(existingPlugin.id);
        expect(result.success, isFalse);
        expect(result.error, contains('插件已安装'));
      });
    });

    group('插件信息查询测试', () {
      test('应该能够检查插件是否已安装', () async {
        await pluginManager.initialize();

        final existingPlugin = pluginManager.installedPlugins.first;
        expect(pluginManager.isPluginInstalled(existingPlugin.id), isTrue);
        expect(pluginManager.isPluginInstalled('non_existent_plugin'), isFalse);
      });

      test('应该能够检查插件是否已启用', () async {
        await pluginManager.initialize();

        // 使用默认插件进行测试
        expect(pluginManager.isPluginEnabled('advanced_brush'), isTrue);
        expect(pluginManager.isPluginEnabled('shape_designer'), isFalse);
      });

      test('应该能够获取插件统计信息', () async {
        await pluginManager.initialize();

        final stats = pluginManager.getPluginStats();
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats['totalInstalled'], isA<int>());
        expect(stats['totalEnabled'], isA<int>());
        expect(stats['totalSize'], isA<int>());
        expect(stats['needsUpdate'], isA<int>());
      });
    });

    group('插件权限测试', () {
      test('插件应该有正确的权限信息', () async {
        await pluginManager.initialize();

        // 使用 advanced_brush 插件测试权限
        const pluginId = 'advanced_brush';
        final plugin = pluginManager.getPluginInfo(pluginId);
        expect(plugin, isNotNull);
        expect(plugin!.permissions, isA<List<PluginPermission>>());
        expect(plugin.permissions.length, greaterThan(0));
      });
    });

    group('插件依赖测试', () {
      test('插件应该有正确的依赖信息', () async {
        await pluginManager.initialize();

        // 使用 shape_designer 插件测试依赖（它有 math_utils 依赖）
        const pluginId = 'shape_designer';
        final plugin = pluginManager.getPluginInfo(pluginId);
        expect(plugin, isNotNull);
        expect(plugin!.dependencies, isA<List<PluginDependency>>());
        expect(plugin.dependencies.length, greaterThan(0));

        final dependency = plugin.dependencies.first;
        expect(dependency.pluginId, isA<String>());
        expect(dependency.version, isA<String>());
        expect(dependency.isRequired, isA<bool>());
      });
    });
  });
}
