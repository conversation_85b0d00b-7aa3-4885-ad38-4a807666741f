/*
---------------------------------------------------------------
File name:          developer_platform_page.dart
Author:             lgnorant-lu
Date created:       2025-07-22
Last modified:      2025-07-22
Dart Version:       3.2+
Description:        开发者平台主界面
---------------------------------------------------------------
Change History:
    2025-07-22: Phase ******* - 开发者平台功能实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:app_manager/app_manager.dart';
import 'package:creative_workshop/src/ui/developer/project_manager_tab.dart';
import 'package:creative_workshop/src/ui/developer/plugin_development_tab.dart';
import 'package:creative_workshop/src/ui/developer/publish_manager_tab.dart';
import 'package:creative_workshop/src/ui/developer/ming_cli_integration_tab.dart';

/// 开发者平台主界面
class DeveloperPlatformPage extends StatefulWidget {
  const DeveloperPlatformPage({
    super.key,
    this.initialTabIndex = 0,
  });

  /// 初始标签页索引
  final int initialTabIndex;

  @override
  State<DeveloperPlatformPage> createState() => _DeveloperPlatformPageState();

  @override
  void debugFillProperties(DiagnosticPropertiesBuilder properties) {
    super.debugFillProperties(properties);
    properties.add(IntProperty('initialTabIndex', initialTabIndex));
  }
}

class _DeveloperPlatformPageState extends State<DeveloperPlatformPage>
    with TickerProviderStateMixin {
  late final TabController _tabController;

  // 开发者统计数据
  int _totalProjects = 0;
  int _publishedPlugins = 0;
  int _totalDownloads = 0;
  double _averageRating = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(
      length: 4,
      vsync: this,
      initialIndex: widget.initialTabIndex,
    );

    // 加载开发者统计数据
    _loadDeveloperStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载开发者统计数据
  Future<void> _loadDeveloperStats() async {
    // TODO: Phase ******* - 从真实数据源加载统计信息
    // 当前使用模拟数据
    await Future<void>.delayed(const Duration(milliseconds: 300));

    setState(() {
      _totalProjects = 5;
      _publishedPlugins = 3;
      _totalDownloads = 1250;
      _averageRating = 4.6;
    });
  }

  @override
  Widget build(BuildContext context) => Scaffold(
      appBar: AppBar(
        title: const Text('开发者平台'),
        actions: <Widget>[
          IconButton(
            icon: const Icon(Icons.store),
            onPressed: _navigateToAppStore,
            tooltip: '访问应用商店',
          ),
          IconButton(
            icon: const Icon(Icons.apps),
            onPressed: _navigateToAppManager,
            tooltip: '应用管理器',
          ),
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showDeveloperHelp,
            tooltip: '开发者帮助',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const <Widget>[
            Tab(icon: Icon(Icons.folder), text: '项目管理'),
            Tab(icon: Icon(Icons.code), text: '插件开发'),
            Tab(icon: Icon(Icons.publish), text: '发布管理'),
            Tab(icon: Icon(Icons.terminal), text: 'Ming CLI'),
          ],
        ),
      ),
      body: Column(
        children: <Widget>[
          // 开发者统计面板
          _buildDeveloperStatsPanel(),

          // 主要内容区域
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const <Widget>[
                ProjectManagerTab(),
                PluginDevelopmentTab(),
                PublishManagerTab(),
                MingCliIntegrationTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewProject,
        icon: const Icon(Icons.add),
        label: const Text('新建项目'),
      ),
    );

  /// 构建开发者统计面板
  Widget _buildDeveloperStatsPanel() => Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Icon(
                Icons.analytics,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                '开发者统计',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: <Widget>[
              Expanded(
                child: _buildStatCard(
                  '项目数量',
                  _totalProjects.toString(),
                  Icons.folder,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '已发布插件',
                  _publishedPlugins.toString(),
                  Icons.extension,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '总下载量',
                  _formatNumber(_totalDownloads),
                  Icons.download,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  '平均评分',
                  _averageRating.toStringAsFixed(1),
                  Icons.star,
                  Colors.amber,
                ),
              ),
            ],
          ),
        ],
      ),
    );

  /// 构建统计卡片
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) => Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Column(
        children: <Widget>[
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );

  /// 格式化数字
  String _formatNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      return '${(number / 1000).toStringAsFixed(1)}K';
    } else {
      return '${(number / 1000000).toStringAsFixed(1)}M';
    }
  }

  /// 显示开发者帮助
  void _showDeveloperHelp() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('开发者帮助'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                '欢迎使用 Creative Workshop 开发者平台！',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 16),
              Text('功能介绍：'),
              SizedBox(height: 8),
              Text('• 项目管理：创建、编辑、管理插件项目'),
              Text('• 插件开发：代码编辑、调试、测试工具'),
              Text('• 发布管理：打包、版本管理、发布流程'),
              Text('• Ming CLI：集成 Ming CLI 开发工具'),
              SizedBox(height: 16),
              Text(
                '快速开始：',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('1. 点击"新建项目"创建插件项目'),
              Text('2. 在"插件开发"标签页编写代码'),
              Text('3. 使用"发布管理"发布到应用商店'),
            ],
          ),
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 打开详细文档
            },
            child: const Text('查看文档'),
          ),
        ],
      ),
    );
  }

  /// 创建新项目
  void _createNewProject() {
    // TODO: Phase ******* - 实现新建项目功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('新建项目功能即将推出...'),
      ),
    );
  }

  /// 导航到应用商店 - 策略A重构阶段5 - 真实实现
  Future<void> _navigateToAppStore() async {
    try {
      // 使用CrossModuleBridge进行真实的跨模块导航
      final bridge = CrossModuleBridge.instance;
      final success = await bridge.requestNavigation(
        'creative_workshop',
        'app_manager',
        '/app_manager/store',
      );

      if (success) {
        // 显示导航成功提示
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('正在跳转到应用商店...'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      } else {
        // 导航失败，显示备用信息
        if (mounted) {
          _showAppStoreInfo();
        }
      }
    } catch (e) {
      // 异常处理，显示备用信息
      if (mounted) {
        _showAppStoreInfo();
      }
    }
  }

  /// 导航到应用管理器 - 策略A重构阶段5
  void _navigateToAppManager() {
    // TODO(cross-module-navigation): 实现跨模块导航到App Manager主页
    try {
      // 使用路由导航到App Manager主页
      Navigator.of(context).pushNamed('/app_manager');
    } catch (e) {
      // 如果路由导航失败，显示提示信息
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('正在跳转到应用管理器...'),
          action: SnackBarAction(
            label: '手动打开',
            onPressed: _showAppManagerInfo,
          ),
        ),
      );
    }
  }

  /// 显示应用商店信息
  void _showAppStoreInfo() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('应用商店'),
        content: const Text(
          '应用商店提供丰富的插件资源，您可以：\n\n'
          '• 浏览和搜索插件\n'
          '• 安装感兴趣的插件\n'
          '• 查看插件详情和评价\n'
          '• 管理已安装的插件\n\n'
          '请通过主应用导航访问应用商店。',
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }

  /// 显示应用管理器信息
  void _showAppManagerInfo() {
    showDialog<void>(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: const Text('应用管理器'),
        content: const Text(
          '应用管理器是系统的核心管理工具，提供：\n\n'
          '• 模块状态监控\n'
          '• 资源使用情况\n'
          '• 权限管理\n'
          '• 系统健康检查\n\n'
          '请通过主应用导航访问应用管理器。',
        ),
        actions: <Widget>[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
        ],
      ),
    );
  }
}
