/*
---------------------------------------------------------------
File name:          plugin_messenger_simple_test.dart 
Author:             lgnorant-lu
Date created:       2025-07-27
Last modified:      2025-07-27
Dart Version:       3.2+
Description:        插件消息传递器简单测试
---------------------------------------------------------------
Change History:
    2025-07-27:    插件消息传递器基础功能测试;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/src/core/plugin_messenger.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

void main() {
  group('PluginMessenger Simple Tests', () {
    late PluginMessenger messenger;
    late PluginRegistry registry;

    setUp(() {
      messenger = PluginMessenger.instance;
      registry = PluginRegistry.instance;
      // 清理注册表
      registry.clear();
    });

    tearDown(() {
      // 清理资源
      registry.clear();
      messenger.clearMessageHistory();
    });

    group('基础功能测试', () {
      test('单例模式验证', () {
        final PluginMessenger instance1 = PluginMessenger.instance;
        final PluginMessenger instance2 = PluginMessenger.instance;
        expect(instance1, same(instance2));
      });

      test('获取通信状态', () {
        final Map<String, dynamic> status = messenger.getStatus();
        expect(status, isA<Map<String, dynamic>>());
        expect(status.containsKey('registeredHandlers'), isTrue);
        expect(status.containsKey('pendingMessages'), isTrue);
        expect(status.containsKey('messageCounter'), isTrue);
      });
    });

    group('消息类型测试', () {
      test('消息类型枚举', () {
        expect(MessageType.values.length, equals(4));
        expect(MessageType.values, contains(MessageType.request));
        expect(MessageType.values, contains(MessageType.response));
        expect(MessageType.values, contains(MessageType.notification));
        expect(MessageType.values, contains(MessageType.broadcast));
      });

      test('消息优先级枚举', () {
        expect(MessagePriority.values.length, equals(4));
        expect(MessagePriority.values, contains(MessagePriority.critical));
        expect(MessagePriority.values, contains(MessagePriority.high));
        expect(MessagePriority.values, contains(MessagePriority.normal));
        expect(MessagePriority.values, contains(MessagePriority.low));
      });
    });

    group('消息数据结构测试', () {
      test('PluginMessage创建', () {
        const PluginMessage message = PluginMessage(
          id: 'test_msg_1',
          type: MessageType.request,
          action: 'test_action',
          senderId: 'sender_plugin',
          targetId: 'target_plugin',
          data: <String, dynamic>{'key': 'value'},
          timeout: 5000,
        );

        expect(message.id, equals('test_msg_1'));
        expect(message.type, equals(MessageType.request));
        expect(message.action, equals('test_action'));
        expect(message.senderId, equals('sender_plugin'));
        expect(message.targetId, equals('target_plugin'));
        expect(message.data, equals(<String, dynamic>{'key': 'value'}));
        expect(message.timeout, equals(5000));
      });

      test('PluginMessageResponse创建', () {
        const PluginMessageResponse response = PluginMessageResponse(
          messageId: 'test_msg_1',
          success: true,
          data: <String, dynamic>{'result': 'success'},
        );

        expect(response.messageId, equals('test_msg_1'));
        expect(response.success, isTrue);
        expect(response.data, equals(<String, dynamic>{'result': 'success'}));
        expect(response.error, isNull);
      });
    });

    group('路由和过滤功能测试', () {
      test('路由规则配置', () {
        const MessageRoutingRule rule = MessageRoutingRule(
          sourcePattern: '*',
          targetPattern: '*',
          actionPattern: '*',
        );

        messenger.configureRouting('test_rule', rule);
        messenger.removeRoutingRule('test_rule');
        
        // 应该正常执行，不抛出异常
        expect(() => messenger.configureRouting('test_rule', rule), returnsNormally);
        expect(() => messenger.removeRoutingRule('test_rule'), returnsNormally);
      });

      test('过滤器管理', () {
        final ContentMessageFilter filter = ContentMessageFilter(
          name: 'test_filter',
          blockedWords: <String>['test'],
        );

        messenger.addMessageFilter(filter);
        messenger.removeMessageFilter('test_filter');
        
        // 应该正常执行，不抛出异常
        expect(() => messenger.addMessageFilter(filter), returnsNormally);
        expect(() => messenger.removeMessageFilter('test_filter'), returnsNormally);
      });
    });

    group('加密和签名测试', () {
      test('SimpleMessageEncryption加密解密', () async {
        final SimpleMessageEncryption encryption = SimpleMessageEncryption('test_key');
        
        const Map<String, dynamic> originalData = <String, dynamic>{
          'message': 'hello world',
          'number': 42,
        };

        final Map<String, dynamic> encryptedData = await encryption.encrypt(originalData);
        expect(encryptedData['encrypted'], isTrue);
        expect(encryptedData['algorithm'], equals('simple_base64'));

        final Map<String, dynamic> decryptedData = await encryption.decrypt(encryptedData);
        expect(decryptedData, equals(originalData));
      });

      test('加密和签名配置', () {
        final SimpleMessageEncryption encryption = SimpleMessageEncryption('test_key');
        final SimpleMessageSignatureVerifier verifier = 
            SimpleMessageSignatureVerifier('secret_key');

        messenger.configureEncryption(encryption);
        messenger.configureSignatureVerification(verifier);
        
        // 应该正常执行，不抛出异常
        expect(() => messenger.configureEncryption(encryption), returnsNormally);
        expect(() => messenger.configureSignatureVerification(verifier), returnsNormally);
      });
    });

    group('消息历史记录测试', () {
      test('获取消息历史记录', () {
        final List<MessageAuditRecord> history = messenger.getMessageHistory();
        expect(history, isA<List<MessageAuditRecord>>());
        expect(history, isEmpty);
      });

      test('清理消息历史记录', () {
        messenger.clearMessageHistory();
        final List<MessageAuditRecord> history = messenger.getMessageHistory();
        expect(history, isEmpty);
      });
    });

    group('错误处理测试', () {
      test('发送消息 - 发送者不存在', () async {
        expect(
          () => messenger.sendMessage(
            'nonexistent_sender',
            'target_plugin',
            'test_action',
            <String, dynamic>{},
          ),
          throwsA(isA<Exception>()),
        );
      });

      test('清理插件消息处理器', () {
        messenger.registerHandler('test_plugin', 'test_action', (PluginMessage message) async => const PluginMessageResponse(
            messageId: 'test',
            success: true,
          ),);

        messenger.cleanupPlugin('test_plugin');
        
        // 应该正常执行，不抛出异常
        expect(() => messenger.cleanupPlugin('test_plugin'), returnsNormally);
      });
    });
  });
}
