/*
---------------------------------------------------------------
File name:          plugin_card.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        插件卡片组件 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 从Creative Workshop迁移插件卡片组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

/// 插件卡片组件
/// 
/// 显示插件的基本信息和操作按钮
class PluginCard extends StatelessWidget {
  /// 插件信息
  final plugin_sys.PluginStoreEntry plugin;
  
  /// 安装回调
  final VoidCallback? onInstall;
  
  /// 查看详情回调
  final VoidCallback? onViewDetails;
  
  /// 是否正在安装
  final bool isInstalling;
  
  /// 是否已安装
  final bool isInstalled;

  const PluginCard({
    super.key,
    required this.plugin,
    this.onInstall,
    this.onViewDetails,
    this.isInstalling = false,
    this.isInstalled = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      clipBehavior: Clip.antiAlias,
      child: InkWell(
        onTap: onViewDetails,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 插件图标和标题
            _buildHeader(context),
            
            // 插件描述
            _buildDescription(context),
            
            // 插件信息
            _buildInfo(context),
            
            // 操作按钮
            _buildActions(context),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // 插件图标
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getCategoryColor(plugin.category),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              _getCategoryIcon(plugin.category),
              color: Colors.white,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          // 插件名称和版本
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  plugin.name,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  'v${plugin.version}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          // 评分
          _buildRating(context),
        ],
      ),
    );
  }

  /// 构建描述
  Widget _buildDescription(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Text(
        plugin.description,
        style: Theme.of(context).textTheme.bodySmall,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  /// 构建信息
  Widget _buildInfo(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // 作者
          Icon(
            Icons.person,
            size: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Expanded(
            child: Text(
              plugin.author,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          const SizedBox(width: 8),
          
          // 下载量
          Icon(
            Icons.download,
            size: 14,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(width: 4),
          Text(
            _formatDownloadCount(plugin.downloadCount),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActions(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Row(
        children: [
          // 分类标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: _getCategoryColor(plugin.category).withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _getCategoryDisplayName(plugin.category),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: _getCategoryColor(plugin.category),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          const Spacer(),
          
          // 安装按钮
          _buildInstallButton(context),
        ],
      ),
    );
  }

  /// 构建评分
  Widget _buildRating(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          Icons.star,
          size: 14,
          color: Colors.amber,
        ),
        const SizedBox(width: 2),
        Text(
          plugin.rating.toStringAsFixed(1),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建安装按钮
  Widget _buildInstallButton(BuildContext context) {
    if (isInstalling) {
      return SizedBox(
        width: 20,
        height: 20,
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    if (isInstalled) {
      return Icon(
        Icons.check_circle,
        color: Colors.green,
        size: 20,
      );
    }

    return SizedBox(
      height: 32,
      child: ElevatedButton(
        onPressed: onInstall,
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          minimumSize: Size.zero,
        ),
        child: const Text(
          '安装',
          style: TextStyle(fontSize: 12),
        ),
      ),
    );
  }

  /// 获取分类颜色
  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'tools':
        return Colors.blue;
      case 'games':
        return Colors.purple;
      case 'productivity':
        return Colors.green;
      case 'graphics':
        return Colors.orange;
      case 'audio':
        return Colors.red;
      case 'video':
        return Colors.indigo;
      case 'development':
        return Colors.teal;
      case 'education':
        return Colors.amber;
      default:
        return Colors.grey;
    }
  }

  /// 获取分类图标
  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'tools':
        return Icons.build;
      case 'games':
        return Icons.games;
      case 'productivity':
        return Icons.work;
      case 'graphics':
        return Icons.image;
      case 'audio':
        return Icons.audiotrack;
      case 'video':
        return Icons.videocam;
      case 'development':
        return Icons.code;
      case 'education':
        return Icons.school;
      default:
        return Icons.extension;
    }
  }

  /// 获取分类显示名称
  String _getCategoryDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'tools':
        return '工具';
      case 'games':
        return '游戏';
      case 'productivity':
        return '效率';
      case 'graphics':
        return '图形';
      case 'audio':
        return '音频';
      case 'video':
        return '视频';
      case 'development':
        return '开发';
      case 'education':
        return '教育';
      default:
        return '其他';
    }
  }

  /// 格式化下载量
  String _formatDownloadCount(int count) {
    if (count >= 1000000) {
      return '${(count / 1000000).toStringAsFixed(1)}M';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}K';
    } else {
      return count.toString();
    }
  }
}
