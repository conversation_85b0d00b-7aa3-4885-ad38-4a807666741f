/*
---------------------------------------------------------------
File name:          cross_module_bridge_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        跨模块通信桥接服务测试 - 策略A重构阶段5
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 跨模块通信桥接服务测试实现;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:app_manager/src/integration/cross_module_bridge.dart';

void main() {
  group('CrossModuleBridge Tests', () {
    late CrossModuleBridge bridge;

    setUp(() {
      bridge = CrossModuleBridge.instance;
    });

    // 注意：不在tearDown中dispose，因为是单例

    test('should be singleton', () {
      final bridge1 = CrossModuleBridge.instance;
      final bridge2 = CrossModuleBridge.instance;
      expect(bridge1, same(bridge2));
    });

    test('should initialize successfully', () async {
      await bridge.initialize();

      // 验证默认模块已注册
      expect(bridge.getModuleState('app_manager'),
          equals(CrossModuleState.initializing));
      expect(bridge.getModuleState('creative_workshop'),
          equals(CrossModuleState.initializing));
    });

    test('should update module state', () async {
      await bridge.initialize();

      bridge.updateModuleState('app_manager', CrossModuleState.ready);
      expect(
          bridge.getModuleState('app_manager'), equals(CrossModuleState.ready));
    });

    test('should handle shared data', () async {
      await bridge.initialize();

      bridge.setSharedData('test_key', 'test_value',
          sourceModule: 'app_manager');
      expect(bridge.getSharedData<String>('test_key'), equals('test_value'));
    });

    test('should handle plugin state sync', () async {
      await bridge.initialize();

      final pluginData = PluginSyncData(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        isInstalled: true,
        isEnabled: true,
      );

      bridge.syncPluginState('test_plugin', pluginData);
      final retrievedData = bridge.getPluginState('test_plugin');

      expect(retrievedData, isNotNull);
      expect(retrievedData!.id, equals('test_plugin'));
      expect(retrievedData.name, equals('Test Plugin'));
      expect(retrievedData.isInstalled, isTrue);
    });

    test('should handle project state sync', () async {
      await bridge.initialize();

      final projectData = ProjectSyncData(
        id: 'test_project',
        name: 'Test Project',
        type: 'plugin',
        status: 'development',
      );

      bridge.syncProjectState('test_project', projectData);
      final retrievedData = bridge.getProjectState('test_project');

      expect(retrievedData, isNotNull);
      expect(retrievedData!.id, equals('test_project'));
      expect(retrievedData.name, equals('Test Project'));
      expect(retrievedData.type, equals('plugin'));
    });

    test('should publish cross module events', () async {
      await bridge.initialize();

      var eventReceived = false;
      String? eventType;

      bridge.events.listen((event) {
        eventReceived = true;
        eventType = event.type;
      });

      bridge.publishCrossModuleEvent('test_event', {'data': 'test'},
          sourceModule: 'test');

      // 等待事件处理
      await Future.delayed(const Duration(milliseconds: 10));

      expect(eventReceived, isTrue);
      expect(eventType, equals('test_event'));
    });

    test('should request navigation', () async {
      await bridge.initialize();

      final result = await bridge.requestNavigation(
        'app_manager',
        'creative_workshop',
        '/creative_workshop/projects',
        params: {'projectId': 'test'},
      );

      expect(result, isTrue);
    });

    test('should handle module state not found', () async {
      await bridge.initialize();

      final state = bridge.getModuleState('non_existent_module');
      expect(state, isNull);
    });

    test('should handle shared data not found', () async {
      await bridge.initialize();

      final data = bridge.getSharedData<String>('non_existent_key');
      expect(data, isNull);
    });

    test('should handle plugin state not found', () async {
      await bridge.initialize();

      final data = bridge.getPluginState('non_existent_plugin');
      expect(data, isNull);
    });

    test('should handle project state not found', () async {
      await bridge.initialize();

      final data = bridge.getProjectState('non_existent_project');
      expect(data, isNull);
    });
  });

  group('CrossModuleState Tests', () {
    test('should have correct values', () {
      expect(CrossModuleState.values, contains(CrossModuleState.initializing));
      expect(CrossModuleState.values, contains(CrossModuleState.ready));
      expect(CrossModuleState.values, contains(CrossModuleState.running));
      expect(CrossModuleState.values, contains(CrossModuleState.error));
      expect(CrossModuleState.values, contains(CrossModuleState.disposed));
    });
  });

  group('PluginSyncData Tests', () {
    test('should create with required fields', () {
      final data = PluginSyncData(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        isInstalled: true,
        isEnabled: false,
      );

      expect(data.id, equals('test_plugin'));
      expect(data.name, equals('Test Plugin'));
      expect(data.version, equals('1.0.0'));
      expect(data.isInstalled, isTrue);
      expect(data.isEnabled, isFalse);
    });

    test('should convert to and from JSON', () {
      final data = PluginSyncData(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        isInstalled: true,
        isEnabled: true,
        lastUpdated: DateTime.now(),
      );

      final json = data.toJson();
      final restored = PluginSyncData.fromJson(json);

      expect(restored.id, equals(data.id));
      expect(restored.name, equals(data.name));
      expect(restored.version, equals(data.version));
      expect(restored.isInstalled, equals(data.isInstalled));
      expect(restored.isEnabled, equals(data.isEnabled));
    });
  });

  group('ProjectSyncData Tests', () {
    test('should create with required fields', () {
      final data = ProjectSyncData(
        id: 'test_project',
        name: 'Test Project',
        type: 'plugin',
        status: 'development',
      );

      expect(data.id, equals('test_project'));
      expect(data.name, equals('Test Project'));
      expect(data.type, equals('plugin'));
      expect(data.status, equals('development'));
    });

    test('should convert to and from JSON', () {
      final data = ProjectSyncData(
        id: 'test_project',
        name: 'Test Project',
        type: 'plugin',
        status: 'development',
        lastModified: DateTime.now(),
      );

      final json = data.toJson();
      final restored = ProjectSyncData.fromJson(json);

      expect(restored.id, equals(data.id));
      expect(restored.name, equals(data.name));
      expect(restored.type, equals(data.type));
      expect(restored.status, equals(data.status));
    });
  });

  group('CrossModuleEvent Tests', () {
    test('should create bridge initialized event', () {
      final event = CrossModuleEvent.bridgeInitialized();
      expect(event.type, equals('bridge_initialized'));
      expect(event.data, isEmpty);
    });

    test('should create module registered event', () {
      final event = CrossModuleEvent.moduleRegistered(
          'test_module', CrossModuleState.ready);
      expect(event.type, equals('module_registered'));
      expect(event.data['moduleId'], equals('test_module'));
      expect(event.data['state'], equals('ready'));
    });

    test('should create custom event', () {
      final event = CrossModuleEvent.custom(
          'custom_event', {'key': 'value'}, 'test_module');
      expect(event.type, equals('custom_event'));
      expect(event.data['key'], equals('value'));
      expect(event.data['sourceModule'], equals('test_module'));
    });
  });
}
