/*
---------------------------------------------------------------
File name:          project_templates.dart
Author:             lgnorant-lu
Date created:       2025-07-18
Last modified:      2025-07-18
Dart Version:       3.2+
Description:        创意项目模板系统
---------------------------------------------------------------
Change History:
    2025-07-18: Initial creation - 项目模板系统实现;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:creative_workshop/src/core/projects/project_manager.dart';

/// 项目模板
class ProjectTemplate {
  const ProjectTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.icon,
    this.tags = const <String>[],
    this.defaultMetadata = const <String, dynamic>{},
    this.defaultData = const <String, dynamic>{},
    this.previewImage,
  });

  /// 模板ID
  final String id;

  /// 模板名称
  final String name;

  /// 模板描述
  final String description;

  /// 项目类型
  final ProjectType type;

  /// 模板图标
  final IconData icon;

  /// 标签列表
  final List<String> tags;

  /// 默认元数据
  final Map<String, dynamic> defaultMetadata;

  /// 默认数据
  final Map<String, dynamic> defaultData;

  /// 预览图片路径
  final String? previewImage;

  /// 创建项目实例
  CreativeProject createProject({
    required String name,
    String? description,
    List<String>? tags,
    Map<String, dynamic>? metadata,
    Map<String, dynamic>? data,
  }) =>
      CreativeProject(
        name: name,
        type: type,
        description: description ?? this.description,
        tags: tags ?? this.tags,
        metadata: <String, dynamic>{
          ...defaultMetadata,
          ...?metadata,
        },
        data: <String, dynamic>{
          ...defaultData,
          ...?data,
        },
      );
}

/// 项目模板管理器
class ProjectTemplateManager {
  ProjectTemplateManager._();

  static final ProjectTemplateManager _instance = ProjectTemplateManager._();
  static ProjectTemplateManager get instance => _instance;

  /// 内置模板列表
  final List<ProjectTemplate> _templates = <ProjectTemplate>[];

  /// 初始化模板管理器
  void initialize() {
    _registerBuiltinTemplates();
    debugPrint('项目模板管理器已初始化，共 ${_templates.length} 个模板');
  }

  /// 注册内置模板
  void _registerBuiltinTemplates() {
    _templates.addAll(<ProjectTemplate>[
      // 应用商店项目模板

      // 设计项目模板
      const ProjectTemplate(
        id: 'ui_design',
        name: 'UI设计',
        description: '用户界面设计项目模板',
        type: ProjectType.design,
        icon: Icons.design_services,
        tags: <String>['设计', 'UI', '界面'],
        defaultMetadata: <String, dynamic>{
          'canvasWidth': 1920,
          'canvasHeight': 1080,
          'gridSize': 8,
          'showGrid': true,
        },
        defaultData: <String, dynamic>{
          'artboards': <Map<String, Object>>[
            <String, Object>{'name': '桌面版', 'width': 1920, 'height': 1080},
            <String, Object>{'name': '移动版', 'width': 375, 'height': 812},
          ],
          'components': <dynamic>[],
        },
      ),

      const ProjectTemplate(
        id: 'logo_design',
        name: 'Logo设计',
        description: '标志和品牌设计项目模板',
        type: ProjectType.design,
        icon: Icons.account_balance,
        tags: <String>['设计', 'Logo', '品牌'],
        defaultMetadata: <String, dynamic>{
          'canvasWidth': 500,
          'canvasHeight': 500,
          'backgroundColor': 0xFFFFFFFF,
        },
        defaultData: <String, dynamic>{
          'versions': <dynamic>[],
          'colorPalette': <dynamic>[],
          'guidelines': <dynamic, dynamic>{},
        },
      ),

      // 插件项目模板
      const ProjectTemplate(
        id: 'simple_plugin',
        name: '简单插件',
        description: '基础插件项目模板',
        type: ProjectType.plugin,
        icon: Icons.extension,
        tags: <String>['插件', '简单'],
        defaultMetadata: <String, dynamic>{
          'pluginType': 'simple',
          'version': '1.0.0',
        },
        defaultData: <String, dynamic>{
          'features': <Map<String, String>>[
            <String, String>{'name': '基础功能', 'type': 'core'},
            <String, String>{'name': '配置管理', 'type': 'config'},
          ],
          'dependencies': <dynamic>[],
        },
      ),

      const ProjectTemplate(
        id: 'utility_plugin',
        name: '实用插件',
        description: '实用工具插件项目模板',
        type: ProjectType.plugin,
        icon: Icons.build,
        tags: <String>['插件', '实用', '工具'],
        defaultMetadata: <String, dynamic>{
          'pluginType': 'utility',
          'category': 'tools',
          'version': '1.0.0',
        },
        defaultData: <String, dynamic>{
          'tools': <dynamic>[],
          'permissions': <String>['read', 'write'],
        },
      ),

      // 动画项目模板
      const ProjectTemplate(
        id: 'simple_animation',
        name: '简单动画',
        description: '基础动画项目模板',
        type: ProjectType.animation,
        icon: Icons.movie,
        tags: <String>['动画', '简单'],
        defaultMetadata: <String, dynamic>{
          'frameRate': 24,
          'duration': 5.0,
          'resolution': '1920x1080',
        },
        defaultData: <String, dynamic>{
          'timeline': <dynamic>[],
          'keyframes': <dynamic>[],
        },
      ),

      // 3D项目模板
      const ProjectTemplate(
        id: 'simple_3d',
        name: '简单3D模型',
        description: '基础3D建模项目模板',
        type: ProjectType.model3d,
        icon: Icons.view_in_ar,
        tags: <String>['3D', '建模'],
        defaultMetadata: <String, dynamic>{
          'renderer': 'basic',
          'lighting': 'default',
        },
        defaultData: <String, dynamic>{
          'objects': <dynamic>[],
          'materials': <dynamic>[],
          'cameras': <Map<String, Object>>[
            <String, Object>{
              'name': '主相机',
              'position': <int>[0, 0, 5],
            },
          ],
        },
      ),

      // 混合项目模板
      const ProjectTemplate(
        id: 'creative_mix',
        name: '创意混合',
        description: '包含多种创意元素的混合项目',
        type: ProjectType.mixed,
        icon: Icons.auto_awesome,
        tags: <String>['混合', '创意', '多元素'],
        defaultMetadata: <String, dynamic>{
          'components': <String>['plugin', 'design', 'animation'],
        },
        defaultData: <String, dynamic>{
          'sections': <Map<String, String>>[
            <String, String>{'type': 'plugin', 'name': '插件部分'},
            <String, String>{'type': 'design', 'name': '设计部分'},
          ],
        },
      ),
    ]);
  }

  /// 获取所有模板
  List<ProjectTemplate> get templates => List.unmodifiable(_templates);

  /// 按类型获取模板
  List<ProjectTemplate> getTemplatesByType(ProjectType type) => _templates
      .where((ProjectTemplate template) => template.type == type)
      .toList();

  /// 按标签获取模板
  List<ProjectTemplate> getTemplatesByTag(String tag) => _templates
      .where(
        (ProjectTemplate template) => template.tags
            .any((String t) => t.toLowerCase().contains(tag.toLowerCase())),
      )
      .toList();

  /// 搜索模板
  List<ProjectTemplate> searchTemplates(String query) {
    final lowerQuery = query.toLowerCase();
    return _templates
        .where(
          (ProjectTemplate template) =>
              template.name.toLowerCase().contains(lowerQuery) ||
              template.description.toLowerCase().contains(lowerQuery) ||
              template.tags
                  .any((String tag) => tag.toLowerCase().contains(lowerQuery)),
        )
        .toList();
  }

  /// 获取模板
  ProjectTemplate? getTemplate(String templateId) {
    try {
      return _templates
          .firstWhere((ProjectTemplate template) => template.id == templateId);
    } catch (e) {
      return null;
    }
  }

  /// 注册自定义模板
  bool registerTemplate(ProjectTemplate template) {
    // 检查ID是否已存在
    if (_templates.any((ProjectTemplate t) => t.id == template.id)) {
      debugPrint('模板ID已存在: ${template.id}');
      return false;
    }

    _templates.add(template);
    debugPrint('自定义模板已注册: ${template.name}');
    return true;
  }

  /// 注销模板
  bool unregisterTemplate(String templateId) {
    final index =
        _templates.indexWhere((ProjectTemplate t) => t.id == templateId);
    if (index == -1) {
      debugPrint('模板不存在: $templateId');
      return false;
    }

    final template = _templates.removeAt(index);
    debugPrint('模板已注销: ${template.name}');
    return true;
  }

  /// 获取模板统计信息
  Map<String, dynamic> getTemplateStats() {
    final stats = <String, dynamic>{};

    // 按类型统计
    for (final type in ProjectType.values) {
      stats['type_${type.name}'] = getTemplatesByType(type).length;
    }

    // 总数统计
    stats['total'] = _templates.length;

    // 标签统计
    final allTags = <String>{};
    for (final template in _templates) {
      allTags.addAll(template.tags);
    }
    stats['uniqueTags'] = allTags.length;

    return stats;
  }
}
