/// Pet App V3 模块集成测试
/// 
/// 测试Plugin System、Creative Workshop、Ming CLI三个核心模块
/// 的集成调用架构是否正常工作。
/// 
/// 作者: Pet App Team
/// 创建时间: 2025-07-29
/// 版本: 1.0.0

import 'package:flutter_test/flutter_test.dart';
import 'package:pet_app_v3/core/integration/module_coordinator.dart';

void main() {
  group('模块集成测试', () {
    late ModuleCoordinator coordinator;

    setUp(() {
      coordinator = ModuleCoordinator.instance;
    });

    tearDown(() async {
      await coordinator.dispose();
    });

    test('模块协调器初始化测试', () async {
      // 测试初始化
      await coordinator.initialize();
      
      // 验证模块已注册
      final modules = coordinator.getAllModules();
      expect(modules.length, greaterThan(0));
      
      // 验证Plugin System已注册
      final pluginSystem = coordinator.getModule('plugin_system');
      expect(pluginSystem, isNotNull);
      expect(pluginSystem!.metadata.id, equals('plugin_system'));
      
      // 验证Creative Workshop已注册
      final creativeWorkshop = coordinator.getModule('creative_workshop');
      expect(creativeWorkshop, isNotNull);
      expect(creativeWorkshop!.metadata.id, equals('creative_workshop'));
    });

    test('模块通信测试', () async {
      await coordinator.initialize();
      
      // 测试发送请求到Plugin System
      final response = await coordinator.communication.sendRequest(
        'plugin_system',
        'list_plugins',
        {},
      );
      
      expect(response, isNotNull);
      expect(response!['success'], isTrue);
    });

    test('系统状态查询测试', () async {
      await coordinator.initialize();
      
      final status = await coordinator.getSystemStatus();
      
      expect(status['coordinator'], isNotNull);
      expect(status['coordinator']['initialized'], isTrue);
      expect(status['modules'], isNotNull);
      expect(status['modules']['plugin_system'], isNotNull);
      expect(status['modules']['creative_workshop'], isNotNull);
    });

    test('模块启动停止测试', () async {
      await coordinator.initialize();
      
      // 启动所有模块
      await coordinator.startAllModules();
      
      // 验证模块状态
      final pluginSystem = coordinator.getModule('plugin_system');
      expect(pluginSystem!.status.name, equals('running'));
      
      final creativeWorkshop = coordinator.getModule('creative_workshop');
      expect(creativeWorkshop!.status.name, equals('running'));
      
      // 停止所有模块
      await coordinator.stopAllModules();
      
      // 验证模块已停止
      expect(pluginSystem.status.name, equals('stopped'));
      expect(creativeWorkshop.status.name, equals('stopped'));
    });

    test('事件广播测试', () async {
      await coordinator.initialize();
      
      bool eventReceived = false;
      Map<String, dynamic>? eventData;
      
      // 订阅事件
      coordinator.communication.subscribeToEvent('test_event', (data) async {
        eventReceived = true;
        eventData = data;
      });
      
      // 广播事件
      await coordinator.communication.broadcastEvent('test_event', {
        'message': 'Hello from test',
        'timestamp': DateTime.now().toIso8601String(),
      });
      
      // 等待事件处理
      await Future.delayed(const Duration(milliseconds: 100));
      
      expect(eventReceived, isTrue);
      expect(eventData, isNotNull);
      expect(eventData!['message'], equals('Hello from test'));
    });
  });
}
