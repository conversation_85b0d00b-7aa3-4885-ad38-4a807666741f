/*
---------------------------------------------------------------
File name:          category_filter.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        分类过滤器组件 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 从Creative Workshop迁移分类过滤器组件;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';

/// 分类过滤器组件
/// 
/// 提供插件分类筛选功能
class CategoryFilter extends StatelessWidget {
  /// 可用分类列表
  final List<String> categories;
  
  /// 当前选中的分类
  final String? selectedCategory;
  
  /// 分类变化回调
  final Function(String?)? onCategoryChanged;
  
  /// 是否显示全部选项
  final bool showAllOption;
  
  /// 全部选项文本
  final String allOptionText;

  const CategoryFilter({
    super.key,
    required this.categories,
    this.selectedCategory,
    this.onCategoryChanged,
    this.showAllOption = true,
    this.allOptionText = '全部',
  });

  @override
  Widget build(BuildContext context) {
    if (categories.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: _getItemCount(),
        itemBuilder: (context, index) {
          if (showAllOption && index == 0) {
            return _buildCategoryChip(
              context,
              allOptionText,
              null,
              selectedCategory == null,
            );
          }
          
          final categoryIndex = showAllOption ? index - 1 : index;
          final category = categories[categoryIndex];
          final isSelected = selectedCategory == category;
          
          return _buildCategoryChip(
            context,
            _getCategoryDisplayName(category),
            category,
            isSelected,
          );
        },
      ),
    );
  }

  /// 获取项目数量
  int _getItemCount() {
    return showAllOption ? categories.length + 1 : categories.length;
  }

  /// 构建分类芯片
  Widget _buildCategoryChip(
    BuildContext context,
    String label,
    String? category,
    bool isSelected,
  ) {
    return Container(
      margin: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: isSelected,
        onSelected: (selected) {
          if (selected) {
            onCategoryChanged?.call(category);
          } else {
            onCategoryChanged?.call(null);
          }
        },
        backgroundColor: Theme.of(context).colorScheme.surface,
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
        checkmarkColor: Theme.of(context).colorScheme.onPrimaryContainer,
        labelStyle: TextStyle(
          color: isSelected
              ? Theme.of(context).colorScheme.onPrimaryContainer
              : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
        ),
        side: BorderSide(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : Theme.of(context).colorScheme.outline.withOpacity(0.2),
        ),
        elevation: isSelected ? 2 : 0,
        pressElevation: 4,
      ),
    );
  }

  /// 获取分类显示名称
  String _getCategoryDisplayName(String category) {
    switch (category.toLowerCase()) {
      case 'tools':
        return '工具';
      case 'games':
        return '游戏';
      case 'productivity':
        return '效率';
      case 'graphics':
        return '图形';
      case 'audio':
        return '音频';
      case 'video':
        return '视频';
      case 'development':
        return '开发';
      case 'education':
        return '教育';
      case 'entertainment':
        return '娱乐';
      case 'business':
        return '商务';
      case 'finance':
        return '财务';
      case 'health':
        return '健康';
      case 'lifestyle':
        return '生活';
      case 'news':
        return '新闻';
      case 'photo':
        return '摄影';
      case 'reference':
        return '参考';
      case 'social':
        return '社交';
      case 'sports':
        return '体育';
      case 'travel':
        return '旅行';
      case 'utilities':
        return '实用工具';
      case 'weather':
        return '天气';
      default:
        // 如果没有匹配的翻译，返回首字母大写的原文
        return category.isNotEmpty
            ? category[0].toUpperCase() + category.substring(1).toLowerCase()
            : category;
    }
  }
}
