/*
---------------------------------------------------------------
File name:          permission_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-23
Last modified:      2025-07-23
Dart Version:       3.2+
Description:        统一插件权限管理器 - 集成Creative Workshop完整实现
---------------------------------------------------------------
Change History:
    2025-07-23: Phase 1.1.1 - 统一权限管理系统，集成Creative Workshop实现;
    2025-07-19: Initial creation - 插件权限管理系统;
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:plugin_system/src/core/plugin.dart';

import 'package:plugin_system/src/core/plugin_registry.dart';

/// 权限策略
enum PermissionPolicy {
  /// 允许
  allow,

  /// 拒绝
  deny,

  /// 询问用户
  ask,
}

/// 权限授权结果
class PermissionAuthorizationResult {
  const PermissionAuthorizationResult({
    required this.permission,
    required this.granted,
    this.reason,
    this.timestamp,
  });

  /// 权限
  final PluginPermission permission;

  /// 是否授权
  final bool granted;

  /// 拒绝原因
  final String? reason;

  /// 授权时间
  final DateTime? timestamp;
}

/// 权限管理器
///
/// 负责插件权限的验证、授权和管理
class PermissionManager {
  PermissionManager._();

  /// 单例实例
  static final PermissionManager _instance = PermissionManager._();
  static PermissionManager get instance => _instance;

  /// 插件注册表引用
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 权限策略配置
  final Map<PluginPermission, PermissionPolicy> _permissionPolicies =
      <PluginPermission, PermissionPolicy>{};

  /// 插件权限授权记录
  final Map<String, Map<PluginPermission, PermissionAuthorizationResult>>
      _pluginPermissions =
      <String, Map<PluginPermission, PermissionAuthorizationResult>>{};

  /// 权限组合规则
  final Map<Set<PluginPermission>, bool> _permissionCombinationRules =
      <Set<PluginPermission>, bool>{};

  /// 用户授权回调
  Future<bool> Function(
    String pluginId,
    PluginPermission permission,
    String? reason,
  )? _userAuthorizationCallback;

  /// 初始化权限管理器
  Future<void> initialize() async {
    try {
      // 1. 加载默认权限策略
      _initializeDefaultPolicies();

      // 2. 加载用户自定义权限策略
      await _loadUserPermissionPolicies();

      // 3. 初始化权限组合规则
      _initializePluginPermissionCombinationRules();

      // 4. 设置权限白名单
      _initializePermissionWhitelist();

      // 5. 初始化权限缓存
      _initializePermissionCache();

      debugPrint('权限管理器初始化完成');
    } catch (e) {
      debugPrint('权限管理器初始化失败: $e');
      rethrow;
    }
  }

  /// 验证插件权限
  ///
  /// [pluginId] 插件ID
  /// [permissions] 需要验证的权限列表
  Future<List<PermissionAuthorizationResult>> validatePluginPermissions(
    String pluginId,
    List<PluginPermission> permissions,
  ) async {
    final results = <PermissionAuthorizationResult>[];

    try {
      // 1. 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        for (final permission in permissions) {
          results.add(PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '插件不存在: $pluginId',
            timestamp: DateTime.now(),
          ),);
        }
        return results;
      }

      // 2. 检查权限白名单
      final whitelistedPermissions = permissions
          .where(
            _permissionWhitelist.contains,
          )
          .toList();

      // 使用白名单权限进行验证
      if (whitelistedPermissions.isEmpty) {
        return permissions
            .map((PluginPermission permission) => PermissionAuthorizationResult(
                  permission: permission,
                  granted: false,
                  reason: 'Permission not in whitelist',
                  timestamp: DateTime.now(),
                ),)
            .toList();
      }

      // 3. 检查权限组合是否合法
      final combinationResult = await _validatePluginPermissionCombination(
        pluginId,
        permissions,
      );
      if (!combinationResult) {
        for (final permission in permissions) {
          results.add(PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '权限组合不安全',
            timestamp: DateTime.now(),
          ),);
        }
        return results;
      }

      // 4. 验证每个权限
      for (final permission in permissions) {
        // 检查缓存
        final cachedResult = _getPermissionFromCache(pluginId, permission);
        if (cachedResult != null) {
          results.add(PermissionAuthorizationResult(
            permission: permission,
            granted: cachedResult,
            reason: cachedResult ? '缓存中已授权' : '缓存中已拒绝',
            timestamp: DateTime.now(),
          ),);
          continue;
        }

        // 执行权限验证
        final result = await _validateSinglePluginPermission(
          pluginId,
          permission,
        );
        results.add(result);

        // 更新缓存
        _updatePermissionCache(pluginId, permission, result.granted);
      }

      // 5. 检查用户授权状态
      await _checkUserAuthorizationStatus(pluginId, permissions);

      return results;
    } on Exception catch (e) {
      debugPrint('权限验证失败: $e');
      // 返回拒绝结果
      for (final permission in permissions) {
        results.add(PermissionAuthorizationResult(
          permission: permission,
          granted: false,
          reason: '权限验证过程中发生错误: $e',
          timestamp: DateTime.now(),
        ),);
      }
      return results;
    }
  }

  /// 请求权限授权
  ///
  /// [pluginId] 插件ID
  /// [permission] 权限
  /// [reason] 请求原因
  Future<PermissionAuthorizationResult> requestPluginPermission(
    String pluginId,
    PluginPermission permission, {
    String? reason,
  }) async {
    try {
      // 1. 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        return _denyPluginPermission(
          pluginId,
          permission,
          '插件不存在: $pluginId',
        );
      }

      // 2. 检查是否已有有效授权
      final existingAuth = _getExistingAuthorization(pluginId, permission);
      if (existingAuth != null && _isAuthorizationValid(existingAuth)) {
        debugPrint('插件 $pluginId 权限 ${permission.id} 已有有效授权');
        return existingAuth;
      }

      // 3. 检查权限策略
      final policy = _getPermissionPolicy(permission);

      // 4. 记录权限请求
      await _recordPermissionRequest(pluginId, permission, reason);

      // 5. 根据策略处理权限请求
      final result = await _processPermissionByPolicy(
        pluginId,
        permission,
        policy,
        reason,
      );

      // 6. 记录授权结果
      await _recordAuthorizationResult(pluginId, permission, result);

      // 7. 更新权限缓存
      _updatePermissionCache(pluginId, permission, result.granted);

      // 8. 通知相关组件
      await _notifyPermissionChange(pluginId, permission, result);

      return result;
    } on Exception catch (e) {
      debugPrint('权限请求失败: $e');
      return _denyPluginPermission(
        pluginId,
        permission,
        '权限请求过程中发生错误: $e',
      );
    }
  }

  /// 撤销权限
  ///
  /// [pluginId] 插件ID
  /// [permission] 权限，如果为null则撤销所有权限
  Future<void> revokePluginPermission(
    String pluginId, [
    PluginPermission? permission,
  ]) async {
    try {
      // 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        debugPrint('插件不存在，无法撤销权限: $pluginId');
        return;
      }

      if (permission == null) {
        // 撤销所有权限
        final removedPermissions =
            _pluginPermissions[pluginId]?.keys.toList() ?? <PluginPermission>[];

        // 1. 移除权限授权记录
        _pluginPermissions.remove(pluginId);

        // 2. 更新权限缓存
        _permissionCache.remove(pluginId);

        // 3. 通知插件权限变更
        for (final removedPermission in removedPermissions) {
          await _notifyPermissionChange(
            pluginId,
            removedPermission,
            PermissionAuthorizationResult(
              permission: removedPermission,
              granted: false,
              reason: '权限已撤销',
              timestamp: DateTime.now(),
            ),
          );
        }

        debugPrint('已撤销插件 $pluginId 的所有权限 (${removedPermissions.length}个)');
      } else {
        // 撤销单个权限
        final pluginPerms = _pluginPermissions[pluginId];
        if (pluginPerms == null || !pluginPerms.containsKey(permission)) {
          debugPrint('插件 $pluginId 未拥有权限 ${permission.id}，无需撤销');
          return;
        }

        // 1. 移除权限授权记录
        pluginPerms.remove(permission);

        // 如果插件没有任何权限了，移除整个记录
        if (pluginPerms.isEmpty) {
          _pluginPermissions.remove(pluginId);
        }

        // 2. 更新权限缓存
        _updatePermissionCache(pluginId, permission, false);

        // 3. 通知插件权限变更
        await _notifyPermissionChange(
          pluginId,
          permission,
          PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '权限已撤销',
            timestamp: DateTime.now(),
          ),
        );

        debugPrint('已撤销插件 $pluginId 的权限: ${permission.id}');
      }

      // 4. 记录撤销操作
      await _recordPermissionRevocation(pluginId, permission);
    } on Exception catch (e) {
      debugPrint('撤销权限失败: $e');
    }
  }

  /// 检查插件是否有指定权限
  ///
  /// [pluginId] 插件ID
  /// [permission] 权限
  bool hasPluginPermission(String pluginId, PluginPermission permission) {
    try {
      // 1. 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        debugPrint('插件不存在: $pluginId');
        return false;
      }

      // 2. 检查权限白名单
      if (_permissionWhitelist.contains(permission)) {
        debugPrint('权限 ${permission.id} 在白名单中，自动允许');
        return true;
      }

      // 3. 检查权限授权记录
      final pluginPerms = _pluginPermissions[pluginId];
      if (pluginPerms == null) {
        debugPrint('插件 $pluginId 无权限记录');
        return false;
      }

      final result = pluginPerms[permission];
      if (result == null) {
        debugPrint('插件 $pluginId 无权限 ${permission.id} 的授权记录');
        return false;
      }

      // 4. 验证权限是否过期
      if (!_isAuthorizationValid(result)) {
        debugPrint('插件 $pluginId 权限 ${permission.id} 已过期');
        // 清理过期权限
        pluginPerms.remove(permission);
        _updatePermissionCache(pluginId, permission, false);
        return false;
      }

      // 5. 检查权限是否被撤销
      if (!result.granted) {
        debugPrint('插件 $pluginId 权限 ${permission.id} 已被撤销');
        return false;
      }

      // 6. 更新缓存
      _updatePermissionCache(pluginId, permission, true);

      return true;
    } on Exception catch (e) {
      debugPrint('权限检查失败: $e');
      return false;
    }
  }

  /// 获取插件的所有权限
  ///
  /// [pluginId] 插件ID
  List<PluginPermission> getPluginPermissions(String pluginId) {
    try {
      // 1. 检查插件是否存在
      if (!_registry.contains(pluginId)) {
        debugPrint('插件不存在: $pluginId');
        return <PluginPermission>[];
      }

      // 2. 获取插件权限记录
      final pluginPerms = _pluginPermissions[pluginId];
      if (pluginPerms == null || pluginPerms.isEmpty) {
        debugPrint('插件 $pluginId 无权限记录');
        return <PluginPermission>[];
      }

      // 3. 过滤有效且已授权的权限
      final validPermissions = <PluginPermission>[];
      final expiredPermissions = <PluginPermission>[];

      for (final entry in pluginPerms.entries) {
        final permission = entry.key;
        final result = entry.value;

        // 检查权限是否有效且已授权
        if (result.granted && _isAuthorizationValid(result)) {
          validPermissions.add(permission);
        } else if (!_isAuthorizationValid(result)) {
          // 标记过期权限
          expiredPermissions.add(permission);
        }
      }

      // 4. 清理过期权限
      for (final expiredPermission in expiredPermissions) {
        pluginPerms.remove(expiredPermission);
        _updatePermissionCache(pluginId, expiredPermission, false);
      }

      // 如果所有权限都过期了，移除整个插件记录
      if (pluginPerms.isEmpty) {
        _pluginPermissions.remove(pluginId);
        _permissionCache.remove(pluginId);
      }

      debugPrint(
        '插件 $pluginId 有效权限: ${validPermissions.length}个, '
        '过期权限: ${expiredPermissions.length}个',
      );

      return validPermissions;
    } on Exception catch (e) {
      debugPrint('获取插件权限列表失败: $e');
      return <PluginPermission>[];
    }
  }

  /// 设置权限策略
  ///
  /// [permission] 权限
  /// [policy] 策略
  void setPermissionPolicy(
    PluginPermission permission,
    PermissionPolicy policy,
  ) {
    try {
      // 1. 验证策略合法性
      if (!_isValidPermissionPolicy(permission, policy)) {
        debugPrint('无效的权限策略: 权限=${permission.id}, 策略=$policy');
        return;
      }

      // 2. 检查策略变更
      final oldPolicy = _permissionPolicies[permission];
      if (oldPolicy == policy) {
        debugPrint('权限策略未变更: 权限=${permission.id}, 策略=$policy');
        return;
      }

      // 3. 更新策略配置
      _permissionPolicies[permission] = policy;

      // 4. 记录策略变更
      debugPrint(
        '权限策略已更新: 权限=${permission.id}, '
        '旧策略=$oldPolicy, 新策略=$policy',
      );

      // 5. 持久化策略设置
      _persistPermissionPolicy(permission, policy);

      // 6. 通知策略变更
      _notifyPolicyChange(permission, oldPolicy, policy);

      // 7. 如果策略变为拒绝，撤销所有相关权限
      if (policy == PermissionPolicy.deny) {
        _revokePermissionForAllPlugins(permission);
      }
    } on Exception catch (e) {
      debugPrint('设置权限策略失败: $e');
    }
  }

  /// 清理插件权限
  ///
  /// [pluginId] 插件ID
  void cleanupPluginPermissions(String pluginId) {
    try {
      // 检查插件是否存在权限记录
      final pluginPerms = _pluginPermissions[pluginId];
      if (pluginPerms == null || pluginPerms.isEmpty) {
        debugPrint('插件 $pluginId 无权限记录，无需清理');
        return;
      }

      // 记录要清理的权限
      final permissionsToCleanup = pluginPerms.keys.toList();

      // 1. 移除所有权限授权记录
      _pluginPermissions.remove(pluginId);

      // 2. 清理权限缓存
      _permissionCache.remove(pluginId);

      // 3. 通知权限变更
      for (final permission in permissionsToCleanup) {
        _notifyPermissionChange(
          pluginId,
          permission,
          PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '插件权限已清理',
            timestamp: DateTime.now(),
          ),
        );
      }

      // 4. 记录清理操作
      _recordPermissionCleanup(pluginId, permissionsToCleanup);

      debugPrint('已清理插件 $pluginId 的所有权限 (${permissionsToCleanup.length}个)');
    } on Exception catch (e) {
      debugPrint('清理插件权限失败: $e');
    }
  }

  /// 初始化默认权限策略
  void _initializeDefaultPolicies() {
    // 配置默认权限策略
    // 根据权限类型设置合理的默认策略

    // 1. 基础权限 - 自动允许（低风险）
    _permissionPolicies[PluginPermission.notifications] =
        PermissionPolicy.allow;
    _permissionPolicies[PluginPermission.clipboard] = PermissionPolicy.allow;
    _permissionPolicies[PluginPermission.deviceInfo] = PermissionPolicy.allow;

    // 2. 敏感权限 - 需要用户确认（中等风险）
    _permissionPolicies[PluginPermission.fileSystem] = PermissionPolicy.ask;
    _permissionPolicies[PluginPermission.network] = PermissionPolicy.ask;
    _permissionPolicies[PluginPermission.camera] = PermissionPolicy.ask;
    _permissionPolicies[PluginPermission.microphone] = PermissionPolicy.ask;
    _permissionPolicies[PluginPermission.location] = PermissionPolicy.ask;

    // 3. 高风险权限 - 默认拒绝（高风险）
    _permissionPolicies[PluginPermission.systemSettings] =
        PermissionPolicy.deny;

    // 4. 其他权限默认询问用户
    for (final permission in PluginPermission.values) {
      _permissionPolicies.putIfAbsent(permission, () => PermissionPolicy.ask);
    }

    // 5. 记录策略初始化
    debugPrint('默认权限策略初始化完成:');
    debugPrint(
        '  自动允许: ${_getPermissionsByPolicy(PermissionPolicy.allow).length}个',);
    debugPrint(
        '  需要确认: ${_getPermissionsByPolicy(PermissionPolicy.ask).length}个',);
    debugPrint(
        '  默认拒绝: ${_getPermissionsByPolicy(PermissionPolicy.deny).length}个',);
  }

  /// 初始化权限组合规则
  void _initializePluginPermissionCombinationRules() {
    // 配置权限组合规则
    // 定义哪些权限组合是不安全的

    // 1. 高风险组合：文件系统 + 网络访问
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.fileSystem,
      PluginPermission.network,
    }] = false;

    // 2. 高风险组合：文件系统 + 系统设置
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.fileSystem,
      PluginPermission.systemSettings,
    }] = false;

    // 3. 高风险组合：网络访问 + 系统设置
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.network,
      PluginPermission.systemSettings,
    }] = false;

    // 4. 极高风险组合：文件系统 + 网络 + 系统设置
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.fileSystem,
      PluginPermission.network,
      PluginPermission.systemSettings,
    }] = false;

    // 5. 隐私风险组合：摄像头 + 麦克风 + 位置
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.camera,
      PluginPermission.microphone,
      PluginPermission.location,
    }] = false;

    // 6. 数据泄露风险：摄像头 + 网络
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.camera,
      PluginPermission.network,
    }] = false;

    // 7. 数据泄露风险：麦克风 + 网络
    _permissionCombinationRules[<PluginPermission>{
      PluginPermission.microphone,
      PluginPermission.network,
    }] = false;

    debugPrint('权限组合规则初始化完成，共配置 ${_permissionCombinationRules.length} 个危险组合');
  }

  /// 验证单个权限
  Future<PermissionAuthorizationResult> _validateSinglePluginPermission(
    String pluginId,
    PluginPermission permission,
  ) async {
    try {
      // 1. 检查权限白名单
      if (_permissionWhitelist.contains(permission)) {
        debugPrint('权限 ${permission.id} 在白名单中，自动授权');
        return PermissionAuthorizationResult(
          permission: permission,
          granted: true,
          reason: '权限在白名单中',
          timestamp: DateTime.now(),
        );
      }

      // 2. 检查现有授权记录
      final existingAuth = _getExistingAuthorization(pluginId, permission);
      if (existingAuth != null) {
        // 验证授权是否仍然有效
        if (_isAuthorizationValid(existingAuth)) {
          debugPrint('插件 $pluginId 权限 ${permission.id} 已有有效授权');
          return existingAuth;
        } else {
          debugPrint('插件 $pluginId 权限 ${permission.id} 授权已过期，需要重新验证');
          // 清理过期授权
          _pluginPermissions[pluginId]?.remove(permission);
        }
      }

      // 3. 检查权限策略
      final policy = _getPermissionPolicy(permission);

      // 4. 根据策略进行验证
      switch (policy) {
        case PermissionPolicy.allow:
          return PermissionAuthorizationResult(
            permission: permission,
            granted: true,
            reason: '权限策略允许',
            timestamp: DateTime.now(),
          );
        case PermissionPolicy.deny:
          return PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '权限策略拒绝',
            timestamp: DateTime.now(),
          );
        case PermissionPolicy.ask:
          // 需要用户确认，返回待确认状态
          return PermissionAuthorizationResult(
            permission: permission,
            granted: false,
            reason: '需要用户确认',
            timestamp: DateTime.now(),
          );
      }
    } on Exception catch (e) {
      debugPrint('单个权限验证失败: $e');
      return PermissionAuthorizationResult(
        permission: permission,
        granted: false,
        reason: '权限验证过程中发生错误: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  /// 验证权限组合
  Future<bool> _validatePluginPermissionCombination(
    String pluginId,
    List<PluginPermission> permissions,
  ) async {
    // 实现权限组合验证
    // 检查权限组合是否安全

    final Set<PluginPermission> permissionSet = permissions.toSet();

    // 检查危险权限组合
    for (final MapEntry<Set<PluginPermission>, bool> rule
        in _permissionCombinationRules.entries) {
      if (rule.key.every(permissionSet.contains)) {
        if (!rule.value) {
          debugPrint('检测到危险权限组合: ${rule.key.map((PluginPermission p) => p.id).join(', ')}');
          return false;
        }
      }
    }

    // 检查特殊权限组合规则
    if (permissionSet.contains(PluginPermission.fileSystem) &&
        permissionSet.contains(PluginPermission.network) &&
        permissionSet.contains(PluginPermission.systemSettings)) {
      debugPrint('检测到高风险权限组合: 文件系统 + 网络 + 系统设置');
      return false;
    }

    return true;
  }

  /// 获取权限策略
  PermissionPolicy _getPermissionPolicy(PluginPermission permission) =>
      _permissionPolicies[permission] ?? PermissionPolicy.ask;

  /// 授予权限
  PermissionAuthorizationResult _grantPluginPermission(
    String pluginId,
    PluginPermission permission,
    String? reason,
  ) {
    try {
      // 1. 验证插件是否存在
      if (!_registry.contains(pluginId)) {
        debugPrint('插件不存在，无法授予权限: $pluginId');
        return PermissionAuthorizationResult(
          permission: permission,
          granted: false,
          reason: '插件不存在',
          timestamp: DateTime.now(),
        );
      }

      // 2. 检查权限策略是否允许授予
      final policy = _getPermissionPolicy(permission);
      if (policy == PermissionPolicy.deny) {
        debugPrint('权限策略拒绝，无法授予权限: ${permission.id}');
        return PermissionAuthorizationResult(
          permission: permission,
          granted: false,
          reason: '权限策略拒绝',
          timestamp: DateTime.now(),
        );
      }

      // 3. 创建授权结果
      final result = PermissionAuthorizationResult(
        permission: permission,
        granted: true,
        reason: reason ?? '权限已授予',
        timestamp: DateTime.now(),
      );

      // 4. 存储权限授权记录
      _pluginPermissions.putIfAbsent(
        pluginId,
        () => <PluginPermission, PermissionAuthorizationResult>{},
      );
      _pluginPermissions[pluginId]![permission] = result;

      // 5. 更新权限缓存
      _updatePermissionCache(pluginId, permission, true);

      // 6. 记录授权操作
      debugPrint('权限授予成功: 插件=$pluginId, 权限=${permission.id}, 原因=$reason');

      return result;
    } on Exception catch (e) {
      debugPrint('权限授予失败: $e');
      return PermissionAuthorizationResult(
        permission: permission,
        granted: false,
        reason: '权限授予过程中发生错误: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  /// 拒绝权限
  PermissionAuthorizationResult _denyPluginPermission(
    String pluginId,
    PluginPermission permission,
    String reason,
  ) {
    try {
      // 1. 创建拒绝结果
      final result = PermissionAuthorizationResult(
        permission: permission,
        granted: false,
        reason: reason,
        timestamp: DateTime.now(),
      );

      // 2. 记录拒绝决定（用于审计）
      _pluginPermissions.putIfAbsent(
        pluginId,
        () => <PluginPermission, PermissionAuthorizationResult>{},
      );
      _pluginPermissions[pluginId]![permission] = result;

      // 3. 更新权限缓存
      _updatePermissionCache(pluginId, permission, false);

      // 4. 记录拒绝操作
      debugPrint('权限拒绝: 插件=$pluginId, 权限=${permission.id}, 原因=$reason');

      return result;
    } on Exception catch (e) {
      debugPrint('权限拒绝处理失败: $e');
      return PermissionAuthorizationResult(
        permission: permission,
        granted: false,
        reason: '权限拒绝处理过程中发生错误: $e',
        timestamp: DateTime.now(),
      );
    }
  }

  /// 询问用户权限
  Future<PermissionAuthorizationResult> _askUserPluginPermission(
    String pluginId,
    PluginPermission permission,
    String? reason,
  ) async {
    try {
      // 1. 检查是否有用户授权回调
      if (_userAuthorizationCallback == null) {
        debugPrint('无用户授权回调，默认拒绝权限: ${permission.id}');
        return _denyPluginPermission(
          pluginId,
          permission,
          '无用户授权回调',
        );
      }

      // 2. 显示权限请求对话框
      debugPrint('显示权限请求对话框: 插件=$pluginId, 权限=${permission.id}');

      final userGranted = await _userAuthorizationCallback!(
        pluginId,
        permission,
        reason,
      );

      // 3. 处理用户选择
      if (userGranted) {
        // 用户授权
        final result = _grantPluginPermission(pluginId, permission, '用户授权');

        // 4. 记录用户决定
        await _recordUserDecision(pluginId, permission, true, reason);

        debugPrint('用户授权权限: 插件=$pluginId, 权限=${permission.id}');
        return result;
      } else {
        // 用户拒绝
        final result = _denyPluginPermission(
          pluginId,
          permission,
          '用户拒绝',
        );

        // 4. 记录用户决定
        await _recordUserDecision(pluginId, permission, false, reason);

        debugPrint('用户拒绝权限: 插件=$pluginId, 权限=${permission.id}');
        return result;
      }
    } on Exception catch (e) {
      debugPrint('用户权限询问失败: $e');
      return _denyPluginPermission(
        pluginId,
        permission,
        '权限询问过程中发生错误: $e',
      );
    }
  }

  /// 权限白名单
  final Set<PluginPermission> _permissionWhitelist = <PluginPermission>{};

  /// 权限缓存
  final Map<String, Map<PluginPermission, bool>> _permissionCache =
      <String, Map<PluginPermission, bool>>{};

  /// 加载用户自定义权限策略
  Future<void> _loadUserPermissionPolicies() async {
    try {
      // 实现用户自定义权限策略加载
      // 这里可以从本地存储、配置文件或远程服务器加载

      // 示例：从本地存储加载用户策略
      // final prefs = await SharedPreferences.getInstance();
      // final userPolicies = prefs.getString('user_permission_policies');

      debugPrint('用户权限策略加载完成');
    } on Exception catch (e) {
      debugPrint('加载用户权限策略失败: $e');
    }
  }

  /// 初始化权限白名单
  void _initializePermissionWhitelist() {
    // 添加基础权限到白名单（低风险权限）
    _permissionWhitelist.addAll(<PluginPermission>[
      PluginPermission.notifications,
      PluginPermission.clipboard,
      PluginPermission.deviceInfo,
    ]);

    debugPrint('权限白名单初始化完成，包含 ${_permissionWhitelist.length} 个权限');
  }

  /// 初始化权限缓存
  void _initializePermissionCache() {
    // 清理过期的权限缓存
    _permissionCache.clear();

    debugPrint('权限缓存初始化完成');
  }

  /// 从缓存获取权限
  bool? _getPermissionFromCache(String pluginId, PluginPermission permission) {
    final pluginCache = _permissionCache[pluginId];
    return pluginCache?[permission];
  }

  /// 更新权限缓存
  void _updatePermissionCache(
      String pluginId, PluginPermission permission, bool granted,) {
    _permissionCache.putIfAbsent(pluginId, () => <PluginPermission, bool>{});
    _permissionCache[pluginId]![permission] = granted;
  }

  /// 检查用户授权状态
  Future<void> _checkUserAuthorizationStatus(
    String pluginId,
    List<PluginPermission> permissions,
  ) async {
    // 实现用户授权状态检查
    // 这里可以检查用户之前的授权决定

    for (final permission in permissions) {
      final existingAuth = _pluginPermissions[pluginId]?[permission];
      if (existingAuth != null) {
        debugPrint(
            '插件 $pluginId 的权限 ${permission.id} 已有授权记录: ${existingAuth.granted}',);
      }
    }
  }

  /// 获取现有授权
  PermissionAuthorizationResult? _getExistingAuthorization(
    String pluginId,
    PluginPermission permission,
  ) => _pluginPermissions[pluginId]?[permission];

  /// 检查授权是否有效
  bool _isAuthorizationValid(PermissionAuthorizationResult auth) {
    // 检查授权是否过期（这里可以添加时间检查逻辑）
    if (auth.timestamp != null) {
      final now = DateTime.now();
      final authTime = auth.timestamp!;
      // 授权有效期为24小时
      if (now.difference(authTime).inHours > 24) {
        return false;
      }
    }
    return auth.granted;
  }

  /// 记录权限请求
  Future<void> _recordPermissionRequest(
    String pluginId,
    PluginPermission permission,
    String? reason,
  ) async {
    // 记录权限请求日志
    debugPrint('权限请求: 插件=$pluginId, 权限=${permission.id}, 原因=$reason');

    // 这里可以添加到审计日志或数据库
  }

  /// 根据策略处理权限
  Future<PermissionAuthorizationResult> _processPermissionByPolicy(
    String pluginId,
    PluginPermission permission,
    PermissionPolicy policy,
    String? reason,
  ) async {
    switch (policy) {
      case PermissionPolicy.allow:
        return _grantPluginPermission(pluginId, permission, reason);
      case PermissionPolicy.deny:
        return _denyPluginPermission(
          pluginId,
          permission,
          '权限策略拒绝',
        );
      case PermissionPolicy.ask:
        return _askUserPluginPermission(pluginId, permission, reason);
    }
  }

  /// 记录授权结果
  Future<void> _recordAuthorizationResult(
    String pluginId,
    PluginPermission permission,
    PermissionAuthorizationResult result,
  ) async {
    // 存储授权结果
    _pluginPermissions.putIfAbsent(
      pluginId,
      () => <PluginPermission, PermissionAuthorizationResult>{},
    );
    _pluginPermissions[pluginId]![permission] = result;

    debugPrint(
        '授权结果已记录: 插件=$pluginId, 权限=${permission.id}, 结果=${result.granted}',);
  }

  /// 通知权限变更
  Future<void> _notifyPermissionChange(
    String pluginId,
    PluginPermission permission,
    PermissionAuthorizationResult result,
  ) async {
    // 通知相关组件权限变更
    // 这里可以发送事件或调用回调函数
    debugPrint(
        '权限变更通知: 插件=$pluginId, 权限=${permission.id}, 授权=${result.granted}',);
  }

  /// 设置用户授权回调
  void setUserAuthorizationCallback(
    Future<bool> Function(
      String pluginId,
      PluginPermission permission,
      String? reason,
    )? callback,
  ) {
    _userAuthorizationCallback = callback;
    debugPrint('用户授权回调已设置');
  }

  /// 记录用户决定
  Future<void> _recordUserDecision(
    String pluginId,
    PluginPermission permission,
    bool granted,
    String? reason,
  ) async {
    // 记录用户的权限决定，用于后续分析和审计
    debugPrint(
      '用户决定记录: 插件=$pluginId, 权限=${permission.id}, '
      '决定=${granted ? "授权" : "拒绝"}, 原因=$reason',
    );

    // 这里可以添加到审计日志或数据库
    // 例如：await _auditLogger.logUserDecision(pluginId, permission, granted, reason);
  }

  /// 记录权限撤销操作
  Future<void> _recordPermissionRevocation(
    String pluginId,
    PluginPermission? permission,
  ) async {
    // 记录权限撤销操作，用于审计和分析
    if (permission == null) {
      debugPrint('权限撤销记录: 插件=$pluginId, 撤销=所有权限');
    } else {
      debugPrint('权限撤销记录: 插件=$pluginId, 撤销=${permission.id}');
    }

    // 这里可以添加到审计日志或数据库
    // 例如：await _auditLogger.logPermissionRevocation(pluginId, permission);
  }

  /// 验证权限策略是否有效
  bool _isValidPermissionPolicy(
    PluginPermission permission,
    PermissionPolicy policy,
  ) {
    // 检查策略是否合法
    switch (policy) {
      case PermissionPolicy.allow:
      case PermissionPolicy.deny:
      case PermissionPolicy.ask:
        break;
    }

    // 检查特殊权限的策略限制
    if (permission == PluginPermission.systemSettings &&
        policy == PermissionPolicy.allow) {
      // 系统设置权限不能设置为自动允许
      debugPrint('系统设置权限不能设置为自动允许');
      return false;
    }

    return true;
  }

  /// 持久化权限策略
  void _persistPermissionPolicy(
    PluginPermission permission,
    PermissionPolicy policy,
  ) {
    // 持久化策略设置到本地存储
    // 这里可以使用SharedPreferences或其他持久化方案
    debugPrint('持久化权限策略: 权限=${permission.id}, 策略=$policy');
  }

  /// 通知策略变更
  void _notifyPolicyChange(
    PluginPermission permission,
    PermissionPolicy? oldPolicy,
    PermissionPolicy newPolicy,
  ) {
    // 通知相关组件策略变更
    debugPrint(
      '策略变更通知: 权限=${permission.id}, '
      '旧策略=$oldPolicy, 新策略=$newPolicy',
    );
  }

  /// 为所有插件撤销指定权限
  void _revokePermissionForAllPlugins(PluginPermission permission) {
    final affectedPlugins = <String>[];

    // 遍历所有插件，撤销指定权限
    for (final entry in _pluginPermissions.entries) {
      final pluginId = entry.key;
      final permissions = entry.value;

      if (permissions.containsKey(permission)) {
        permissions.remove(permission);
        _updatePermissionCache(pluginId, permission, false);
        affectedPlugins.add(pluginId);
      }
    }

    debugPrint(
      '已为${affectedPlugins.length}个插件撤销权限: ${permission.id}',
    );
  }

  /// 根据策略获取权限列表
  List<PluginPermission> _getPermissionsByPolicy(PermissionPolicy policy) => _permissionPolicies.entries
        .where((MapEntry<PluginPermission, PermissionPolicy> entry) => entry.value == policy)
        .map((MapEntry<PluginPermission, PermissionPolicy> entry) => entry.key)
        .toList();

  /// 记录权限清理操作
  void _recordPermissionCleanup(
    String pluginId,
    List<PluginPermission> permissions,
  ) {
    // 记录权限清理操作，用于审计和分析
    debugPrint(
      '权限清理记录: 插件=$pluginId, 清理权限数=${permissions.length}',
    );

    // 这里可以添加到审计日志或数据库
    // 例如：await _auditLogger.logPermissionCleanup(pluginId, permissions);
  }
}
