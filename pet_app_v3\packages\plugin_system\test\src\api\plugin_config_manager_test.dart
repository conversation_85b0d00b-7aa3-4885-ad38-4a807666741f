/*
---------------------------------------------------------------
File name:          plugin_config_manager_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        PluginConfigManager 测试文件
---------------------------------------------------------------
*/

import 'dart:convert';
import 'dart:io';

import 'package:flutter_test/flutter_test.dart';
import 'package:path/path.dart' as path;
import 'package:plugin_system/src/api/plugin_api_interface.dart';
import 'package:plugin_system/src/api/plugin_config_manager.dart';
import 'package:plugin_system/src/core/plugin.dart';
import 'package:plugin_system/src/core/plugin_manifest.dart';
import 'package:plugin_system/src/core/plugin_registry.dart';

// 测试用的插件实现
class TestPlugin implements Plugin {
  TestPlugin(this.manifest);

  @override
  final PluginManifest manifest;

  @override
  String get id => manifest.id;

  @override
  String get name => manifest.name;

  @override
  String get version => manifest.version;

  @override
  String get description => manifest.description;

  @override
  String get author => manifest.author;

  @override
  PluginType get category => PluginType.tool;

  @override
  List<PluginPermission> get requiredPermissions => <PluginPermission>[];

  @override
  List<PluginDependency> get dependencies => <PluginDependency>[];

  @override
  List<SupportedPlatform> get supportedPlatforms => <SupportedPlatform>[];

  @override
  PluginState get currentState => PluginState.loaded;

  @override
  Stream<PluginState> get stateChanges => const Stream.empty();

  @override
  bool get isEnabled => false;

  @override
  Duration? get loadTime => null;

  @override
  Future<void> initialize() async {}

  @override
  Future<void> start() async {}

  @override
  Future<void> pause() async {}

  @override
  Future<void> resume() async {}

  @override
  Future<void> stop() async {}

  @override
  Future<void> dispose() async {}

  @override
  Object? getConfigWidget() => null;

  @override
  Object getMainWidget() => 'Test Plugin Widget';

  @override
  Future<dynamic> handleMessage(
      String action, Map<String, dynamic> data,) async => <String, Object>{'action': action, 'data': data};
}

void main() {
  group('PluginConfigManager Tests', () {
    late PluginConfigManager configManager;
    late PluginRegistry registry;
    late TestPlugin testPlugin;
    late Directory tempDir;

    setUp(() async {
      // 创建临时目录
      tempDir = await Directory.systemTemp.createTemp('plugin_config_test_');

      // 设置工作目录
      Directory.current = tempDir;

      registry = PluginRegistry.instance;
      configManager = PluginConfigManager(registry: registry);

      // 创建测试插件
      const manifest = PluginManifest(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        description: 'A test plugin',
        author: 'Test Author',
        category: 'tool',
        main: 'lib/main.dart',
        permissions: <String>['fileSystem', 'network'],
        platforms: <String>['android', 'ios'],
      );

      testPlugin = TestPlugin(manifest);
    });

    tearDown(() async {
      // 清理临时目录
      if (tempDir.existsSync()) {
        await tempDir.delete(recursive: true);
      }
    });

    group('Configuration Loading', () {
      test('should load plugin config from manifest', () async {
        final config = await configManager.getPluginConfig(testPlugin.id);

        expect(config.isSuccess, isTrue);
        expect(config.data, isA<Map<String, dynamic>>());
        expect(config.data!['config'], isA<Map<String, dynamic>>());

        final pluginConfig = config.data!['config'] as Map<String, dynamic>;
        expect(pluginConfig['permissions'], contains('fileSystem'));
        expect(pluginConfig['permissions'], contains('network'));
        expect(pluginConfig['platforms'], contains('android'));
        expect(pluginConfig['platforms'], contains('ios'));
      });

      test('should load config from user file when exists', () async {
        // 创建用户配置文件
        final userConfigDir =
            path.join(tempDir.path, 'config', 'plugins', 'user');
        await Directory(userConfigDir).create(recursive: true);

        final userConfigFile =
            File(path.join(userConfigDir, 'test_plugin.json'));
        final userConfig = <String, Object>{
          'enabled': true,
          'customSetting': 'user_value',
        };
        await userConfigFile.writeAsString(jsonEncode(userConfig));

        final config = await configManager.getPluginConfig(testPlugin.id);

        expect(config.isSuccess, isTrue);
        final pluginConfig = config.data!['config'] as Map<String, dynamic>;
        expect(pluginConfig['enabled'], isTrue);
        expect(pluginConfig['customSetting'], 'user_value');
      });

      test('should load config from environment variables', () async {
        // 设置环境变量
        Platform.environment['PLUGIN_TEST_PLUGIN_ENABLED'] = 'true';
        Platform.environment['PLUGIN_TEST_PLUGIN_LOGLEVEL'] = 'debug';
        Platform.environment['PLUGIN_TEST_PLUGIN_TIMEOUT'] = '60';

        final config = await configManager.getPluginConfig(testPlugin.id);

        expect(config.isSuccess, isTrue);
        final pluginConfig = config.data!['config'] as Map<String, dynamic>;
        expect(pluginConfig['enabled'], isTrue);
        expect(pluginConfig['loglevel'], 'debug');
        expect(pluginConfig['timeout'], 60);
      });
    });

    group('Configuration Update', () {
      test('should update plugin config successfully', () async {
        final newConfig = <String, Object>{
          'enabled': true,
          'logLevel': 'debug',
          'customSetting': 'new_value',
        };

        final result =
            await configManager.updatePluginConfig(testPlugin.id, newConfig);

        expect(result.isSuccess, isTrue);
        expect(result.data!['updated'], isTrue);
        expect(result.data!['appliedConfig'], equals(newConfig));
      });

      test('should save config to user file', () async {
        final newConfig = <String, Object>{
          'enabled': true,
          'logLevel': 'debug',
        };

        await configManager.updatePluginConfig(testPlugin.id, newConfig);

        // 验证文件是否创建
        final userConfigFile = File(path.join(
            tempDir.path, 'config', 'plugins', 'user', 'test_plugin.json',),);

        expect(userConfigFile.existsSync(), isTrue);

        final savedConfig = jsonDecode(await userConfigFile.readAsString());
        expect(savedConfig['enabled'], isTrue);
        expect(savedConfig['logLevel'], 'debug');
      });
    });

    group('System Configuration', () {
      test('should get system config', () async {
        final config = await configManager.getSystemConfig();

        expect(config.isSuccess, isTrue);
        expect(config.data, isA<Map<String, dynamic>>());
        expect(config.data!['config'], isA<Map<String, dynamic>>());

        final systemConfig = config.data!['config'] as Map<String, dynamic>;
        expect(systemConfig['maxPlugins'], isA<int>());
        expect(systemConfig['autoStart'], isA<bool>());
        expect(systemConfig['logLevel'], isA<String>());
      });

      test('should update system config', () async {
        final newConfig = <String, Object>{
          'maxPlugins': 50,
          'autoStart': false,
          'logLevel': 'debug',
        };

        final result = await configManager.updateSystemConfig(newConfig);

        expect(result.isSuccess, isTrue);
        expect(result.data!['updated'], isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle non-existent plugin gracefully', () async {
        final config =
            await configManager.getPluginConfig('non_existent_plugin');

        expect(config.isSuccess, isFalse);
        expect(config.hasErrors, isTrue);
      });

      test('should handle invalid config data', () async {
        final invalidConfig = <String, void>{
          'invalid_key': null,
        };

        final result = await configManager.updatePluginConfig(
            testPlugin.id, invalidConfig,);

        // 应该仍然成功，因为我们的实现比较宽松
        expect(result.isSuccess, isTrue);
      });
    });
  });
}
