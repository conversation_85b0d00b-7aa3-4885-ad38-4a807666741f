/*
---------------------------------------------------------------
File name:          creative_workshop_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop适应层集成测试
---------------------------------------------------------------
Change History:
    2025-07-29: Creative Workshop适应层集成测试实现;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:creative_workshop/src/integration/creative_workshop_adapter.dart';

void main() {
  group('CreativeWorkshopAdapter Tests', () {
    late CreativeWorkshopAdapter adapter;

    setUp(() {
      adapter = CreativeWorkshopAdapter.instance;
    });

    test('应该能够获取适应层实例', () {
      expect(adapter, isNotNull);
      expect(adapter, isA<CreativeWorkshopAdapter>());
    });

    test('应该能够处理插件数据转换', () async {
      final pluginData = {
        'id': 'test_plugin',
        'name': 'Test Plugin',
        'version': '1.0.0',
        'description': 'Test plugin for workshop',
      };

      // 测试工具插件创建
      expect(() => adapter.onPluginAdded(pluginData, 'tool'), returnsNormally);

      // 测试游戏插件创建
      expect(() => adapter.onPluginAdded(pluginData, 'game'), returnsNormally);
    });

    test('应该能够处理插件移除', () async {
      expect(() => adapter.onPluginRemoved('test_plugin'), returnsNormally);
    });

    test('应该能够处理插件更新', () async {
      final pluginData = {
        'id': 'test_plugin',
        'name': 'Updated Test Plugin',
        'version': '1.1.0',
      };

      expect(() => adapter.onPluginUpdated(pluginData), returnsNormally);
    });
  });
}

/// Mock模块协调器
class MockModuleCoordinator implements IModuleCoordinator {
  @override
  IModuleCommunication get communication => MockModuleCommunication();

  @override
  Future<void> registerModule(IModuleAdapter adapter) async {
    // Mock implementation
  }

  @override
  Future<void> unregisterModule(String moduleId) async {
    // Mock implementation
  }

  @override
  IModuleAdapter? getModule(String moduleId) => null;

  @override
  List<IModuleAdapter> getAllModules() => <IModuleAdapter>[];

  @override
  Future<void> startAllModules() async {
    // Mock implementation
  }

  @override
  Future<void> stopAllModules() async {
    // Mock implementation
  }

  @override
  Future<Map<String, dynamic>> getSystemStatus() async {
    return <String, dynamic>{'status': 'running', 'modules': 2};
  }
}

/// Mock模块通信
class MockModuleCommunication implements IModuleCommunication {
  @override
  Future<Map<String, dynamic>?> sendRequest(
    String targetModule,
    String action,
    Map<String, dynamic> data, {
    int timeoutMs = 5000,
  }) async {
    return <String, dynamic>{'success': true, 'data': 'mock response'};
  }

  @override
  Future<void> sendNotification(
    String targetModule,
    String event,
    Map<String, dynamic> data,
  ) async {
    // Mock implementation
  }

  @override
  Future<void> broadcastEvent(
    String event,
    Map<String, dynamic> data, {
    List<String> excludeModules = const [],
  }) async {
    // Mock implementation
  }

  @override
  void subscribeToEvent(String event, EventHandler handler) {
    // Mock implementation
  }

  @override
  void unsubscribeFromEvent(String event, [EventHandler? handler]) {
    // Mock implementation
  }
}
