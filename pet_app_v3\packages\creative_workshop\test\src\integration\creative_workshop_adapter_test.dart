/*
---------------------------------------------------------------
File name:          creative_workshop_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Creative Workshop适应层集成测试
---------------------------------------------------------------
Change History:
    2025-07-29: Creative Workshop适应层集成测试实现;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:creative_workshop/src/integration/creative_workshop_adapter.dart';

void main() {
  group('CreativeWorkshopAdapter Tests', () {
    late CreativeWorkshopAdapter adapter;

    setUp(() {
      adapter = CreativeWorkshopAdapter.instance;
    });

    test('应该能够获取适应层实例', () {
      expect(adapter, isNotNull);
      expect(adapter, isA<CreativeWorkshopAdapter>());
    });

    test('应该能够处理插件数据转换', () async {
      final pluginData = {
        'id': 'test_plugin',
        'name': 'Test Plugin',
        'version': '1.0.0',
        'description': 'Test plugin for workshop',
      };

      expect(pluginData['id'], equals('test_plugin'));
      expect(pluginData['name'], equals('Test Plugin'));
      expect(pluginData['version'], equals('1.0.0'));
    });

    test('应该能够提供健康状态', () async {
      final healthStatus = await adapter.getHealthStatus();

      expect(healthStatus, isA<Map<String, dynamic>>());
      expect(healthStatus['status'], isNotNull);
    });
  });
}
