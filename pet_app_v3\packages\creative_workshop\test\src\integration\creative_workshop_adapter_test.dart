import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/integration/creative_workshop_adapter.dart';

void main() {
  group('CreativeWorkshopAdapter Tests', () {
    late CreativeWorkshopAdapter adapter;

    setUp(() {
      adapter = CreativeWorkshopAdapter();
    });

    tearDown(() async {
      await adapter.dispose();
    });

    group('初始化和配置', () {
      test('应该成功初始化适应层', () async {
        await adapter.initialize();
        expect(adapter.isInitialized, isTrue);
      });

      test('应该处理配置变更', () async {
        await adapter.initialize();
        
        final config = {
          'enableWorkshopSync': true,
          'maxProjects': 50,
          'autoSaveInterval': 60000,
        };
        
        await adapter.configure(config);
        expect(adapter.isInitialized, isTrue);
      });
    });

    group('插件同步功能', () {
      test('应该处理插件添加事件', () async {
        await adapter.initialize();
        
        final pluginData = {
          'id': 'test_plugin',
          'name': 'Test Plugin',
          'version': '1.0.0',
          'type': 'tool',
          'description': 'Test plugin for workshop',
        };
        
        await adapter.onPluginAdded(pluginData, 'tool');
        
        // 验证插件已添加到工坊
        expect(adapter.isInitialized, isTrue);
      });

      test('应该处理插件移除事件', () async {
        await adapter.initialize();
        
        await adapter.onPluginRemoved('test_plugin');
        
        // 验证插件已从工坊移除
        expect(adapter.isInitialized, isTrue);
      });

      test('应该处理插件更新事件', () async {
        await adapter.initialize();
        
        final pluginData = {
          'id': 'test_plugin',
          'name': 'Updated Test Plugin',
          'version': '1.1.0',
        };
        
        await adapter.onPluginUpdated(pluginData);
        
        // 验证插件已更新
        expect(adapter.isInitialized, isTrue);
      });
    });

    group('工坊插件同步', () {
      test('应该同步工坊插件到Plugin System', () async {
        await adapter.initialize();
        
        // 模拟工坊插件
        final workshopPlugin = MockWorkshopPlugin(
          id: 'workshop_plugin',
          name: 'Workshop Plugin',
          version: '1.0.0',
        );
        
        await adapter.onWorkshopPluginAdded(workshopPlugin, 'game');
        
        // 验证插件已同步
        expect(adapter.isInitialized, isTrue);
      });
    });

    group('请求处理', () {
      test('应该处理获取工坊状态请求', () async {
        await adapter.initialize();
        
        final result = await adapter.handleRequest('get_workshop_status', {});
        
        expect(result, isA<Map<String, dynamic>>());
        expect(result['success'], isTrue);
        expect(result['data'], isA<Map<String, dynamic>>());
      });

      test('应该处理同步插件请求', () async {
        await adapter.initialize();
        
        final result = await adapter.handleRequest('sync_plugins', {
          'direction': 'to_workshop',
          'pluginIds': ['plugin1', 'plugin2'],
        });
        
        expect(result, isA<Map<String, dynamic>>());
        expect(result['success'], isA<bool>());
      });

      test('应该处理未知请求类型', () async {
        await adapter.initialize();
        
        final result = await adapter.handleRequest('unknown_action', {});
        
        expect(result['success'], isFalse);
        expect(result['error'], contains('Unknown action'));
      });
    });

    group('错误处理', () {
      test('应该处理插件同步失败', () async {
        await adapter.initialize();
        
        // 传入无效的插件数据
        await adapter.onPluginAdded({}, 'invalid_category');
        
        // 应该不抛出异常
        expect(adapter.isInitialized, isTrue);
      });

      test('应该处理工坊管理器访问失败', () async {
        await adapter.initialize();
        
        // 模拟工坊管理器不可用的情况
        await adapter.onPluginRemoved('non_existent_plugin');
        
        // 应该优雅处理错误
        expect(adapter.isInitialized, isTrue);
      });
    });

    group('生命周期管理', () {
      test('应该正确处理启动和停止', () async {
        await adapter.initialize();
        await adapter.start();
        expect(adapter.isRunning, isTrue);
        
        await adapter.stop();
        expect(adapter.isRunning, isFalse);
      });

      test('应该正确清理资源', () async {
        await adapter.initialize();
        await adapter.start();
        
        await adapter.dispose();
        expect(adapter.isInitialized, isFalse);
        expect(adapter.isRunning, isFalse);
      });
    });
  });
}

/// 模拟工坊插件类
class MockWorkshopPlugin {
  final String id;
  final String name;
  final String version;
  final String description;
  final String author;

  MockWorkshopPlugin({
    required this.id,
    required this.name,
    required this.version,
    this.description = 'Mock workshop plugin',
    this.author = 'Test Author',
  });
}
