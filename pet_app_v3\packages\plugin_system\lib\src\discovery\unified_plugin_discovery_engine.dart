/*
---------------------------------------------------------------
File name:          unified_plugin_discovery_engine.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        统一插件发现引擎 - 多源插件发现机制
---------------------------------------------------------------
Change History:
    2025-07-29: P0.1.1 - 创建统一插件发现引擎，实现多源插件发现机制;
---------------------------------------------------------------
*/

import 'dart:io';
import 'dart:convert';
import 'package:path/path.dart' as path;
import 'package:yaml/yaml.dart';

import '../core/plugin_manifest.dart';
import '../core/plugin_manifest_parser.dart';
import '../core/plugin_file_manager.dart';
import '../core/plugin_registry.dart';

/// 插件发现来源
enum PluginSource {
  /// Ming CLI 生成的插件
  mingCli('ming_cli'),

  /// 系统插件目录
  system('system'),

  /// 项目 plugins/ 目录
  project('project'),

  /// 插件注册表
  registry('registry');

  const PluginSource(this.name);
  final String name;
}

/// 插件发现状态
enum PluginDiscoveryStatus {
  /// 已发现
  discovered('discovered'),

  /// 已验证
  validated('validated'),

  /// 发现错误
  error('error'),

  /// 已忽略
  ignored('ignored');

  const PluginDiscoveryStatus(this.name);
  final String name;
}

/// 插件发现结果
class PluginDiscoveryResult {
  const PluginDiscoveryResult({
    required this.source,
    required this.path,
    required this.manifest,
    required this.status,
    this.error,
    this.metadata,
  });

  final PluginSource source;
  final String path;
  final PluginManifest manifest;
  final PluginDiscoveryStatus status;
  final String? error;
  final Map<String, dynamic>? metadata;

  @override
  String toString() {
    return 'PluginDiscoveryResult(source: ${source.name}, '
        'id: ${manifest.id}, status: ${status.name}, path: $path)';
  }
}

/// 统一插件发现引擎
///
/// 负责从多个来源发现插件：
/// 1. Ming CLI 生成的插件 (plugin.yaml)
/// 2. 系统插件目录 (Plugin System)
/// 3. 项目 plugins/ 目录
/// 4. 插件注册表 (plugin_registry.json)
class UnifiedPluginDiscoveryEngine {
  UnifiedPluginDiscoveryEngine._();
  static final UnifiedPluginDiscoveryEngine _instance =
      UnifiedPluginDiscoveryEngine._();

  /// 获取发现引擎单例实例
  static UnifiedPluginDiscoveryEngine get instance => _instance;

  final PluginManifestParser _manifestParser = PluginManifestParser.instance;
  final PluginFileManager _fileManager = PluginFileManager.instance;
  final PluginRegistry _registry = PluginRegistry.instance;

  /// 发现所有插件
  Future<List<PluginDiscoveryResult>> discoverAllPlugins() async {
    final results = <PluginDiscoveryResult>[];

    try {
      // 1. 扫描 plugins/ 目录 (项目插件)
      final projectPlugins = await _scanProjectPluginsDirectory();
      results.addAll(projectPlugins);

      // 2. 扫描系统插件目录 (Plugin System)
      final systemPlugins = await _scanSystemPluginsDirectory();
      results.addAll(systemPlugins);

      // 3. 扫描 Ming CLI 生成的插件
      final mingCliPlugins = await _scanMingCliGeneratedPlugins();
      results.addAll(mingCliPlugins);

      // 4. 解析 plugin_registry.json
      final registryPlugins = await _parsePluginRegistry();
      results.addAll(registryPlugins);

      // 5. 去重和验证
      return _deduplicateAndValidate(results);
    } catch (e) {
      print('插件发现过程中发生错误: $e');
      return results;
    }
  }

  /// 扫描项目 plugins/ 目录
  Future<List<PluginDiscoveryResult>> _scanProjectPluginsDirectory() async {
    final results = <PluginDiscoveryResult>[];

    try {
      // 获取项目根目录下的 plugins/ 目录
      final projectRoot = _findProjectRoot();
      if (projectRoot == null) {
        return results;
      }

      final pluginsDir = Directory(path.join(projectRoot, 'plugins'));
      if (!await pluginsDir.exists()) {
        return results;
      }

      // 递归扫描所有子目录
      await for (final entity in pluginsDir.list(recursive: true)) {
        if (entity is File && path.basename(entity.path) == 'plugin.yaml') {
          final result = await _processPluginManifestFile(
              entity.path, PluginSource.project);
          if (result != null) {
            results.add(result);
          }
        }
      }
    } catch (e) {
      print('扫描项目插件目录失败: $e');
    }

    return results;
  }

  /// 扫描系统插件目录
  Future<List<PluginDiscoveryResult>> _scanSystemPluginsDirectory() async {
    final results = <PluginDiscoveryResult>[];

    try {
      // 确保文件管理器已初始化
      await _fileManager.initialize();

      // 获取系统插件目录
      final systemPluginsDir = Directory(await _getSystemPluginsDirectory());
      if (!await systemPluginsDir.exists()) {
        return results;
      }

      // 扫描已安装的插件
      final installedPluginIds = await _fileManager.getInstalledPluginIds();

      for (final pluginId in installedPluginIds) {
        try {
          final manifestResult =
              await _manifestParser.parseFromPlugin(pluginId);
          if (manifestResult.success && manifestResult.manifest != null) {
            final pluginPath = path.join(systemPluginsDir.path, pluginId);
            results.add(PluginDiscoveryResult(
              source: PluginSource.system,
              path: pluginPath,
              manifest: manifestResult.manifest!,
              status: PluginDiscoveryStatus.validated,
            ));
          }
        } catch (e) {
          print('处理系统插件 $pluginId 失败: $e');
        }
      }
    } catch (e) {
      print('扫描系统插件目录失败: $e');
    }

    return results;
  }

  /// 扫描 Ming CLI 生成的插件
  Future<List<PluginDiscoveryResult>> _scanMingCliGeneratedPlugins() async {
    final results = <PluginDiscoveryResult>[];

    try {
      // 1. 扫描当前工作目录和子目录中的 plugin.yaml 文件
      final currentDir = Directory.current;
      final pluginFiles = await _findPluginYamlFiles(currentDir);

      for (final file in pluginFiles) {
        final result =
            await _processPluginManifestFile(file.path, PluginSource.mingCli);
        if (result != null && _isMingCliGenerated(result.manifest)) {
          // 添加 Ming CLI 特有的元数据
          final enhancedResult = PluginDiscoveryResult(
            source: result.source,
            path: result.path,
            manifest: result.manifest,
            status: result.status,
            error: result.error,
            metadata: {
              'detected_by': 'ming_cli_scanner',
              'scan_time': DateTime.now().toIso8601String(),
              'confidence': _calculateMingCliConfidence(result.manifest),
              'template_features': _extractTemplateFeatures(result.manifest),
            },
          );
          results.add(enhancedResult);
        }
      }

      // 2. 扫描常见的 Ming CLI 输出目录
      final commonMingCliDirs = await _findCommonMingCliDirectories();
      for (final dir in commonMingCliDirs) {
        final dirResults = await _scanDirectoryForMingCliPlugins(dir);
        results.addAll(dirResults);
      }
    } catch (e) {
      print('扫描 Ming CLI 插件失败: $e');
    }

    return results;
  }

  /// 查找常见的 Ming CLI 输出目录
  Future<List<Directory>> _findCommonMingCliDirectories() async {
    final directories = <Directory>[];
    final currentDir = Directory.current;

    // 常见的 Ming CLI 输出目录模式
    final patterns = [
      'plugins',
      'generated',
      'output',
      'build',
      'dist',
    ];

    for (final pattern in patterns) {
      final dir = Directory(path.join(currentDir.path, pattern));
      if (await dir.exists()) {
        directories.add(dir);
      }
    }

    return directories;
  }

  /// 扫描目录中的 Ming CLI 插件
  Future<List<PluginDiscoveryResult>> _scanDirectoryForMingCliPlugins(
      Directory directory) async {
    final results = <PluginDiscoveryResult>[];

    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File && path.basename(entity.path) == 'plugin.yaml') {
          final result = await _processPluginManifestFile(
              entity.path, PluginSource.mingCli);
          if (result != null && _isMingCliGenerated(result.manifest)) {
            results.add(result);
          }
        }
      }
    } catch (e) {
      print('扫描目录 ${directory.path} 失败: $e');
    }

    return results;
  }

  /// 计算 Ming CLI 置信度
  double _calculateMingCliConfidence(PluginManifest manifest) {
    double confidence = 0.0;

    // 作者匹配 (30%)
    if (manifest.author.contains('lgnorant-lu')) {
      confidence += 0.3;
    } else if (manifest.author.contains('ming-cli') ||
        manifest.author.contains('Ming CLI')) {
      confidence += 0.25;
    }

    // 描述匹配 (25%)
    if (manifest.description
        .contains('A Pet App plugin created with Ming CLI')) {
      confidence += 0.25;
    } else if (manifest.description.contains('Ming CLI')) {
      confidence += 0.15;
    }

    // 关键词匹配 (20%)
    final mingCliKeywords = ['pet_app', 'creative_workshop', 'ming-cli'];
    final matchedKeywords = manifest.keywords
        .where((keyword) => mingCliKeywords.contains(keyword))
        .length;
    confidence += (matchedKeywords / mingCliKeywords.length) * 0.2;

    // 版本格式匹配 (10%)
    if (manifest.version == '1.0.0') {
      confidence += 0.1;
    }

    // 文件结构匹配 (15%)
    if (manifest.main.startsWith('lib/') && manifest.main.contains('_')) {
      confidence += 0.15;
    }

    return confidence.clamp(0.0, 1.0);
  }

  /// 提取模板特征
  Map<String, dynamic> _extractTemplateFeatures(PluginManifest manifest) {
    return {
      'plugin_type': manifest.category,
      'has_snake_case_id': manifest.id.contains('_'),
      'has_standard_version': manifest.version == '1.0.0',
      'has_standard_main': manifest.main.startsWith('lib/'),
      'keyword_count': manifest.keywords.length,
      'has_pet_app_keywords': manifest.keywords
          .any((k) => k.contains('pet') || k.contains('creative')),
    };
  }

  /// 解析插件注册表
  Future<List<PluginDiscoveryResult>> _parsePluginRegistry() async {
    final results = <PluginDiscoveryResult>[];

    try {
      final projectRoot = _findProjectRoot();
      if (projectRoot == null) {
        return results;
      }

      final registryFile =
          File(path.join(projectRoot, 'plugins', 'plugin_registry.json'));
      if (!await registryFile.exists()) {
        return results;
      }

      final registryContent = await registryFile.readAsString();
      final registryData = jsonDecode(registryContent) as Map<String, dynamic>;

      // 解析新的注册表格式
      if (registryData.containsKey('plugins')) {
        final pluginsData = registryData['plugins'] as Map<String, dynamic>;

        // 遍历所有分类 (examples, development, testing, production, archived)
        for (final categoryEntry in pluginsData.entries) {
          final category = categoryEntry.key;
          final categoryPlugins = categoryEntry.value as Map<String, dynamic>;

          for (final pluginEntry in categoryPlugins.entries) {
            final pluginId = pluginEntry.key;
            final pluginInfo = pluginEntry.value as Map<String, dynamic>;

            try {
              final result = await _processRegistryPlugin(
                  projectRoot, pluginId, pluginInfo, category);
              if (result != null) {
                results.add(result);
              }
            } catch (e) {
              print('处理注册表插件 $pluginId 失败: $e');
            }
          }
        }
      } else {
        // 兼容旧格式
        for (final entry in registryData.entries) {
          final pluginId = entry.key;
          final pluginInfo = entry.value as Map<String, dynamic>;

          try {
            final result = await _processRegistryPlugin(
                projectRoot, pluginId, pluginInfo, 'unknown');
            if (result != null) {
              results.add(result);
            }
          } catch (e) {
            print('处理注册表插件 $pluginId 失败: $e');
          }
        }
      }
    } catch (e) {
      print('解析插件注册表失败: $e');
    }

    return results;
  }

  /// 处理注册表中的单个插件
  Future<PluginDiscoveryResult?> _processRegistryPlugin(
    String projectRoot,
    String pluginId,
    Map<String, dynamic> pluginInfo,
    String category,
  ) async {
    try {
      // 获取插件路径
      final pluginPath = pluginInfo['path'] as String?;
      if (pluginPath == null) {
        print('插件 $pluginId 缺少路径信息');
        return null;
      }

      final fullPluginPath = path.join(projectRoot, 'plugins', pluginPath);
      final manifestFile = File(path.join(fullPluginPath, 'plugin.yaml'));

      // 检查插件文件是否存在
      if (await manifestFile.exists()) {
        // 从实际的 plugin.yaml 文件解析
        final result = await _processPluginManifestFile(
            manifestFile.path, PluginSource.registry);

        if (result != null) {
          // 添加注册表特有的元数据
          return PluginDiscoveryResult(
            source: result.source,
            path: result.path,
            manifest: result.manifest,
            status: result.status,
            error: result.error,
            metadata: {
              'registry_category': category,
              'registry_status': pluginInfo['status'] ?? 'unknown',
              'registry_type': pluginInfo['type'] ?? 'unknown',
              'install_count': pluginInfo['install_count'] ?? 0,
              'rating': pluginInfo['rating'] ?? 0.0,
              'tags': pluginInfo['tags'] ?? [],
              'created_at': pluginInfo['created_at'],
              'updated_at': pluginInfo['updated_at'],
              'supported_platforms': pluginInfo['supported_platforms'] ?? [],
              'required_permissions': pluginInfo['required_permissions'] ?? [],
              'dependencies': pluginInfo['dependencies'] ?? [],
            },
          );
        }
      } else {
        // 插件文件不存在，创建错误结果
        print('注册表插件 $pluginId 的文件不存在: $fullPluginPath');
        return PluginDiscoveryResult(
          source: PluginSource.registry,
          path: fullPluginPath,
          manifest: _createRegistryManifest(pluginId, pluginInfo),
          status: PluginDiscoveryStatus.error,
          error: '插件文件不存在',
          metadata: {
            'registry_category': category,
            'registry_status': 'missing',
            'file_missing': true,
          },
        );
      }
    } catch (e) {
      print('处理注册表插件 $pluginId 时发生错误: $e');
      return null;
    }

    return null;
  }

  /// 从注册表信息创建插件清单
  PluginManifest _createRegistryManifest(
    String pluginId,
    Map<String, dynamic> pluginInfo,
  ) {
    return PluginManifest(
      id: pluginInfo['id'] as String? ?? pluginId,
      name: pluginInfo['name'] as String? ?? pluginId,
      version: pluginInfo['version'] as String? ?? '1.0.0',
      description: pluginInfo['description'] as String? ?? 'Registry plugin',
      author: pluginInfo['author'] as String? ?? 'Unknown',
      category: pluginInfo['category'] as String? ?? 'unknown',
      main: pluginInfo['entry_point'] as String? ?? 'lib/main.dart',
      keywords: (pluginInfo['tags'] as List<dynamic>?)
              ?.map((tag) => tag.toString())
              .toList() ??
          [],
    );
  }

  /// 处理插件清单文件
  Future<PluginDiscoveryResult?> _processPluginManifestFile(
      String manifestPath, PluginSource source) async {
    try {
      final manifestFile = File(manifestPath);
      final manifestBytes = await manifestFile.readAsBytes();

      final parseResult = await _manifestParser.parseFromBytes(manifestBytes);
      if (!parseResult.success || parseResult.manifest == null) {
        return PluginDiscoveryResult(
          source: source,
          path: path.dirname(manifestPath),
          manifest: _createEmptyManifest(),
          status: PluginDiscoveryStatus.error,
          error: parseResult.error,
        );
      }

      return PluginDiscoveryResult(
        source: source,
        path: path.dirname(manifestPath),
        manifest: parseResult.manifest!,
        status: PluginDiscoveryStatus.discovered,
      );
    } catch (e) {
      return PluginDiscoveryResult(
        source: source,
        path: path.dirname(manifestPath),
        manifest: _createEmptyManifest(),
        status: PluginDiscoveryStatus.error,
        error: e.toString(),
      );
    }
  }

  /// 查找项目根目录
  String? _findProjectRoot() {
    Directory current = Directory.current;

    while (current.path != current.parent.path) {
      // 检查是否存在 pubspec.yaml 或 plugins/ 目录
      final pubspecFile = File(path.join(current.path, 'pubspec.yaml'));
      final pluginsDir = Directory(path.join(current.path, 'plugins'));

      if (pubspecFile.existsSync() || pluginsDir.existsSync()) {
        return current.path;
      }

      current = current.parent;
    }

    return null;
  }

  /// 查找 plugin.yaml 文件
  Future<List<File>> _findPluginYamlFiles(Directory directory) async {
    final files = <File>[];

    try {
      await for (final entity in directory.list(recursive: true)) {
        if (entity is File && path.basename(entity.path) == 'plugin.yaml') {
          files.add(entity);
        }
      }
    } catch (e) {
      print('查找 plugin.yaml 文件失败: $e');
    }

    return files;
  }

  /// 判断是否为 Ming CLI 生成的插件
  bool _isMingCliGenerated(PluginManifest manifest) {
    // 检查是否包含 Ming CLI 特有的标记

    // 1. 检查作者是否包含 Ming CLI 标记
    if (manifest.author.contains('ming-cli') ||
        manifest.author.contains('Ming CLI') ||
        manifest.author.contains('lgnorant-lu')) {
      return true;
    }

    // 2. 检查描述是否包含模板标记
    if (manifest.description.contains('Generated by Ming CLI') ||
        manifest.description.contains('Ming CLI Template') ||
        manifest.description
            .contains('A Pet App plugin created with Ming CLI') ||
        manifest.description.contains('created with Ming CLI')) {
      return true;
    }

    // 3. 检查关键词是否包含 Ming CLI 标记
    if (manifest.keywords.any((keyword) =>
        keyword.contains('ming-cli') ||
        keyword.contains('pet-app') ||
        keyword.contains('creative-workshop'))) {
      return true;
    }

    // 4. 检查是否包含 Ming CLI 模板特有的标签
    // 基于 plugin.yaml 模板中的标签
    final mingCliTags = ['pet_app', 'creative_workshop'];
    if (manifest.keywords.any((keyword) => mingCliTags.contains(keyword))) {
      return true;
    }

    // 5. 检查插件ID格式 (Ming CLI 通常使用 snake_case)
    if (manifest.id.contains('_') &&
        !manifest.id.contains('-') &&
        manifest.id.toLowerCase() == manifest.id) {
      // 进一步检查是否有其他 Ming CLI 特征
      if (_hasAdditionalMingCliFeatures(manifest)) {
        return true;
      }
    }

    return false;
  }

  /// 检查是否有额外的 Ming CLI 特征
  bool _hasAdditionalMingCliFeatures(PluginManifest manifest) {
    // 检查版本格式 (Ming CLI 默认使用 1.0.0)
    if (manifest.version == '1.0.0') {
      return true;
    }

    // 检查主入口文件格式 (Ming CLI 使用 snake_case)
    if (manifest.main.contains('lib/') &&
        manifest.main.endsWith('.dart') &&
        manifest.main.contains('_')) {
      return true;
    }

    // 检查分类是否为 Ming CLI 支持的类型
    final mingCliCategories = ['tool', 'game', 'theme', 'service', 'widget'];
    if (mingCliCategories.contains(manifest.category.toLowerCase())) {
      return true;
    }

    return false;
  }

  /// 去重和验证插件
  List<PluginDiscoveryResult> _deduplicateAndValidate(
      List<PluginDiscoveryResult> results) {
    final Map<String, PluginDiscoveryResult> uniquePlugins = {};

    for (final result in results) {
      final pluginId = result.manifest.id;

      // 如果已存在相同ID的插件，选择优先级更高的来源
      if (uniquePlugins.containsKey(pluginId)) {
        final existing = uniquePlugins[pluginId]!;
        if (_getSourcePriority(result.source) >
            _getSourcePriority(existing.source)) {
          uniquePlugins[pluginId] = result;
        }
      } else {
        uniquePlugins[pluginId] = result;
      }
    }

    return uniquePlugins.values.toList();
  }

  /// 获取插件来源的优先级
  int _getSourcePriority(PluginSource source) {
    switch (source) {
      case PluginSource.system:
        return 4; // 最高优先级：已安装的系统插件
      case PluginSource.registry:
        return 3; // 注册表中的插件
      case PluginSource.project:
        return 2; // 项目目录中的插件
      case PluginSource.mingCli:
        return 1; // 最低优先级：Ming CLI 生成的插件
    }
  }

  /// 验证插件发现结果
  Future<PluginDiscoveryResult> validateDiscoveryResult(
      PluginDiscoveryResult result) async {
    try {
      // 1. 验证插件路径存在
      final pluginDir = Directory(result.path);
      if (!await pluginDir.exists()) {
        return PluginDiscoveryResult(
          source: result.source,
          path: result.path,
          manifest: result.manifest,
          status: PluginDiscoveryStatus.error,
          error: '插件目录不存在: ${result.path}',
        );
      }

      // 2. 验证主入口文件存在
      final mainFile = File(path.join(result.path, result.manifest.main));
      if (!await mainFile.exists()) {
        return PluginDiscoveryResult(
          source: result.source,
          path: result.path,
          manifest: result.manifest,
          status: PluginDiscoveryStatus.error,
          error: '主入口文件不存在: ${result.manifest.main}',
        );
      }

      // 3. 验证依赖关系
      // TODO: 实现依赖验证逻辑

      return PluginDiscoveryResult(
        source: result.source,
        path: result.path,
        manifest: result.manifest,
        status: PluginDiscoveryStatus.validated,
      );
    } catch (e) {
      return PluginDiscoveryResult(
        source: result.source,
        path: result.path,
        manifest: result.manifest,
        status: PluginDiscoveryStatus.error,
        error: '验证失败: $e',
      );
    }
  }

  /// 获取发现统计信息
  Map<String, int> getDiscoveryStatistics(List<PluginDiscoveryResult> results) {
    final stats = <String, int>{
      'total': results.length,
      'discovered': 0,
      'validated': 0,
      'error': 0,
      'ignored': 0,
    };

    for (final source in PluginSource.values) {
      stats[source.name] = 0;
    }

    for (final result in results) {
      stats[result.status.name] = (stats[result.status.name] ?? 0) + 1;
      stats[result.source.name] = (stats[result.source.name] ?? 0) + 1;
    }

    return stats;
  }

  /// 创建空的插件清单
  PluginManifest _createEmptyManifest() {
    return const PluginManifest(
      id: 'unknown',
      name: 'Unknown Plugin',
      version: '0.0.0',
      description: 'Unknown plugin',
      author: 'Unknown',
      category: 'unknown',
      main: 'lib/main.dart',
    );
  }

  /// 获取系统插件目录
  Future<String> _getSystemPluginsDirectory() async {
    try {
      // 尝试从文件管理器获取目录
      final homeDir = Platform.environment['HOME'] ??
          Platform.environment['USERPROFILE'] ??
          '.';
      return path.join(homeDir, '.plugin_system', 'plugins');
    } catch (e) {
      return path.join('.', '.plugin_system', 'plugins');
    }
  }
}
