/*
---------------------------------------------------------------
File name:          workshop_plugin_adapter_test.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        WorkshopPluginAdapter 测试文件
---------------------------------------------------------------
*/

import 'dart:async';

import 'package:creative_workshop/src/core/plugins/plugin_manager.dart';
import 'package:creative_workshop/src/core/plugins/workshop_plugin_adapter.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

void main() {
  group('WorkshopPluginAdapter Tests', () {
    late WorkshopPluginAdapter adapter;
    late plugin_sys.PluginManifest manifest;
    late PluginInstallInfo workshopInfo;

    setUp(() {
      // 创建测试用的插件清单
      manifest = const plugin_sys.PluginManifest(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        description: 'A test plugin',
        author: 'Test Author',
        category: 'tool',
        main: 'lib/main.dart',
        permissions: <String>['filesystem', 'network'],
        platforms: <String>['android', 'ios'],
        dependencies: <plugin_sys.PluginManifestDependency>[],
        keywords: <String>['test', 'plugin'],
      );

      // 创建测试用的Workshop插件信息
      workshopInfo = PluginInstallInfo(
        id: 'test_plugin',
        name: 'Test Plugin',
        version: '1.0.0',
        state: PluginState.installed,
        installedAt: DateTime.now(),
        size: 1024,
        autoUpdate: true,
      );

      adapter = WorkshopPluginAdapter(
        manifest: manifest,
        workshopInfo: workshopInfo,
      );
    });

    tearDown(() async {
      await adapter.dispose();
    });

    group('Basic Properties', () {
      test('should return correct id', () {
        expect(adapter.id, 'test_plugin');
      });

      test('should return correct name', () {
        expect(adapter.name, 'Test Plugin');
      });

      test('should return correct version', () {
        expect(adapter.version, '1.0.0');
      });

      test('should return correct description', () {
        expect(adapter.description, 'A test plugin');
      });

      test('should return correct author', () {
        expect(adapter.author, 'Test Author');
      });

      test('should return correct category', () {
        expect(adapter.category, plugin_sys.PluginType.tool);
      });

      test('should return correct manifest', () {
        expect(adapter.manifest, same(manifest));
      });

      test('should return correct initial state', () {
        expect(adapter.currentState, plugin_sys.PluginState.loaded);
      });

      test('should return correct initial enabled state', () {
        expect(adapter.isEnabled, isFalse);
      });

      test('should return null load time initially', () {
        expect(adapter.loadTime, isNull);
      });
    });

    group('Permissions Conversion', () {
      test('should convert filesystem permission correctly', () {
        final List<plugin_sys.PluginPermission> permissions =
            adapter.requiredPermissions;
        expect(permissions, contains(plugin_sys.PluginPermission.fileSystem));
      });

      test('should convert network permission correctly', () {
        final List<plugin_sys.PluginPermission> permissions =
            adapter.requiredPermissions;
        expect(permissions, contains(plugin_sys.PluginPermission.network));
      });
    });

    group('Platform Conversion', () {
      test('should convert android platform correctly', () {
        final List<plugin_sys.SupportedPlatform> platforms =
            adapter.supportedPlatforms;
        expect(platforms, contains(plugin_sys.SupportedPlatform.android));
      });

      test('should convert ios platform correctly', () {
        final List<plugin_sys.SupportedPlatform> platforms =
            adapter.supportedPlatforms;
        expect(platforms, contains(plugin_sys.SupportedPlatform.ios));
      });
    });

    group('State Management', () {
      test('should initialize correctly', () async {
        await adapter.initialize();
        expect(adapter.currentState, plugin_sys.PluginState.initialized);
      });

      test('should start correctly', () async {
        await adapter.initialize();
        await adapter.start();
        expect(adapter.currentState, plugin_sys.PluginState.started);
        expect(adapter.isEnabled, isTrue);
        expect(adapter.loadTime, isNotNull);
      });

      test('should pause correctly', () async {
        await adapter.initialize();
        await adapter.start();
        await adapter.pause();
        expect(adapter.currentState, plugin_sys.PluginState.paused);
      });

      test('should resume correctly', () async {
        await adapter.initialize();
        await adapter.start();
        await adapter.pause();
        await adapter.resume();
        expect(adapter.currentState, plugin_sys.PluginState.started);
      });

      test('should stop correctly', () async {
        await adapter.initialize();
        await adapter.start();
        await adapter.stop();
        expect(adapter.currentState, plugin_sys.PluginState.stopped);
      });

      test('should dispose correctly', () async {
        await adapter.initialize();
        await adapter.dispose();
        expect(adapter.currentState, plugin_sys.PluginState.unloaded);
      });

      test('should emit state changes', () async {
        final List<plugin_sys.PluginState> states = <plugin_sys.PluginState>[];
        final StreamSubscription<plugin_sys.PluginState> subscription =
            adapter.stateChanges.listen(states.add);

        await adapter.initialize();
        await adapter.start();
        await adapter.stop();

        await subscription.cancel();

        expect(states, contains(plugin_sys.PluginState.loaded));
        expect(states, contains(plugin_sys.PluginState.initialized));
        expect(states, contains(plugin_sys.PluginState.started));
        expect(states, contains(plugin_sys.PluginState.stopped));
      });
    });

    group('Message Handling', () {
      test('should handle ping message', () async {
        final dynamic result =
            await adapter.handleMessage('ping', <String, dynamic>{});
        expect(result, isA<Map<String, dynamic>>());
        expect(result['status'], 'pong');
        expect(result['pluginId'], 'test_plugin');
      });

      test('should handle getInfo message', () async {
        final dynamic result =
            await adapter.handleMessage('getInfo', <String, dynamic>{});
        expect(result, isA<Map<String, dynamic>>());
        expect(result['id'], 'test_plugin');
        expect(result['name'], 'Test Plugin');
        expect(result['version'], '1.0.0');
        expect(result['workshopInfo'], isA<Map<String, dynamic>>());
      });

      test('should handle unknown message', () async {
        final dynamic result =
            await adapter.handleMessage('unknown', <String, dynamic>{});
        expect(result, isA<Map<String, dynamic>>());
        expect(result['error'], 'Unknown action: unknown');
      });
    });

    group('Widget Methods', () {
      test('should return null for config widget', () {
        expect(adapter.getConfigWidget(), isNull);
      });

      test('should return string for main widget', () {
        final Object mainWidget = adapter.getMainWidget();
        expect(mainWidget, isA<String>());
        expect(mainWidget.toString(), contains('Workshop Plugin'));
        expect(mainWidget.toString(), contains('Test Plugin'));
        expect(mainWidget.toString(), contains('test_plugin'));
      });
    });

    group('Type Conversion', () {
      test('should convert plugin categories correctly', () {
        expect(adapter.category, plugin_sys.PluginType.tool);
      });

      test('should handle unknown category', () {
        const plugin_sys.PluginManifest unknownManifest =
            plugin_sys.PluginManifest(
          id: 'test',
          name: 'Test',
          version: '1.0.0',
          description: 'Test',
          author: 'Test',
          category: 'unknown_category',
          main: 'lib/main.dart',
        );

        final WorkshopPluginAdapter unknownAdapter = WorkshopPluginAdapter(
          manifest: unknownManifest,
          workshopInfo: workshopInfo,
        );

        expect(unknownAdapter.category, plugin_sys.PluginType.tool);
      });

      test('should handle unknown permission', () {
        const plugin_sys.PluginManifest permissionManifest =
            plugin_sys.PluginManifest(
          id: 'test',
          name: 'Test',
          version: '1.0.0',
          description: 'Test',
          author: 'Test',
          category: 'tool',
          main: 'lib/main.dart',
          permissions: <String>['unknown_permission'],
        );

        final WorkshopPluginAdapter permissionAdapter = WorkshopPluginAdapter(
          manifest: permissionManifest,
          workshopInfo: workshopInfo,
        );

        final List<plugin_sys.PluginPermission> permissions =
            permissionAdapter.requiredPermissions;
        expect(permissions, contains(plugin_sys.PluginPermission.fileSystem));
      });

      test('should handle unknown platform', () {
        const plugin_sys.PluginManifest platformManifest =
            plugin_sys.PluginManifest(
          id: 'test',
          name: 'Test',
          version: '1.0.0',
          description: 'Test',
          author: 'Test',
          category: 'tool',
          main: 'lib/main.dart',
          platforms: <String>['unknown_platform'],
        );

        final WorkshopPluginAdapter platformAdapter = WorkshopPluginAdapter(
          manifest: platformManifest,
          workshopInfo: workshopInfo,
        );

        final List<plugin_sys.SupportedPlatform> platforms =
            platformAdapter.supportedPlatforms;
        expect(platforms, contains(plugin_sys.SupportedPlatform.android));
      });
    });
  });
}
