/*
---------------------------------------------------------------
File name:          simple_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        简单功能测试 - 策略A重构阶段4
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 简单功能验证测试;
---------------------------------------------------------------
*/

import 'package:test/test.dart';
import 'package:creative_workshop/src/core/projects/project_manager.dart';

void main() {
  group('Simple Creative Workshop Tests', () {
    test('ProjectType enum should have correct values', () {
      expect(ProjectType.values.length, greaterThan(0));
      expect(ProjectType.values, contains(ProjectType.plugin));
      expect(ProjectType.values, contains(ProjectType.design));
      expect(ProjectType.values, contains(ProjectType.animation));
    });

    test('ProjectStatus enum should have correct values', () {
      expect(ProjectStatus.values.length, greaterThan(0));
      expect(ProjectStatus.values, contains(ProjectStatus.draft));
      expect(ProjectStatus.values, contains(ProjectStatus.inProgress));
      expect(ProjectStatus.values, contains(ProjectStatus.completed));
    });

    test('CreativeProject should create with required fields', () {
      final project = CreativeProject(
        name: 'Test Project',
        type: ProjectType.plugin,
        description: 'Test Description',
      );

      expect(project.name, equals('Test Project'));
      expect(project.type, equals(ProjectType.plugin));
      expect(project.description, equals('Test Description'));
      expect(project.status, equals(ProjectStatus.draft));
      expect(project.id, isNotEmpty);
      expect(project.createdAt, isNotNull);
      expect(project.updatedAt, isNotNull);
    });

    test('ProjectManager should be singleton', () {
      final manager1 = ProjectManager.instance;
      final manager2 = ProjectManager.instance;
      expect(manager1, same(manager2));
    });

    test('ProjectResult should create success result', () {
      final result = ProjectResult<String>.success('test data');
      expect(result.success, isTrue);
      expect(result.data, equals('test data'));
      expect(result.error, isNull);
    });

    test('ProjectResult should create failure result', () {
      final result = ProjectResult<String>.failure('test error');
      expect(result.success, isFalse);
      expect(result.data, isNull);
      expect(result.error, equals('test error'));
    });

    test('CreativeProject should convert to JSON', () {
      final project = CreativeProject(
        name: 'Test Project',
        type: ProjectType.plugin,
        description: 'Test Description',
        tags: ['test', 'demo'],
        metadata: {'version': '1.0.0'},
      );

      final json = project.toJson();
      expect(json['name'], equals('Test Project'));
      expect(json['type'], equals('plugin'));
      expect(json['description'], equals('Test Description'));
      expect(json['tags'], equals(['test', 'demo']));
      expect(json['metadata']['version'], equals('1.0.0'));
    });

    test('CreativeProject should create from JSON', () {
      final json = {
        'id': 'test-id',
        'name': 'Test Project',
        'type': 'plugin',
        'description': 'Test Description',
        'status': 'draft',
        'createdAt': DateTime.now().toIso8601String(),
        'updatedAt': DateTime.now().toIso8601String(),
        'tags': ['test', 'demo'],
        'metadata': {'version': '1.0.0'},
        'data': {},
      };

      final project = CreativeProject.fromJson(json);
      expect(project.id, equals('test-id'));
      expect(project.name, equals('Test Project'));
      expect(project.type, equals(ProjectType.plugin));
      expect(project.description, equals('Test Description'));
      expect(project.status, equals(ProjectStatus.draft));
      expect(project.tags, equals(['test', 'demo']));
      expect(project.metadata['version'], equals('1.0.0'));
    });
  });
}
