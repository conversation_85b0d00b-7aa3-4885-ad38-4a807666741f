/*
---------------------------------------------------------------
File name:          mock_plugin_store_manager.dart
Author:             lgnorant-lu
Date created:       2025-07-24
Last modified:      2025-07-24
Dart Version:       3.2+
Description:        测试用模拟插件商店管理器
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:io';
import 'package:plugin_system/plugin_system.dart';

/// 测试用模拟插件商店管理器
class MockPluginStoreManager {

  MockPluginStoreManager._();
  static MockPluginStoreManager? _instance;

  /// 获取单例实例
  static MockPluginStoreManager get instance {
    _instance ??= MockPluginStoreManager._();
    return _instance!;
  }

  /// 模拟插件数据
  final Map<String, PluginStoreEntry> _mockPlugins = <String, PluginStoreEntry>{
    'advanced_brush': PluginStoreEntry(
      id: 'advanced_brush',
      name: '高级画笔工具',
      description: '提供多种高级画笔效果',
      version: '1.2.0',
      author: 'Creative Team',
      storeId: 'mock_store',
      category: 'tools',
      tags: const <String>['画笔', '绘画', '工具'],
      rating: 4.8,
      downloadCount: 15420,
      updatedAt: DateTime.now().subtract(const Duration(days: 7)),
      screenshots: const <String>[],
      downloadUrl: 'mock://plugins/advanced_brush.zip',
    ),
    'color_palette': PluginStoreEntry(
      id: 'color_palette',
      name: '调色板专家',
      description: '智能调色板工具',
      version: '1.5.2',
      author: 'Color Studio',
      storeId: 'mock_store',
      category: 'tools',
      tags: const <String>['调色板', '颜色', '设计'],
      rating: 4.6,
      downloadCount: 8930,
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      screenshots: const <String>[],
      downloadUrl: 'mock://plugins/color_palette.zip',
    ),
  };

  /// 获取插件详情
  Future<PluginStoreEntry?> getPluginDetails(String pluginId) async {
    // 模拟网络延迟
    await Future<void>.delayed(const Duration(milliseconds: 100));

    // 如果是测试插件，动态创建
    if (pluginId.startsWith('test_plugin_')) {
      return PluginStoreEntry(
        id: pluginId,
        name: 'Test Plugin',
        description: 'A test plugin for unit testing',
        version: '1.0.0',
        author: 'Test Author',
        storeId: 'mock_store',
        category: 'tools',
        tags: <String>const <String>['test'],
        rating: 4,
        downloadCount: 100,
        updatedAt: DateTime.now(),
        screenshots: <String>const <dynamic>[],
        downloadUrl: 'mock://plugins/$pluginId.zip',
      );
    }

    return _mockPlugins[pluginId];
  }

  /// 下载插件
  Future<bool> downloadPlugin(
    String pluginId,
    String version,
    String savePath, {
    String? storeId,
    void Function(int received, int total)? onProgress,
  }) async {
    // 模拟下载进度
    for (int i = 0; i <= 100; i += 10) {
      await Future<void>.delayed(const Duration(milliseconds: 10));
      onProgress?.call(i, 100);
    }

    // 模拟创建下载文件
    try {
      final file = File(savePath);
      await file.parent.create(recursive: true);
      await file.writeAsString('Mock plugin content for $pluginId');
      return true;
    } catch (e) {
      return false;
    }
  }

  /// 搜索插件
  Future<PluginSearchResult> searchPlugins(PluginSearchQuery query) async {
    await Future<void>.delayed(const Duration(milliseconds: 50));

    final plugins = _mockPlugins.values.toList();
    return PluginSearchResult(
      plugins: plugins,
      totalCount: plugins.length,
      query: query,
    );
  }
}

/// 为测试设置模拟的插件商店管理器
void setupMockPluginStoreManager() {
  // 这里可以添加设置模拟管理器的逻辑
  // 例如替换 PluginStoreManager.instance
}
