z/*
---------------------------------------------------------------
File name:          config_workflow_coordinator.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        配置变更工作流协调器 - P1.2 工作流集成核心组件
---------------------------------------------------------------
*/

import 'dart:async';
import 'package:flutter/foundation.dart';

import 'unified_message_bus.dart';
import 'module_communication_coordinator.dart';
import 'data_sync_manager.dart';
import 'conflict_resolution_engine.dart';

/// 配置变更工作流状态
enum ConfigWorkflowStatus {
  /// 空闲状态
  idle,

  /// 验证中
  validating,

  /// 同步中
  synchronizing,

  /// 应用中
  applying,

  /// 通知中
  notifying,

  /// 完成
  completed,

  /// 失败
  failed,

  /// 回滚中
  rollback,
}

/// 配置变更工作流结果
@immutable
class ConfigWorkflowResult {
  const ConfigWorkflowResult({
    required this.workflowId,
    required this.status,
    required this.timestamp,
    this.appliedModules = const <String>[],
    this.failedModules = const <String>[],
    this.conflicts = const <String>[],
    this.error,
    this.rollbackRequired = false,
  });

  /// 工作流ID
  final String workflowId;

  /// 工作流状态
  final ConfigWorkflowStatus status;

  /// 时间戳
  final DateTime timestamp;

  /// 成功应用的模块
  final List<String> appliedModules;

  /// 失败的模块
  final List<String> failedModules;

  /// 冲突列表
  final List<String> conflicts;

  /// 错误信息
  final String? error;

  /// 是否需要回滚
  final bool rollbackRequired;

  /// 是否成功
  bool get isSuccess =>
      status == ConfigWorkflowStatus.completed && failedModules.isEmpty;

  /// 是否有冲突
  bool get hasConflicts => conflicts.isNotEmpty;

  /// 转换为JSON
  Map<String, dynamic> toJson() => <String, dynamic>{
        'workflowId': workflowId,
        'status': status.name,
        'timestamp': timestamp.toIso8601String(),
        'appliedModules': appliedModules,
        'failedModules': failedModules,
        'conflicts': conflicts,
        'error': error,
        'rollbackRequired': rollbackRequired,
      };
}

/// 配置变更工作流协调器
///
/// P1.2 核心功能：
/// - 协调跨模块配置变更
/// - 处理配置冲突解决
/// - 管理配置同步工作流
/// - 提供配置回滚机制
class ConfigWorkflowCoordinator {
  ConfigWorkflowCoordinator._();

  static final ConfigWorkflowCoordinator _instance =
      ConfigWorkflowCoordinator._();
  static ConfigWorkflowCoordinator get instance => _instance;

  /// 统一消息总线
  final UnifiedMessageBus _messageBus = UnifiedMessageBus.instance;

  /// 模块通信协调器
  final ModuleCommunicationCoordinator _moduleCoordinator =
      ModuleCommunicationCoordinator.instance;

  /// 数据同步管理器
  final DataSyncManager _syncManager = DataSyncManager.instance;

  /// 冲突解决引擎
  final ConflictResolutionEngine _conflictEngine =
      ConflictResolutionEngine.instance;

  /// 当前工作流状态
  final Map<String, ConfigWorkflowStatus> _workflowStatus = {};

  /// 工作流历史
  final Map<String, ConfigWorkflowResult> _workflowHistory = {};

  /// 配置快照（用于回滚）
  final Map<String, Map<String, Map<String, dynamic>>> _configSnapshots = {};

  /// 工作流ID计数器
  int _workflowIdCounter = 0;

  /// 生成工作流ID
  String _generateWorkflowId() {
    return 'config_workflow_${DateTime.now().millisecondsSinceEpoch}_${++_workflowIdCounter}';
  }

  /// 启动配置变更工作流
  ///
  /// [sourceModule] 发起变更的模块
  /// [configChanges] 配置变更内容
  /// [targetModules] 目标模块列表，为空则广播到所有模块
  /// [strategy] 同步策略
  Future<ConfigWorkflowResult> startConfigWorkflow({
    required String sourceModule,
    required Map<String, dynamic> configChanges,
    List<String> targetModules = const <String>[],
    SyncStrategy strategy = SyncStrategy.realtime,
  }) async {
    final workflowId = _generateWorkflowId();

    try {
      debugPrint('启动配置变更工作流: $workflowId');

      // 1. 初始化工作流
      _workflowStatus[workflowId] = ConfigWorkflowStatus.idle;
      await _createConfigSnapshot(workflowId, targetModules);

      // 2. 验证配置变更
      _workflowStatus[workflowId] = ConfigWorkflowStatus.validating;
      final validationResult = await _validateConfigChanges(
        sourceModule,
        configChanges,
        targetModules,
      );

      if (!(validationResult['valid'] ?? false)) {
        return _createFailureResult(
          workflowId,
          '配置验证失败: ${validationResult['error']}',
        );
      }

      // 3. 检测和解决冲突
      _workflowStatus[workflowId] = ConfigWorkflowStatus.synchronizing;
      final conflictResult = await _resolveConfigConflicts(
        sourceModule,
        configChanges,
        targetModules,
      );

      // 4. 应用配置变更
      _workflowStatus[workflowId] = ConfigWorkflowStatus.applying;
      final applyResult = await _applyConfigChanges(
        workflowId,
        sourceModule,
        configChanges,
        targetModules,
        strategy,
      );

      // 5. 通知配置变更完成
      _workflowStatus[workflowId] = ConfigWorkflowStatus.notifying;
      await _notifyConfigWorkflowCompleted(workflowId, applyResult);

      // 6. 完成工作流
      _workflowStatus[workflowId] = ConfigWorkflowStatus.completed;

      final result = ConfigWorkflowResult(
        workflowId: workflowId,
        status: ConfigWorkflowStatus.completed,
        timestamp: DateTime.now(),
        appliedModules: applyResult['appliedModules'] as List<String>,
        failedModules: applyResult['failedModules'] as List<String>,
        conflicts: conflictResult['conflicts'] as List<String>,
      );

      _workflowHistory[workflowId] = result;
      debugPrint('配置变更工作流完成: $workflowId');

      return result;
    } catch (e, stackTrace) {
      debugPrint('配置变更工作流失败: $workflowId, 错误: $e');
      debugPrint('堆栈跟踪: $stackTrace');

      // 尝试回滚
      await _rollbackConfigChanges(workflowId);

      return _createFailureResult(workflowId, e.toString(),
          rollbackRequired: true);
    }
  }

  /// 创建配置快照
  Future<void> _createConfigSnapshot(
      String workflowId, List<String> modules) async {
    try {
      final snapshot = <String, Map<String, dynamic>>{};

      // 如果没有指定模块，获取所有已注册模块
      final targetModules = modules.isEmpty
          ? _moduleCoordinator.registeredModules.map((m) => m.id).toList()
          : modules;

      for (final moduleId in targetModules) {
        try {
          // 请求模块当前配置
          final response = await _moduleCoordinator.sendRequest(
            'system',
            moduleId,
            'get_current_config',
            <String, dynamic>{},
            timeoutMs: 3000,
          );

          if (response != null) {
            snapshot[moduleId] = response;
          }
        } catch (e) {
          debugPrint('获取模块 $moduleId 配置快照失败: $e');
        }
      }

      _configSnapshots[workflowId] = snapshot;
      debugPrint('配置快照已创建: $workflowId, 模块数: ${snapshot.length}');
    } catch (e) {
      debugPrint('创建配置快照失败: $e');
    }
  }

  /// 验证配置变更
  Future<Map<String, dynamic>> _validateConfigChanges(
    String sourceModule,
    Map<String, dynamic> configChanges,
    List<String> targetModules,
  ) async {
    try {
      // 基本验证
      if (configChanges.isEmpty) {
        return <String, dynamic>{
          'valid': false,
          'error': '配置变更不能为空',
        };
      }

      // 验证源模块
      if (_moduleCoordinator.getModuleInfo(sourceModule) == null) {
        return <String, dynamic>{
          'valid': false,
          'error': '源模块未注册: $sourceModule',
        };
      }

      // 验证目标模块
      for (final moduleId in targetModules) {
        if (_moduleCoordinator.getModuleInfo(moduleId) == null) {
          return <String, dynamic>{
            'valid': false,
            'error': '目标模块未注册: $moduleId',
          };
        }
      }

      return <String, dynamic>{
        'valid': true,
      };
    } catch (e) {
      return <String, dynamic>{
        'valid': false,
        'error': '配置验证异常: $e',
      };
    }
  }

  /// 解决配置冲突
  Future<Map<String, dynamic>> _resolveConfigConflicts(
    String sourceModule,
    Map<String, dynamic> configChanges,
    List<String> targetModules,
  ) async {
    try {
      final conflicts = <String>[];

      // 检测配置冲突
      for (final moduleId in targetModules) {
        try {
          final conflictRecord = await _conflictEngine.detectConflict(
            type: ConflictType.configuration,
            severity: ConflictSeverity.medium,
            involvedModules: [sourceModule, moduleId],
            resourceId: 'config_${sourceModule}_${moduleId}',
            description: '配置变更冲突: $sourceModule -> $moduleId',
            metadata: {
              'configChanges': configChanges,
              'sourceModule': sourceModule,
              'targetModule': moduleId,
            },
          );

          conflicts.add(conflictRecord.description);

          // 尝试自动解决冲突
          await _conflictEngine.resolveConflict(conflictRecord.id);
        } catch (e) {
          debugPrint('检测模块 $moduleId 配置冲突失败: $e');
        }
      }

      return <String, dynamic>{
        'conflicts': conflicts,
        'resolved': true,
      };
    } catch (e) {
      return <String, dynamic>{
        'conflicts': <String>[],
        'resolved': false,
        'error': '冲突解决异常: $e',
      };
    }
  }

  /// 应用配置变更
  Future<Map<String, dynamic>> _applyConfigChanges(
    String workflowId,
    String sourceModule,
    Map<String, dynamic> configChanges,
    List<String> targetModules,
    SyncStrategy strategy,
  ) async {
    final appliedModules = <String>[];
    final failedModules = <String>[];

    try {
      // 如果没有指定目标模块，广播到所有模块
      final targets = targetModules.isEmpty
          ? _moduleCoordinator.registeredModules.map((m) => m.id).toList()
          : targetModules;

      for (final moduleId in targets) {
        if (moduleId == sourceModule) continue; // 跳过源模块

        try {
          // 发送配置变更请求
          final response = await _moduleCoordinator.sendRequest(
            sourceModule,
            moduleId,
            'config_changed',
            configChanges,
            timeoutMs: 10000,
          );

          if (response != null && response['success'] == true) {
            appliedModules.add(moduleId);

            // 记录数据变更用于同步
            await _syncManager.recordDataChange(
              moduleId: moduleId,
              dataKey: 'config',
              changeType: DataChangeType.update,
              newValue: configChanges,
              metadata: {
                'sourceModule': sourceModule,
                'workflowId': workflowId,
              },
            );
          } else {
            failedModules.add(moduleId);
            debugPrint('模块 $moduleId 配置应用失败: ${response?['error']}');
          }
        } catch (e) {
          failedModules.add(moduleId);
          debugPrint('向模块 $moduleId 发送配置变更失败: $e');
        }
      }

      return <String, dynamic>{
        'appliedModules': appliedModules,
        'failedModules': failedModules,
        'success': failedModules.isEmpty,
      };
    } catch (e) {
      return <String, dynamic>{
        'appliedModules': appliedModules,
        'failedModules': failedModules,
        'success': false,
        'error': '应用配置变更异常: $e',
      };
    }
  }

  /// 通知配置工作流完成
  Future<void> _notifyConfigWorkflowCompleted(
    String workflowId,
    Map<String, dynamic> result,
  ) async {
    try {
      // 发布工作流完成事件
      _messageBus.publishEvent(
        'system',
        'config_workflow_completed',
        <String, dynamic>{
          'workflowId': workflowId,
          'result': result,
          'timestamp': DateTime.now().toIso8601String(),
        },
        priority: MessagePriority.high,
      );

      debugPrint('配置工作流完成通知已发送: $workflowId');
    } catch (e) {
      debugPrint('发送配置工作流完成通知失败: $e');
    }
  }

  /// 回滚配置变更
  Future<void> _rollbackConfigChanges(String workflowId) async {
    try {
      _workflowStatus[workflowId] = ConfigWorkflowStatus.rollback;

      final snapshot = _configSnapshots[workflowId];
      if (snapshot == null) {
        debugPrint('没有找到工作流 $workflowId 的配置快照');
        return;
      }

      debugPrint('开始回滚配置变更: $workflowId');

      for (final entry in snapshot.entries) {
        final moduleId = entry.key;
        final originalConfig = entry.value;

        try {
          await _moduleCoordinator.sendRequest(
            'system',
            moduleId,
            'config_changed',
            originalConfig,
            timeoutMs: 5000,
          );

          debugPrint('模块 $moduleId 配置已回滚');
        } catch (e) {
          debugPrint('回滚模块 $moduleId 配置失败: $e');
        }
      }

      debugPrint('配置回滚完成: $workflowId');
    } catch (e) {
      debugPrint('配置回滚异常: $e');
    }
  }

  /// 创建失败结果
  ConfigWorkflowResult _createFailureResult(
    String workflowId,
    String error, {
    bool rollbackRequired = false,
  }) {
    _workflowStatus[workflowId] = ConfigWorkflowStatus.failed;

    final result = ConfigWorkflowResult(
      workflowId: workflowId,
      status: ConfigWorkflowStatus.failed,
      timestamp: DateTime.now(),
      error: error,
      rollbackRequired: rollbackRequired,
    );

    _workflowHistory[workflowId] = result;
    return result;
  }

  /// 获取工作流状态
  ConfigWorkflowStatus? getWorkflowStatus(String workflowId) {
    return _workflowStatus[workflowId];
  }

  /// 获取工作流历史
  List<ConfigWorkflowResult> getWorkflowHistory() {
    return _workflowHistory.values.toList()
      ..sort((a, b) => b.timestamp.compareTo(a.timestamp));
  }

  /// 清理过期的工作流数据
  void cleanupExpiredWorkflows({Duration maxAge = const Duration(hours: 24)}) {
    final cutoffTime = DateTime.now().subtract(maxAge);

    final expiredWorkflows = _workflowHistory.entries
        .where((entry) => entry.value.timestamp.isBefore(cutoffTime))
        .map((entry) => entry.key)
        .toList();

    for (final workflowId in expiredWorkflows) {
      _workflowHistory.remove(workflowId);
      _workflowStatus.remove(workflowId);
      _configSnapshots.remove(workflowId);
    }

    debugPrint('清理了 ${expiredWorkflows.length} 个过期工作流');
  }
}
