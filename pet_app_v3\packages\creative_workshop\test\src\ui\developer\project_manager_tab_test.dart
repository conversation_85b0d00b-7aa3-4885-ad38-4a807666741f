/*
---------------------------------------------------------------
File name:          project_manager_tab_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        项目管理标签页测试 - 策略A重构阶段4
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 项目管理功能强化测试;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/ui/developer/project_manager_tab.dart'
    show ProjectManagerTab, ProjectType, ProjectStatus, DeveloperProject;

void main() {
  group('ProjectManagerTab Tests', () {
    testWidgets('should display project manager interface', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      // 验证基本UI元素
      expect(find.text('项目管理'), findsOneWidget);
      expect(find.byIcon(Icons.add), findsOneWidget);
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('should show create project dialog when add button is tapped',
        (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      // 等待加载完成
      await tester.pumpAndSettle();

      // 点击新建项目按钮
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 验证对话框出现
      expect(find.text('创建新项目'), findsOneWidget);
      expect(find.text('项目名称'), findsOneWidget);
      expect(find.text('项目类型'), findsOneWidget);
    });

    testWidgets('should validate project name input', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开创建项目对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 尝试创建空名称的项目
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证验证错误
      expect(find.text('请输入项目名称'), findsOneWidget);
    });

    testWidgets('should create project with valid input', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开创建项目对话框
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 输入项目信息
      await tester.enterText(find.byType(TextFormField).first, '测试项目');
      await tester.enterText(find.byType(TextFormField).at(1), '这是一个测试项目');

      // 创建项目
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证对话框关闭
      expect(find.text('创建新项目'), findsNothing);
    });

    testWidgets('should display project cards', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证项目卡片显示
      expect(find.byType(Card), findsWidgets);
    });

    testWidgets('should handle project actions', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找项目操作按钮
      final actionButtons = find.byIcon(Icons.more_vert);
      if (actionButtons.evaluate().isNotEmpty) {
        await tester.tap(actionButtons.first);
        await tester.pumpAndSettle();

        // 验证操作菜单
        expect(find.text('编辑'), findsOneWidget);
        expect(find.text('删除'), findsOneWidget);
      }
    });

    testWidgets('should filter projects by type', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找过滤器
      final filterChips = find.byType(FilterChip);
      if (filterChips.evaluate().isNotEmpty) {
        await tester.tap(filterChips.first);
        await tester.pumpAndSettle();

        // 验证过滤效果
        expect(find.byType(Card), findsWidgets);
      }
    });

    testWidgets('should search projects', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找搜索框
      final searchField = find.byType(TextField);
      if (searchField.evaluate().isNotEmpty) {
        await tester.enterText(searchField.first, '测试');
        await tester.pumpAndSettle();

        // 验证搜索结果
        expect(find.byType(Card), findsWidgets);
      }
    });

    testWidgets('should refresh project list', (tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: Scaffold(
            body: ProjectManagerTab(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击刷新按钮
      await tester.tap(find.byIcon(Icons.refresh));
      await tester.pumpAndSettle();

      // 验证刷新完成
      expect(find.byType(Card), findsWidgets);
    });
  });

  group('ProjectType Tests', () {
    test('should have correct display names', () {
      expect(ProjectType.tool.displayName, equals('工具插件'));
      expect(ProjectType.game.displayName, equals('游戏插件'));
      expect(ProjectType.utility.displayName, equals('实用工具'));
      expect(ProjectType.theme.displayName, equals('主题包'));
      expect(ProjectType.other.displayName, equals('其他'));
    });
  });

  group('ProjectStatus Tests', () {
    test('should have correct display names', () {
      expect(ProjectStatus.development.displayName, equals('开发中'));
      expect(ProjectStatus.testing.displayName, equals('测试中'));
      expect(ProjectStatus.ready.displayName, equals('准备发布'));
      expect(ProjectStatus.published.displayName, equals('已发布'));
      expect(ProjectStatus.archived.displayName, equals('已归档'));
    });

    // 注意：ProjectStatus可能没有color属性，这个测试可能需要调整
    // test('should have correct colors', () {
    //   expect(ProjectStatus.development.color, equals(Colors.blue));
    //   expect(ProjectStatus.testing.color, equals(Colors.orange));
    //   expect(ProjectStatus.ready.color, equals(Colors.green));
    //   expect(ProjectStatus.published.color, equals(Colors.purple));
    //   expect(ProjectStatus.archived.color, equals(Colors.grey));
    // });
  });

  group('DeveloperProject Tests', () {
    test('should create project with required fields', () {
      final project = DeveloperProject(
        id: 'test_id',
        name: 'Test Project',
        description: 'Test Description',
        type: ProjectType.tool,
        status: ProjectStatus.development,
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
        version: '1.0.0',
        tags: ['test'],
      );

      expect(project.id, equals('test_id'));
      expect(project.name, equals('Test Project'));
      expect(project.type, equals(ProjectType.tool));
      expect(project.status, equals(ProjectStatus.development));
    });
  });
}
