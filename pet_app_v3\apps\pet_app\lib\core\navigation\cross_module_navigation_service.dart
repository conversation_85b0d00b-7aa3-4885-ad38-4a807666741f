/*
---------------------------------------------------------------
File name:          cross_module_navigation_service.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        跨模块导航服务 - 真实实现
---------------------------------------------------------------
Change History:
    2025-07-30: 真实跨模块导航服务实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:app_manager/app_manager.dart';

/// 跨模块导航服务
/// 
/// 负责协调PageView导航与CrossModuleBridge的跨模块导航请求
class CrossModuleNavigationService {
  static CrossModuleNavigationService? _instance;
  static CrossModuleNavigationService get instance => _instance ??= CrossModuleNavigationService._();
  
  CrossModuleNavigationService._();

  // 导航控制器
  PageController? _pageController;
  
  // 页面索引映射
  final Map<String, int> _modulePageIndexMap = {};
  
  // 路由到页面索引的映射
  final Map<String, int> _routePageIndexMap = {};
  
  // 当前页面索引
  int _currentPageIndex = 0;
  
  // 导航状态回调
  final List<Function(int)> _navigationCallbacks = [];
  
  // 桥接服务
  CrossModuleBridge? _bridge;
  
  // 事件订阅
  StreamSubscription<CrossModuleEvent>? _eventSubscription;

  /// 初始化导航服务
  Future<void> initialize({
    required PageController pageController,
    required Map<String, int> modulePageIndexMap,
  }) async {
    try {
      developer.log('初始化跨模块导航服务', name: 'CrossModuleNavigationService');
      
      _pageController = pageController;
      _modulePageIndexMap.addAll(modulePageIndexMap);
      
      // 建立路由映射
      _buildRouteMapping();
      
      // 初始化CrossModuleBridge
      _bridge = CrossModuleBridge.instance;
      await _bridge!.initialize();
      
      // 订阅跨模块导航事件
      _eventSubscription = _bridge!.events.listen(_handleCrossModuleEvent);
      
      developer.log('跨模块导航服务初始化完成', name: 'CrossModuleNavigationService');
    } catch (e, stackTrace) {
      developer.log(
        '跨模块导航服务初始化失败',
        name: 'CrossModuleNavigationService',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 建立路由映射
  void _buildRouteMapping() {
    // 主页面路由映射
    _routePageIndexMap.addAll({
      '/': 0,
      '/home': 0,
      '/desktop_pet': 1,
      '/pet': 1,
      '/creative_workshop': 2,
      '/workshop': 2,
      '/app_manager': 3,
      '/apps': 3,
      '/settings': 4,
    });
    
    // App Manager子路由映射
    _routePageIndexMap.addAll({
      '/app_manager/store': 3,
      '/app_manager/dashboard': 3,
      '/app_manager/modules': 3,
      '/app_manager/permissions': 3,
    });
    
    // Creative Workshop子路由映射
    _routePageIndexMap.addAll({
      '/creative_workshop/projects': 2,
      '/creative_workshop/workspace': 2,
      '/creative_workshop/tools': 2,
      '/creative_workshop/games': 2,
      '/creative_workshop/settings': 2,
    });
  }

  /// 处理跨模块事件
  void _handleCrossModuleEvent(CrossModuleEvent event) {
    if (event.type == 'navigation_requested') {
      final fromModule = event.data['fromModule'] as String?;
      final toModule = event.data['toModule'] as String?;
      final route = event.data['route'] as String?;
      final params = event.data['params'] as Map<String, dynamic>?;
      
      if (route != null) {
        _handleNavigationRequest(fromModule, toModule, route, params);
      }
    }
  }

  /// 处理导航请求
  Future<void> _handleNavigationRequest(
    String? fromModule,
    String? toModule,
    String route,
    Map<String, dynamic>? params,
  ) async {
    try {
      developer.log(
        '处理导航请求: $fromModule -> $toModule ($route)',
        name: 'CrossModuleNavigationService',
      );

      // 查找目标页面索引
      final targetPageIndex = _routePageIndexMap[route];
      
      if (targetPageIndex != null) {
        await navigateToPage(targetPageIndex, params: params);
        developer.log('导航成功: $route -> 页面$targetPageIndex', name: 'CrossModuleNavigationService');
      } else {
        developer.log('未找到路由映射: $route', name: 'CrossModuleNavigationService');
        
        // 尝试模块级导航
        if (toModule != null) {
          final modulePageIndex = _modulePageIndexMap[toModule];
          if (modulePageIndex != null) {
            await navigateToPage(modulePageIndex, params: params);
            developer.log('模块导航成功: $toModule -> 页面$modulePageIndex', name: 'CrossModuleNavigationService');
          }
        }
      }
    } catch (e, stackTrace) {
      developer.log(
        '导航请求处理失败',
        name: 'CrossModuleNavigationService',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 导航到指定页面
  Future<void> navigateToPage(int pageIndex, {Map<String, dynamic>? params}) async {
    if (_pageController == null) {
      throw Exception('PageController未初始化');
    }
    
    if (pageIndex < 0 || pageIndex >= _modulePageIndexMap.length) {
      throw Exception('页面索引超出范围: $pageIndex');
    }
    
    if (pageIndex == _currentPageIndex) {
      developer.log('已在目标页面: $pageIndex', name: 'CrossModuleNavigationService');
      return;
    }

    try {
      // 执行页面切换动画
      await _pageController!.animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
      
      _currentPageIndex = pageIndex;
      
      // 通知导航回调
      for (final callback in _navigationCallbacks) {
        callback(pageIndex);
      }
      
      // 更新桥接服务状态
      _updateBridgeNavigationState(pageIndex, params);
      
      developer.log('页面导航完成: $pageIndex', name: 'CrossModuleNavigationService');
    } catch (e, stackTrace) {
      developer.log(
        '页面导航失败',
        name: 'CrossModuleNavigationService',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 更新桥接服务导航状态
  void _updateBridgeNavigationState(int pageIndex, Map<String, dynamic>? params) {
    if (_bridge == null) return;
    
    // 根据页面索引确定当前模块
    String? currentModule;
    for (final entry in _modulePageIndexMap.entries) {
      if (entry.value == pageIndex) {
        currentModule = entry.key;
        break;
      }
    }
    
    if (currentModule != null) {
      // 更新模块状态
      _bridge!.updateModuleState(currentModule, CrossModuleState.running);
      
      // 设置导航参数
      if (params != null) {
        _bridge!.setSharedData('navigation_params_$currentModule', params);
      }
      
      // 发布导航完成事件
      _bridge!.publishCrossModuleEvent(
        'navigation_completed',
        {
          'targetModule': currentModule,
          'pageIndex': pageIndex,
          'params': params,
        },
      );
    }
  }

  /// 注册导航回调
  void addNavigationCallback(Function(int) callback) {
    _navigationCallbacks.add(callback);
  }

  /// 移除导航回调
  void removeNavigationCallback(Function(int) callback) {
    _navigationCallbacks.remove(callback);
  }

  /// 获取当前页面索引
  int get currentPageIndex => _currentPageIndex;

  /// 更新当前页面索引（由外部PageView调用）
  void updateCurrentPageIndex(int index) {
    if (index != _currentPageIndex) {
      _currentPageIndex = index;
      _updateBridgeNavigationState(index, null);
    }
  }

  /// 获取页面索引映射
  Map<String, int> get modulePageIndexMap => Map.unmodifiable(_modulePageIndexMap);

  /// 获取路由映射
  Map<String, int> get routePageIndexMap => Map.unmodifiable(_routePageIndexMap);

  /// 添加路由映射
  void addRouteMapping(String route, int pageIndex) {
    _routePageIndexMap[route] = pageIndex;
  }

  /// 移除路由映射
  void removeRouteMapping(String route) {
    _routePageIndexMap.remove(route);
  }

  /// 检查路由是否存在
  bool hasRoute(String route) {
    return _routePageIndexMap.containsKey(route);
  }

  /// 获取路由对应的页面索引
  int? getPageIndexForRoute(String route) {
    return _routePageIndexMap[route];
  }

  /// 清理资源
  void dispose() {
    _eventSubscription?.cancel();
    _navigationCallbacks.clear();
    _modulePageIndexMap.clear();
    _routePageIndexMap.clear();
    _pageController = null;
    _bridge = null;
  }
}
