/*
---------------------------------------------------------------
File name:          local_ming_cli_service.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        Ming CLI本地集成服务
---------------------------------------------------------------
Change History:
    2025-07-30: 实现Ming CLI本地集成服务;
---------------------------------------------------------------
*/

import 'dart:io';
import 'dart:developer' as developer;
import 'package:path/path.dart' as path;

/// Ming CLI执行结果
class MingCliResult {
  const MingCliResult({
    required this.success,
    required this.output,
    required this.error,
    required this.exitCode,
    required this.executionTime,
    required this.mode,
  });

  final bool success;
  final String output;
  final String error;
  final int exitCode;
  final Duration executionTime;
  final MingCliMode mode;

  @override
  String toString() => 'MingCliResult(success: $success, exitCode: $exitCode, mode: $mode, duration: ${executionTime.inMilliseconds}ms)';
}

/// Ming CLI运行模式
enum MingCliMode {
  unknown,
  systemInstalled, // 系统PATH中安装
  localBuilt, // 本地构建版本
  dartRun, // 通过dart run执行
  fallback, // 降级模拟模式
}

/// Ming CLI本地集成服务
///
/// 负责管理Ming CLI的本地集成，包括：
/// - 检测本地Ming CLI安装
/// - 从源码构建本地版本
/// - 执行Ming CLI命令
/// - 降级到模拟模式
class LocalMingCliService {

  LocalMingCliService._();
  static LocalMingCliService? _instance;
  static LocalMingCliService get instance =>
      _instance ??= LocalMingCliService._();

  // Ming CLI状态
  bool _isInstalled = false;
  String _version = '';
  String? _executablePath;
  MingCliMode _currentMode = MingCliMode.unknown;

  // 本地构建路径
  static const String _localMingCliPath = 'tools/ming_status_cli';
  static const String _localExecutableName = 'ming';

  /// 获取安装状态
  bool get isInstalled => _isInstalled;

  /// 获取版本信息
  String get version => _version;

  /// 获取当前运行模式
  MingCliMode get currentMode => _currentMode;

  /// 获取状态描述
  String get statusDescription {
    switch (_currentMode) {
      case MingCliMode.systemInstalled:
        return '系统安装版本 v$_version';
      case MingCliMode.localBuilt:
        return '本地构建版本 v$_version';
      case MingCliMode.dartRun:
        return 'Dart Run模式 v$_version';
      case MingCliMode.fallback:
        return '模拟模式 (功能受限)';
      case MingCliMode.unknown:
        return '未检测到Ming CLI';
    }
  }

  /// 初始化服务
  Future<void> initialize() async {
    developer.log('初始化Ming CLI本地集成服务', name: 'LocalMingCliService');
    await _detectMingCli();
  }

  /// 检测Ming CLI安装状态
  Future<void> _detectMingCli() async {
    // 1. 检查系统PATH中的ming命令
    if (await _checkSystemMingCli()) {
      _currentMode = MingCliMode.systemInstalled;
      return;
    }

    // 2. 检查本地构建版本
    if (await _checkLocalBuiltMingCli()) {
      _currentMode = MingCliMode.localBuilt;
      return;
    }

    // 3. 检查是否可以通过dart run执行
    if (await _checkDartRunMingCli()) {
      _currentMode = MingCliMode.dartRun;
      return;
    }

    // 4. 降级到模拟模式
    _currentMode = MingCliMode.fallback;
    developer.log('未找到可用的Ming CLI，将使用模拟模式', name: 'LocalMingCliService');
  }

  /// 检查系统安装的Ming CLI
  Future<bool> _checkSystemMingCli() async {
    try {
      final result = await Process.run('ming', <String>['--version'], runInShell: true);
      if (result.exitCode == 0) {
        final versionOutput = result.stdout.toString().trim();
        final versionMatch =
            RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

        _isInstalled = true;
        _version = versionMatch?.group(1) ?? 'unknown';
        _executablePath = 'ming';

        developer.log('发现系统安装的Ming CLI: v$_version',
            name: 'LocalMingCliService',);
        return true;
      }
    } catch (e) {
      developer.log('系统Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 检查本地构建的Ming CLI
  Future<bool> _checkLocalBuiltMingCli() async {
    try {
      final localExecutablePath = path.join(
        Directory.current.path,
        _localMingCliPath,
        _localExecutableName,
      );

      final executableFile = File(localExecutablePath);
      if (await executableFile.exists()) {
        final result = await Process.run(localExecutablePath, <String>['--version'],
            runInShell: true,);
        if (result.exitCode == 0) {
          final versionOutput = result.stdout.toString().trim();
          final versionMatch =
              RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

          _isInstalled = true;
          _version = versionMatch?.group(1) ?? 'unknown';
          _executablePath = localExecutablePath;

          developer.log('发现本地构建的Ming CLI: v$_version',
              name: 'LocalMingCliService',);
          return true;
        }
      }
    } catch (e) {
      developer.log('本地构建Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 检查Dart Run模式的Ming CLI
  Future<bool> _checkDartRunMingCli() async {
    try {
      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));
      final mainFile =
          File(path.join(mingCliProjectPath, 'bin', 'ming_status_cli.dart'));

      if (await pubspecFile.exists() && await mainFile.exists()) {
        final result = await Process.run(
          'dart',
          <String>['run', 'bin/ming_status_cli.dart', '--version'],
          workingDirectory: mingCliProjectPath,
          runInShell: true,
        );

        if (result.exitCode == 0) {
          final versionOutput = result.stdout.toString().trim();
          final versionMatch =
              RegExp(r'(\d+\.\d+\.\d+)').firstMatch(versionOutput);

          _isInstalled = true;
          _version = versionMatch?.group(1) ?? 'unknown';
          _executablePath = mingCliProjectPath;

          developer.log('发现Dart Run模式的Ming CLI: v$_version',
              name: 'LocalMingCliService',);
          return true;
        }
      }
    } catch (e) {
      developer.log('Dart Run模式Ming CLI检查失败: $e', name: 'LocalMingCliService');
    }
    return false;
  }

  /// 执行Ming CLI命令
  Future<MingCliResult> executeCommand(String command) async {
    final args = command.trim().split(' ');
    return executeCommandWithArgs(args);
  }

  /// 执行Ming CLI命令（带参数）
  Future<MingCliResult> executeCommandWithArgs(List<String> args) async {
    final startTime = DateTime.now();

    try {
      switch (_currentMode) {
        case MingCliMode.systemInstalled:
          return await _executeSystemCommand(args, startTime);
        case MingCliMode.localBuilt:
          return await _executeLocalBuiltCommand(args, startTime);
        case MingCliMode.dartRun:
          return await _executeDartRunCommand(args, startTime);
        case MingCliMode.fallback:
        case MingCliMode.unknown:
          return await _executeFallbackCommand(args, startTime);
      }
    } catch (e, stackTrace) {
      final endTime = DateTime.now();
      final duration = endTime.difference(startTime);

      developer.log(
        'Ming CLI命令执行异常',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );

      return MingCliResult(
        success: false,
        output: '',
        error: '命令执行异常: $e',
        exitCode: -1,
        executionTime: duration,
        mode: _currentMode,
      );
    }
  }

  /// 执行系统安装的Ming CLI命令
  Future<MingCliResult> _executeSystemCommand(
      List<String> args, DateTime startTime,) async {
    final result = await Process.run('ming', args, runInShell: true);
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.systemInstalled,
    );
  }

  /// 执行本地构建的Ming CLI命令
  Future<MingCliResult> _executeLocalBuiltCommand(
      List<String> args, DateTime startTime,) async {
    final result = await Process.run(_executablePath!, args, runInShell: true);
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.localBuilt,
    );
  }

  /// 执行Dart Run Ming CLI命令
  Future<MingCliResult> _executeDartRunCommand(
      List<String> args, DateTime startTime,) async {
    final result = await Process.run(
      'dart',
      <String>['run', 'bin/ming_status_cli.dart', ...args],
      workingDirectory: _executablePath,
      runInShell: true,
    );
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    return MingCliResult(
      success: result.exitCode == 0,
      output: result.stdout.toString(),
      error: result.stderr.toString(),
      exitCode: result.exitCode,
      executionTime: duration,
      mode: MingCliMode.dartRun,
    );
  }

  /// 执行降级模拟命令
  Future<MingCliResult> _executeFallbackCommand(
      List<String> args, DateTime startTime,) async {
    final endTime = DateTime.now();
    final duration = endTime.difference(startTime);

    // TODO: 实现更详细的模拟命令响应
    final command = args.join(' ');
    String mockOutput;

    if (command.startsWith('create')) {
      mockOutput = '''
🎉 模拟模式：项目创建成功
📁 项目路径: ./mock_project
📋 模板类型: plugin
⚠️  注意：这是模拟输出，实际文件未创建
💡 提示：安装真实的Ming CLI以获得完整功能
''';
    } else if (command == 'doctor') {
      mockOutput = '''
🔍 模拟模式：环境检查
✅ Dart环境: 正常
✅ Flutter环境: 正常
⚠️  Ming CLI: 模拟模式
💡 建议：安装真实的Ming CLI以获得完整功能
''';
    } else if (command == '--version') {
      mockOutput = 'ming_status_cli 1.0.0 (模拟模式)';
    } else {
      mockOutput = '''
⚠️  模拟模式：命令 "$command" 已接收
💡 这是模拟输出，实际功能未执行
🔧 安装真实的Ming CLI以获得完整功能
''';
    }

    return MingCliResult(
      success: true,
      output: mockOutput,
      error: '',
      exitCode: 0,
      executionTime: duration,
      mode: MingCliMode.fallback,
    );
  }

  /// 尝试安装或构建Ming CLI
  Future<bool> installOrBuildMingCli() async {
    developer.log('尝试安装或构建Ming CLI', name: 'LocalMingCliService');

    try {
      // 1. 首先尝试通过pub global activate安装
      if (await _tryPubGlobalInstall()) {
        await _detectMingCli();
        return _isInstalled;
      }

      // 2. 尝试从本地源码构建
      if (await _tryLocalBuild()) {
        await _detectMingCli();
        return _isInstalled;
      }

      // 3. 尝试设置Dart Run模式
      if (await _trySetupDartRun()) {
        await _detectMingCli();
        return _isInstalled;
      }

      developer.log(
        '所有安装方式都失败，将使用降级模式',
        name: 'LocalMingCliService',
      );
      _currentMode = MingCliMode.fallback;
      return false;
    } catch (e, stackTrace) {
      developer.log(
        'Ming CLI安装失败',
        name: 'LocalMingCliService',
        error: e,
        stackTrace: stackTrace,
      );
      _currentMode = MingCliMode.fallback;
      return false;
    }
  }

  /// 尝试通过pub global activate安装
  Future<bool> _tryPubGlobalInstall() async {
    try {
      developer.log('尝试通过pub global activate安装Ming CLI',
          name: 'LocalMingCliService',);

      final result = await Process.run(
        'dart',
        <String>['pub', 'global', 'activate', 'ming_status_cli'],
        runInShell: true,
      );

      if (result.exitCode == 0) {
        developer.log('pub global activate安装成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log('pub global activate安装失败: ${result.stderr}',
            name: 'LocalMingCliService',);
        return false;
      }
    } catch (e) {
      developer.log('pub global activate安装异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试从本地源码构建
  Future<bool> _tryLocalBuild() async {
    try {
      developer.log('尝试从本地源码构建Ming CLI', name: 'LocalMingCliService');

      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));

      if (!await pubspecFile.exists()) {
        developer.log('未找到Ming CLI源码，无法构建', name: 'LocalMingCliService');
        return false;
      }

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        <String>['pub', 'get'],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode != 0) {
        developer.log('依赖安装失败: ${pubGetResult.stderr}',
            name: 'LocalMingCliService',);
        return false;
      }

      // 编译可执行文件
      final compileResult = await Process.run(
        'dart',
        <String>[
          'compile',
          'exe',
          'bin/ming_status_cli.dart',
          '-o',
          _localExecutableName,
        ],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (compileResult.exitCode == 0) {
        developer.log('本地构建成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log('本地构建失败: ${compileResult.stderr}',
            name: 'LocalMingCliService',);
        return false;
      }
    } catch (e) {
      developer.log('本地构建异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 尝试设置Dart Run模式
  Future<bool> _trySetupDartRun() async {
    try {
      developer.log('尝试设置Dart Run模式', name: 'LocalMingCliService');

      final mingCliProjectPath =
          path.join(Directory.current.path, _localMingCliPath);
      final pubspecFile = File(path.join(mingCliProjectPath, 'pubspec.yaml'));

      if (!await pubspecFile.exists()) {
        developer.log('未找到Ming CLI源码，无法设置Dart Run模式',
            name: 'LocalMingCliService',);
        return false;
      }

      // 安装依赖
      final pubGetResult = await Process.run(
        'dart',
        <String>['pub', 'get'],
        workingDirectory: mingCliProjectPath,
        runInShell: true,
      );

      if (pubGetResult.exitCode == 0) {
        developer.log('Dart Run模式设置成功', name: 'LocalMingCliService');
        return true;
      } else {
        developer.log('Dart Run模式设置失败: ${pubGetResult.stderr}',
            name: 'LocalMingCliService',);
        return false;
      }
    } catch (e) {
      developer.log('Dart Run模式设置异常: $e', name: 'LocalMingCliService');
      return false;
    }
  }

  /// 重新检测Ming CLI状态
  Future<void> refresh() async {
    developer.log('重新检测Ming CLI状态', name: 'LocalMingCliService');

    _isInstalled = false;
    _version = '';
    _executablePath = null;
    _currentMode = MingCliMode.unknown;

    await _detectMingCli();
  }

  /// 获取详细状态信息
  Map<String, dynamic> getDetailedStatus() => <String, dynamic>{
      'isInstalled': _isInstalled,
      'version': _version,
      'executablePath': _executablePath,
      'currentMode': _currentMode.toString(),
      'statusDescription': statusDescription,
      'timestamp': DateTime.now().toIso8601String(),
    };
}
