/*
---------------------------------------------------------------
File name:          local_ming_cli_service_test.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        LocalMingCliService测试
---------------------------------------------------------------
Change History:
    2025-07-30: 创建LocalMingCliService测试;
---------------------------------------------------------------
*/

import 'package:flutter_test/flutter_test.dart';
import 'package:creative_workshop/src/core/ming_cli/local_ming_cli_service.dart';

void main() {
  group('LocalMingCliService Tests', () {
    late LocalMingCliService service;

    setUp(() {
      service = LocalMingCliService.instance;
    });

    test('should be singleton', () {
      final service1 = LocalMingCliService.instance;
      final service2 = LocalMingCliService.instance;
      expect(service1, same(service2));
    });

    test('should initialize successfully', () async {
      await service.initialize();
      expect(service.currentMode, isNotNull);
    });

    test('should execute fallback command when no CLI available', () async {
      await service.initialize();
      
      final result = await service.executeCommand('--version');
      
      expect(result, isNotNull);
      expect(result.mode, isNotNull);
      expect(result.executionTime, isNotNull);
    });

    test('should handle create command in fallback mode', () async {
      await service.initialize();
      
      final result = await service.executeCommand('create test-plugin');
      
      expect(result, isNotNull);
      expect(result.output, contains('模拟模式'));
      expect(result.success, isTrue);
    });

    test('should handle doctor command in fallback mode', () async {
      await service.initialize();
      
      final result = await service.executeCommand('doctor');
      
      expect(result, isNotNull);
      expect(result.output, contains('环境检查'));
      expect(result.success, isTrue);
    });

    test('should provide detailed status', () async {
      await service.initialize();
      
      final status = service.getDetailedStatus();
      
      expect(status, isA<Map<String, dynamic>>());
      expect(status['isInstalled'], isA<bool>());
      expect(status['version'], isA<String>());
      expect(status['currentMode'], isA<String>());
      expect(status['statusDescription'], isA<String>());
      expect(status['timestamp'], isA<String>());
    });

    test('should refresh status', () async {
      await service.initialize();
      final initialMode = service.currentMode;
      
      await service.refresh();
      
      expect(service.currentMode, isNotNull);
      // 模式可能相同，但刷新操作应该成功
    });

    test('should handle installation attempt', () async {
      await service.initialize();
      
      // 安装可能失败，但不应该抛出异常
      final result = await service.installOrBuildMingCli();
      
      expect(result, isA<bool>());
      // 即使失败，服务也应该切换到fallback模式
      expect(service.currentMode, isNotNull);
    });

    group('MingCliResult Tests', () {
      test('should create result with all properties', () {
        const result = MingCliResult(
          success: true,
          output: 'test output',
          error: '',
          exitCode: 0,
          executionTime: Duration(milliseconds: 100),
          mode: MingCliMode.fallback,
        );

        expect(result.success, isTrue);
        expect(result.output, equals('test output'));
        expect(result.error, isEmpty);
        expect(result.exitCode, equals(0));
        expect(result.executionTime.inMilliseconds, equals(100));
        expect(result.mode, equals(MingCliMode.fallback));
      });

      test('should have meaningful toString', () {
        const result = MingCliResult(
          success: false,
          output: '',
          error: 'test error',
          exitCode: 1,
          executionTime: Duration(milliseconds: 200),
          mode: MingCliMode.systemInstalled,
        );

        final str = result.toString();
        expect(str, contains('success: false'));
        expect(str, contains('exitCode: 1'));
        expect(str, contains('mode: MingCliMode.systemInstalled'));
        expect(str, contains('200ms'));
      });
    });

    group('MingCliMode Tests', () {
      test('should have all expected modes', () {
        expect(MingCliMode.values, hasLength(5));
        expect(MingCliMode.values, contains(MingCliMode.unknown));
        expect(MingCliMode.values, contains(MingCliMode.systemInstalled));
        expect(MingCliMode.values, contains(MingCliMode.localBuilt));
        expect(MingCliMode.values, contains(MingCliMode.dartRun));
        expect(MingCliMode.values, contains(MingCliMode.fallback));
      });
    });
  });
}
