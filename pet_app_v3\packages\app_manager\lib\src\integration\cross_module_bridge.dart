/*
---------------------------------------------------------------
File name:          cross_module_bridge.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        跨模块通信桥接服务 - 策略A重构阶段5
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 跨模块通信桥接服务实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:developer' as developer;

/// 跨模块通信桥接服务
///
/// 负责App Manager和Creative Workshop模块间的通信协调
class CrossModuleBridge {
  static CrossModuleBridge? _instance;
  static CrossModuleBridge get instance => _instance ??= CrossModuleBridge._();

  CrossModuleBridge._();

  // 事件流控制器
  final StreamController<CrossModuleEvent> _eventController =
      StreamController<CrossModuleEvent>.broadcast();

  // 模块状态
  final Map<String, CrossModuleState> _moduleStates = {};

  // 共享数据存储
  final Map<String, dynamic> _sharedData = {};

  /// 获取事件流
  Stream<CrossModuleEvent> get events => _eventController.stream;

  /// 初始化桥接服务
  Future<void> initialize() async {
    try {
      developer.log('初始化跨模块通信桥接服务', name: 'CrossModuleBridge');

      // 注册默认模块
      _registerModule('app_manager', CrossModuleState.initializing);
      _registerModule('creative_workshop', CrossModuleState.initializing);

      // 发布初始化事件
      _publishEvent(CrossModuleEvent.bridgeInitialized());

      developer.log('跨模块通信桥接服务初始化完成', name: 'CrossModuleBridge');
    } catch (e, stackTrace) {
      developer.log(
        '跨模块通信桥接服务初始化失败',
        name: 'CrossModuleBridge',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 注册模块
  void _registerModule(String moduleId, CrossModuleState state) {
    _moduleStates[moduleId] = state;
    _publishEvent(CrossModuleEvent.moduleRegistered(moduleId, state));
  }

  /// 更新模块状态
  void updateModuleState(String moduleId, CrossModuleState state) {
    if (_moduleStates.containsKey(moduleId)) {
      _moduleStates[moduleId] = state;
      _publishEvent(CrossModuleEvent.moduleStateChanged(moduleId, state));
    }
  }

  /// 获取模块状态
  CrossModuleState? getModuleState(String moduleId) {
    return _moduleStates[moduleId];
  }

  /// 设置共享数据
  void setSharedData(String key, dynamic value, {String? sourceModule}) {
    _sharedData[key] = value;
    _publishEvent(CrossModuleEvent.dataShared(key, value, sourceModule));
  }

  /// 获取共享数据
  T? getSharedData<T>(String key) {
    final value = _sharedData[key];
    return value is T ? value : null;
  }

  /// 发布跨模块事件
  void publishCrossModuleEvent(String eventType, Map<String, dynamic> data,
      {String? sourceModule}) {
    _publishEvent(CrossModuleEvent.custom(eventType, data, sourceModule));
  }

  /// 请求模块间导航
  Future<bool> requestNavigation(
      String fromModule, String toModule, String route,
      {Map<String, dynamic>? params}) async {
    try {
      developer.log(
        '请求模块间导航: $fromModule -> $toModule ($route)',
        name: 'CrossModuleBridge',
      );

      // 发布导航请求事件
      _publishEvent(CrossModuleEvent.navigationRequested(
          fromModule, toModule, route, params));

      // TODO(navigation-integration): 实现真实的导航逻辑
      // 这里可以集成到主应用的路由系统

      return true;
    } catch (e) {
      developer.log(
        '模块间导航失败',
        name: 'CrossModuleBridge',
        error: e,
      );
      return false;
    }
  }

  /// 同步插件状态
  void syncPluginState(String pluginId, PluginSyncData data) {
    setSharedData('plugin_$pluginId', data, sourceModule: 'app_manager');
    _publishEvent(CrossModuleEvent.pluginStateSynced(pluginId, data));
  }

  /// 获取插件状态
  PluginSyncData? getPluginState(String pluginId) {
    return getSharedData<PluginSyncData>('plugin_$pluginId');
  }

  /// 同步项目状态
  void syncProjectState(String projectId, ProjectSyncData data) {
    setSharedData('project_$projectId', data,
        sourceModule: 'creative_workshop');
    _publishEvent(CrossModuleEvent.projectStateSynced(projectId, data));
  }

  /// 获取项目状态
  ProjectSyncData? getProjectState(String projectId) {
    return getSharedData<ProjectSyncData>('project_$projectId');
  }

  /// 发布事件
  void _publishEvent(CrossModuleEvent event) {
    _eventController.add(event);
  }

  /// 清理资源
  void dispose() {
    _eventController.close();
    _moduleStates.clear();
    _sharedData.clear();
  }
}

/// 跨模块状态枚举
enum CrossModuleState {
  initializing,
  ready,
  running,
  error,
  disposed,
}

/// 跨模块事件
class CrossModuleEvent {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  CrossModuleEvent._(this.type, this.data) : timestamp = DateTime.now();

  factory CrossModuleEvent.bridgeInitialized() =>
      CrossModuleEvent._('bridge_initialized', {});

  factory CrossModuleEvent.moduleRegistered(
          String moduleId, CrossModuleState state) =>
      CrossModuleEvent._('module_registered', {
        'moduleId': moduleId,
        'state': state.name,
      });

  factory CrossModuleEvent.moduleStateChanged(
          String moduleId, CrossModuleState state) =>
      CrossModuleEvent._('module_state_changed', {
        'moduleId': moduleId,
        'state': state.name,
      });

  factory CrossModuleEvent.dataShared(
          String key, dynamic value, String? sourceModule) =>
      CrossModuleEvent._('data_shared', {
        'key': key,
        'value': value,
        'sourceModule': sourceModule,
      });

  factory CrossModuleEvent.navigationRequested(
    String fromModule,
    String toModule,
    String route,
    Map<String, dynamic>? params,
  ) =>
      CrossModuleEvent._('navigation_requested', {
        'fromModule': fromModule,
        'toModule': toModule,
        'route': route,
        'params': params,
      });

  factory CrossModuleEvent.pluginStateSynced(
          String pluginId, PluginSyncData data) =>
      CrossModuleEvent._('plugin_state_synced', {
        'pluginId': pluginId,
        'data': data.toJson(),
      });

  factory CrossModuleEvent.projectStateSynced(
          String projectId, ProjectSyncData data) =>
      CrossModuleEvent._('project_state_synced', {
        'projectId': projectId,
        'data': data.toJson(),
      });

  factory CrossModuleEvent.custom(
          String eventType, Map<String, dynamic> data, String? sourceModule) =>
      CrossModuleEvent._(eventType, {
        'sourceModule': sourceModule,
        ...data,
      });
}

/// 插件同步数据
class PluginSyncData {
  final String id;
  final String name;
  final String version;
  final bool isInstalled;
  final bool isEnabled;
  final DateTime? lastUpdated;

  const PluginSyncData({
    required this.id,
    required this.name,
    required this.version,
    required this.isInstalled,
    required this.isEnabled,
    this.lastUpdated,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'version': version,
        'isInstalled': isInstalled,
        'isEnabled': isEnabled,
        'lastUpdated': lastUpdated?.toIso8601String(),
      };

  factory PluginSyncData.fromJson(Map<String, dynamic> json) => PluginSyncData(
        id: json['id'] as String,
        name: json['name'] as String,
        version: json['version'] as String,
        isInstalled: json['isInstalled'] as bool,
        isEnabled: json['isEnabled'] as bool,
        lastUpdated: json['lastUpdated'] != null
            ? DateTime.parse(json['lastUpdated'] as String)
            : null,
      );
}

/// 项目同步数据
class ProjectSyncData {
  final String id;
  final String name;
  final String type;
  final String status;
  final DateTime? lastModified;

  const ProjectSyncData({
    required this.id,
    required this.name,
    required this.type,
    required this.status,
    this.lastModified,
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'type': type,
        'status': status,
        'lastModified': lastModified?.toIso8601String(),
      };

  factory ProjectSyncData.fromJson(Map<String, dynamic> json) =>
      ProjectSyncData(
        id: json['id'] as String,
        name: json['name'] as String,
        type: json['type'] as String,
        status: json['status'] as String,
        lastModified: json['lastModified'] != null
            ? DateTime.parse(json['lastModified'] as String)
            : null,
      );
}
