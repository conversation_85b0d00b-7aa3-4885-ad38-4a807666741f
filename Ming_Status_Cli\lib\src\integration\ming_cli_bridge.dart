/*
---------------------------------------------------------------
File name:          ming_cli_bridge.dart
Author:             lgnorant-lu
Date created:       2025-07-29
Last modified:      2025-07-29
Dart Version:       3.2+
Description:        Ming CLI与Pet App V3的集成桥接实现
---------------------------------------------------------------
Change History:
    2025-07-29: Ming CLI与Pet App V3的集成桥接实现;
---------------------------------------------------------------
*/

import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:developer' as developer;

import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;

import '../core/plugin_system/pet_app_bridge.dart';
import '../core/plugin_system/local_registry.dart';
import '../core/plugin_system/plugin_validator.dart';
import '../core/plugin_system/plugin_builder.dart';
import '../utils/logger.dart';

/// Ming CLI 集成桥接器
/// 
/// 负责Ming CLI与Pet App V3之间的真实集成通信，
/// 替代原有的模拟实现，提供实际的插件同步和管理功能。
class MingCliBridge {
  /// 单例实例
  static MingCliBridge? _instance;
  
  /// 获取单例实例
  static MingCliBridge get instance {
    _instance ??= MingCliBridge._();
    return _instance!;
  }
  
  /// 私有构造函数
  MingCliBridge._();

  /// Pet App V3 桥接器
  late final PetAppBridge _petAppBridge;
  
  /// 本地注册表
  late final LocalRegistry _localRegistry;
  
  /// 插件验证器
  late final PluginValidator _validator;
  
  /// 插件构建器
  late final PluginBuilder _builder;
  
  /// HTTP客户端
  late final http.Client _httpClient;
  
  /// 是否已初始化
  bool _initialized = false;
  
  /// Pet App V3 连接配置
  Map<String, dynamic>? _connectionConfig;

  /// 初始化桥接器
  Future<void> initialize() async {
    if (_initialized) {
      developer.log('Ming CLI Bridge 已经初始化', name: 'MingCliBridge');
      return;
    }

    try {
      developer.log('开始初始化 Ming CLI Bridge', name: 'MingCliBridge');

      // 1. 初始化核心组件
      _petAppBridge = PetAppBridge();
      _localRegistry = LocalRegistry();
      _validator = PluginValidator();
      _builder = PluginBuilder();
      _httpClient = http.Client();

      // 2. 加载连接配置
      await _loadConnectionConfig();

      // 3. 测试连接
      await _testConnection();

      _initialized = true;
      developer.log('Ming CLI Bridge 初始化完成', name: 'MingCliBridge');
    } catch (e, stackTrace) {
      developer.log(
        'Ming CLI Bridge 初始化失败: $e',
        name: 'MingCliBridge',
        error: e,
        stackTrace: stackTrace,
      );
      rethrow;
    }
  }

  /// 销毁桥接器
  Future<void> dispose() async {
    try {
      developer.log('销毁 Ming CLI Bridge', name: 'MingCliBridge');
      
      _httpClient.close();
      _initialized = false;
      _connectionConfig = null;
      
      developer.log('Ming CLI Bridge 销毁完成', name: 'MingCliBridge');
    } catch (e, stackTrace) {
      developer.log(
        'Ming CLI Bridge 销毁失败: $e',
        name: 'MingCliBridge',
        error: e,
        stackTrace: stackTrace,
      );
    }
  }

  /// 同步插件到Pet App V3
  /// 
  /// [pluginId] 插件ID
  /// [options] 同步选项
  Future<Map<String, dynamic>> syncPluginToPetApp(
    String pluginId, {
    bool dryRun = false,
    bool verbose = false,
  }) async {
    _ensureInitialized();

    try {
      if (verbose) {
        Logger.info('📤 开始同步插件到Pet App V3: $pluginId');
      }

      // 1. 验证插件存在
      final pluginInfo = await _localRegistry.getPlugin(pluginId);
      if (pluginInfo == null) {
        throw Exception('插件 $pluginId 不存在于本地注册表中');
      }

      // 2. 验证插件兼容性
      final validationResult = await _validatePluginForPetApp(pluginId);
      if (!validationResult['isValid']) {
        throw Exception('插件验证失败: ${validationResult['errors']}');
      }

      if (dryRun) {
        return {
          'success': true,
          'pluginId': pluginId,
          'dryRun': true,
          'message': '预览模式：插件验证通过，可以同步到Pet App V3',
          'validationResult': validationResult,
        };
      }

      // 3. 构建插件包
      final buildResult = await _buildPluginForPetApp(pluginId);
      if (!buildResult['success']) {
        throw Exception('插件构建失败: ${buildResult['error']}');
      }

      // 4. 发送到Pet App V3
      final syncResult = await _performRealSync(pluginId, buildResult['packagePath']);

      if (verbose) {
        Logger.success('✅ 插件同步完成: $pluginId');
      }

      return {
        'success': true,
        'pluginId': pluginId,
        'syncResult': syncResult,
        'buildResult': buildResult,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e, stackTrace) {
      developer.log(
        '同步插件失败: $pluginId, 错误: $e',
        name: 'MingCliBridge',
        error: e,
        stackTrace: stackTrace,
      );
      
      return {
        'success': false,
        'pluginId': pluginId,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 从Pet App V3导入插件
  /// 
  /// [pluginId] 插件ID
  /// [options] 导入选项
  Future<Map<String, dynamic>> importPluginFromPetApp(
    String pluginId, {
    bool dryRun = false,
    bool verbose = false,
  }) async {
    _ensureInitialized();

    try {
      if (verbose) {
        Logger.info('📥 开始从Pet App V3导入插件: $pluginId');
      }

      // 1. 从Pet App V3获取插件信息
      final pluginData = await _fetchPluginFromPetApp(pluginId);

      if (dryRun) {
        return {
          'success': true,
          'pluginId': pluginId,
          'dryRun': true,
          'message': '预览模式：可以从Pet App V3导入插件',
          'pluginData': pluginData,
        };
      }

      // 2. 转换为Ming CLI格式
      final mingFormat = await _convertToMingFormat(pluginData);

      // 3. 导入到本地注册表
      await _importToLocalRegistry(pluginId, mingFormat);

      if (verbose) {
        Logger.success('✅ 插件导入完成: $pluginId');
      }

      return {
        'success': true,
        'pluginId': pluginId,
        'importedData': mingFormat,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e, stackTrace) {
      developer.log(
        '导入插件失败: $pluginId, 错误: $e',
        name: 'MingCliBridge',
        error: e,
        stackTrace: stackTrace,
      );
      
      return {
        'success': false,
        'pluginId': pluginId,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 获取Pet App V3插件列表
  Future<Map<String, dynamic>> getPluginListFromPetApp({
    String? category,
    String? author,
    int limit = 50,
    int offset = 0,
  }) async {
    _ensureInitialized();

    try {
      final queryParams = <String, String>{
        'limit': limit.toString(),
        'offset': offset.toString(),
      };
      
      if (category != null) queryParams['category'] = category;
      if (author != null) queryParams['author'] = author;

      final response = await _makeApiRequest(
        'GET',
        '/api/plugins',
        queryParams: queryParams,
      );

      return {
        'success': true,
        'plugins': response['data'],
        'total': response['total'],
        'limit': limit,
        'offset': offset,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 检查Pet App V3连接状态
  Future<Map<String, dynamic>> checkConnectionStatus() async {
    try {
      if (!_initialized) {
        return {
          'connected': false,
          'error': 'Bridge not initialized',
        };
      }

      final response = await _makeApiRequest('GET', '/api/health');
      
      return {
        'connected': true,
        'status': response['status'],
        'version': response['version'],
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'connected': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// 确保已初始化
  void _ensureInitialized() {
    if (!_initialized) {
      throw StateError('Ming CLI Bridge 未初始化，请先调用 initialize()');
    }
  }

  /// 加载连接配置
  Future<void> _loadConnectionConfig() async {
    // TODO(ming_cli): 从配置文件或环境变量加载Pet App V3连接配置
    // 这里使用默认配置作为示例
    _connectionConfig = {
      'baseUrl': Platform.environment['PET_APP_V3_URL'] ?? 'http://localhost:8080',
      'apiKey': Platform.environment['PET_APP_V3_API_KEY'] ?? 'dev-api-key',
      'timeout': 30000,
      'retryCount': 3,
    };
    
    developer.log('连接配置加载完成: ${_connectionConfig!['baseUrl']}', name: 'MingCliBridge');
  }

  /// 测试连接
  Future<void> _testConnection() async {
    try {
      final response = await _makeApiRequest('GET', '/api/health');
      developer.log('Pet App V3 连接测试成功: ${response['status']}', name: 'MingCliBridge');
    } catch (e) {
      developer.log('Pet App V3 连接测试失败: $e', name: 'MingCliBridge');
      // 连接失败不阻止初始化，允许离线模式
    }
  }

  /// 验证插件Pet App V3兼容性
  Future<Map<String, dynamic>> _validatePluginForPetApp(String pluginId) async {
    // 使用现有的PetAppBridge验证功能
    final pluginInfo = await _localRegistry.getPlugin(pluginId);
    if (pluginInfo == null) {
      throw Exception('插件不存在: $pluginId');
    }

    return await _petAppBridge.validatePetAppCompatibility(pluginInfo['path']);
  }

  /// 构建插件包
  Future<Map<String, dynamic>> _buildPluginForPetApp(String pluginId) async {
    try {
      final pluginInfo = await _localRegistry.getPlugin(pluginId);
      if (pluginInfo == null) {
        throw Exception('插件不存在: $pluginId');
      }

      final buildResult = await _builder.buildPlugin(
        pluginInfo['path'],
        outputDir: path.join(Directory.systemTemp.path, 'ming_builds', pluginId),
        targetPlatform: 'pet_app_v3',
      );

      return buildResult;
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 执行真实同步
  Future<Map<String, dynamic>> _performRealSync(String pluginId, String packagePath) async {
    try {
      // 读取插件包
      final packageFile = File(packagePath);
      final packageBytes = await packageFile.readAsBytes();

      // 创建多部分请求
      final request = http.MultipartRequest(
        'POST',
        Uri.parse('${_connectionConfig!['baseUrl']}/api/plugins/sync'),
      );

      // 添加文件
      request.files.add(http.MultipartFile.fromBytes(
        'package',
        packageBytes,
        filename: '$pluginId.zip',
      ));

      // 添加元数据
      request.fields['pluginId'] = pluginId;
      request.fields['source'] = 'ming_cli';
      request.fields['version'] = '1.0.0';

      // 添加认证头
      request.headers['Authorization'] = 'Bearer ${_connectionConfig!['apiKey']}';

      // 发送请求
      final streamedResponse = await request.send();
      final response = await http.Response.fromStream(streamedResponse);

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'success': true,
          'data': responseData,
        };
      } else {
        throw Exception('同步失败: ${response.statusCode} ${response.body}');
      }
    } catch (e) {
      throw Exception('执行同步失败: $e');
    }
  }

  /// 从Pet App V3获取插件
  Future<Map<String, dynamic>> _fetchPluginFromPetApp(String pluginId) async {
    final response = await _makeApiRequest('GET', '/api/plugins/$pluginId');
    return response['data'];
  }

  /// 转换为Ming格式
  Future<Map<String, dynamic>> _convertToMingFormat(Map<String, dynamic> petAppData) async {
    // TODO(ming_cli): 实现Pet App V3格式到Ming CLI格式的转换
    return {
      'id': petAppData['id'],
      'name': petAppData['name'],
      'version': petAppData['version'],
      'description': petAppData['description'],
      'author': petAppData['author'],
      'source': 'pet_app_v3',
      'importedAt': DateTime.now().toIso8601String(),
    };
  }

  /// 导入到本地注册表
  Future<void> _importToLocalRegistry(String pluginId, Map<String, dynamic> pluginData) async {
    await _localRegistry.addPlugin(pluginId, pluginData);
  }

  /// 发送API请求
  Future<Map<String, dynamic>> _makeApiRequest(
    String method,
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? queryParams,
  }) async {
    final baseUrl = _connectionConfig!['baseUrl'];
    final uri = Uri.parse('$baseUrl$endpoint');
    final finalUri = queryParams != null ? uri.replace(queryParameters: queryParams) : uri;

    final headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ${_connectionConfig!['apiKey']}',
      'User-Agent': 'Ming-CLI/1.0.0',
    };

    http.Response response;
    
    switch (method.toUpperCase()) {
      case 'GET':
        response = await _httpClient.get(finalUri, headers: headers);
        break;
      case 'POST':
        response = await _httpClient.post(
          finalUri,
          headers: headers,
          body: body != null ? jsonEncode(body) : null,
        );
        break;
      case 'PUT':
        response = await _httpClient.put(
          finalUri,
          headers: headers,
          body: body != null ? jsonEncode(body) : null,
        );
        break;
      case 'DELETE':
        response = await _httpClient.delete(finalUri, headers: headers);
        break;
      default:
        throw Exception('不支持的HTTP方法: $method');
    }

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return jsonDecode(response.body);
    } else {
      throw Exception('API请求失败: ${response.statusCode} ${response.body}');
    }
  }
}
