/*
---------------------------------------------------------------
File name:          app_store_page.dart
Author:             lgnorant-lu
Date created:       2025-07-30
Last modified:      2025-07-30
Dart Version:       3.2+
Description:        App Manager应用商店主页面 - 策略A重构阶段3
---------------------------------------------------------------
Change History:
    2025-07-30: 策略A重构 - 从Creative Workshop迁移应用商店功能;
---------------------------------------------------------------
*/

import 'package:flutter/material.dart';
import 'package:plugin_system/plugin_system.dart' as plugin_sys;

import '../../services/app_store_service.dart';
import '../../services/plugin_installation_service.dart';
import 'plugin_card.dart';
import 'plugin_search_bar.dart';
import 'category_filter.dart';

/// 应用商店主页面
/// 
/// 提供插件浏览、搜索、安装等功能
class AppStorePage extends StatefulWidget {
  const AppStorePage({super.key});

  @override
  State<AppStorePage> createState() => _AppStorePageState();
}

class _AppStorePageState extends State<AppStorePage> 
    with SingleTickerProviderStateMixin {
  
  // 服务实例
  late AppStoreService _storeService;
  late PluginInstallationService _installationService;
  
  // 状态管理
  bool _isLoading = true;
  String? _error;
  List<plugin_sys.PluginStoreEntry> _plugins = [];
  List<String> _categories = [];
  String _searchQuery = '';
  String? _selectedCategory;
  
  // UI控制器
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();
  
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  /// 初始化服务
  Future<void> _initializeServices() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      _storeService = AppStoreService.instance;
      _installationService = PluginInstallationService.instance;

      // 确保服务已初始化
      if (!_storeService.isInitialized) {
        await _storeService.initialize();
      }
      if (!_installationService.isInitialized) {
        await _installationService.initialize();
      }

      // 监听服务状态变化
      _storeService.addListener(_onStoreServiceChanged);
      _installationService.addListener(_onInstallationServiceChanged);

      // 加载初始数据
      await _loadInitialData();

    } catch (e) {
      setState(() {
        _error = '初始化失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 加载初始数据
  Future<void> _loadInitialData() async {
    final plugins = await _storeService.searchPlugins('');
    final categories = _storeService.getAvailableCategories();
    
    setState(() {
      _plugins = plugins;
      _categories = categories;
    });
  }

  /// 商店服务状态变化
  void _onStoreServiceChanged() {
    if (mounted) {
      setState(() {
        _plugins = _storeService.filteredPlugins;
        _error = _storeService.error;
      });
    }
  }

  /// 安装服务状态变化
  void _onInstallationServiceChanged() {
    if (mounted) {
      setState(() {
        // 触发UI更新以反映安装状态变化
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('应用商店'),
      elevation: 0,
      backgroundColor: Theme.of(context).colorScheme.surface,
      foregroundColor: Theme.of(context).colorScheme.onSurface,
      actions: [
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: _refreshStore,
          tooltip: '刷新',
        ),
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: _openSettings,
          tooltip: '设置',
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        tabs: const [
          Tab(text: '推荐', icon: Icon(Icons.star)),
          Tab(text: '分类', icon: Icon(Icons.category)),
          Tab(text: '已安装', icon: Icon(Icons.download_done)),
        ],
      ),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return _buildErrorView();
    }

    return Column(
      children: [
        // 搜索栏
        _buildSearchSection(),
        
        // 分类过滤器
        if (_categories.isNotEmpty) _buildCategoryFilter(),
        
        // 内容区域
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildRecommendedTab(),
              _buildCategoriesTab(),
              _buildInstalledTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建错误视图
  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _refreshStore,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建搜索区域
  Widget _buildSearchSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: PluginSearchBar(
        onSearchChanged: _onSearchChanged,
        hintText: '搜索插件...',
      ),
    );
  }

  /// 构建分类过滤器
  Widget _buildCategoryFilter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: CategoryFilter(
        categories: _categories,
        selectedCategory: _selectedCategory,
        onCategoryChanged: _onCategoryChanged,
      ),
    );
  }

  /// 构建推荐标签页
  Widget _buildRecommendedTab() {
    return _buildPluginGrid(_plugins);
  }

  /// 构建分类标签页
  Widget _buildCategoriesTab() {
    return _buildPluginGrid(_plugins);
  }

  /// 构建已安装标签页
  Widget _buildInstalledTab() {
    // TODO(installed-plugins): 实现已安装插件列表
    return const Center(
      child: Text('已安装插件功能即将推出...'),
    );
  }

  /// 构建插件网格
  Widget _buildPluginGrid(List<plugin_sys.PluginStoreEntry> plugins) {
    if (plugins.isEmpty) {
      return const Center(
        child: Text('暂无插件'),
      );
    }

    return GridView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: plugins.length,
      itemBuilder: (context, index) {
        final plugin = plugins[index];
        return PluginCard(
          plugin: plugin,
          onInstall: () => _installPlugin(plugin.id),
          onViewDetails: () => _viewPluginDetails(plugin),
        );
      },
    );
  }

  /// 构建浮动操作按钮
  Widget? _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _refreshStore,
      tooltip: '刷新商店',
      child: const Icon(Icons.refresh),
    );
  }

  /// 搜索变化处理
  Future<void> _onSearchChanged(String query) async {
    setState(() {
      _searchQuery = query;
    });
    
    await _storeService.searchPlugins(query);
  }

  /// 分类变化处理
  Future<void> _onCategoryChanged(String? category) async {
    setState(() {
      _selectedCategory = category;
    });
    
    await _storeService.filterByCategory(category);
  }

  /// 安装插件
  Future<void> _installPlugin(String pluginId) async {
    try {
      final result = await _installationService.installPlugin(pluginId);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              result.isSuccess ? '安装成功' : '安装失败: ${result.message}',
            ),
            backgroundColor: result.isSuccess ? Colors.green : Colors.red,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('安装失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// 查看插件详情
  void _viewPluginDetails(plugin_sys.PluginStoreEntry plugin) {
    // TODO(plugin-details): 实现插件详情页面
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('查看 ${plugin.name} 详情')),
    );
  }

  /// 刷新商店
  Future<void> _refreshStore() async {
    await _storeService.refreshStore();
  }

  /// 打开设置
  void _openSettings() {
    // TODO(store-settings): 实现商店设置页面
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('商店设置功能即将推出...')),
    );
  }
}
